{"number": ["calculation", "computation", "numeric_field", "integer_value", "decimal_value", "float_value", "counter", "accumulator", "aggregation", "sum_total", "average_value", "median_value", "percentile", "quartile", "standard_deviation", "variance", "coefficient", "ratio_value", "proportion_value", "fraction_value", "multiplier", "divisor", "factor", "exponent", "logarithm", "square_root", "power_value", "absolute_value", "relative_value", "normalized_value"], "email": ["email_addr", "mail_addr", "electronic_mail", "e_mail_address", "contact_mail", "business_email", "personal_email", "work_email", "home_email", "primary_email", "secondary_email", "backup_email", "recovery_email", "notification_mail", "alert_email", "system_email", "automated_email", "no_reply_email", "do_not_reply"], "phone": ["tel_no", "phone_no", "contact_no", "mobile_no", "cell_no", "landline", "landline_number", "home_tel", "work_tel", "office_tel", "business_tel", "emergency_tel", "primary_tel", "secondary_tel", "backup_phone", "alternate_phone", "direct_line", "extension", "phone_ext", "tel_ext"], "url": ["web_address", "internet_address", "web_location", "site_url", "page_url", "resource_url", "api_url", "service_url", "endpoint_url", "callback_url", "redirect_url", "return_url", "success_url", "error_url", "cancel_url", "webhook_url", "notification_url", "download_url", "upload_url", "media_url"], "address": ["addr", "postal_addr", "mailing_addr", "street_addr", "home_addr", "work_addr", "office_addr", "business_addr", "billing_addr", "shipping_addr", "delivery_addr", "contact_addr", "permanent_addr", "temporary_addr", "current_addr", "previous_addr", "forwarding_addr", "correspondence_addr", "registered_addr", "legal_addr"], "date": ["dt", "date_value", "calendar_date", "event_date", "occurrence_date", "scheduled_date", "planned_date", "target_date", "expected_date", "actual_date", "recorded_date", "registered_date", "filed_date", "submitted_date", "received_date", "processed_date", "approved_date", "rejected_date", "cancelled_date", "postponed_date"], "datetime": ["dt_time", "date_time", "timestamp_value", "time_stamp_value", "event_timestamp", "log_timestamp", "audit_timestamp", "system_timestamp", "user_timestamp", "server_timestamp", "client_timestamp", "utc_timestamp", "local_timestamp", "created_timestamp", "updated_timestamp", "modified_timestamp", "accessed_timestamp", "viewed_timestamp", "clicked_timestamp", "downloaded_timestamp"], "time": ["time_value", "clock_time", "wall_time", "system_time", "local_time", "utc_time", "timezone_time", "elapsed_time", "execution_time", "processing_time", "response_time", "wait_time", "delay_time", "timeout_value", "interval_time", "duration_value", "period_value", "cycle_time", "frequency_time", "schedule_time"], "boolean": ["bool_value", "flag_value", "switch_value", "toggle_value", "checkbox_value", "radio_value", "option_flag", "setting_flag", "config_flag", "feature_flag", "debug_flag", "test_flag", "production_flag", "development_flag", "staging_flag", "maintenance_flag", "emergency_flag", "critical_flag", "warning_flag", "info_flag"], "currency": ["monetary_value", "financial_value", "economic_value", "commercial_value", "business_value", "trade_value", "market_value", "fair_value", "book_value", "net_value", "gross_value", "taxable_value", "non_taxable_value", "before_tax", "after_tax", "pre_tax", "post_tax", "inclusive_tax", "exclusive_tax", "with_tax"], "percentage": ["percent_value", "pct_value", "rate_percent", "ratio_percent", "proportion_percent", "share_percent", "portion_percent", "fraction_percent", "part_percent", "percentage_value", "completion_percent", "progress_percent", "success_rate", "failure_rate", "error_rate", "accuracy_rate", "efficiency_rate", "performance_rate", "utilization_rate", "conversion_rate"], "text": ["text_value", "string_value", "char_value", "varchar_value", "content_text", "message_text", "description_text", "comment_text", "note_text", "remark_text", "observation_text", "feedback_text", "review_text", "summary_text", "abstract_text", "excerpt_text", "snippet_text", "fragment_text", "sample_text", "example_text"], "textarea": ["long_text", "multiline_text", "paragraph_text", "essay_text", "article_text", "document_text", "report_text", "story_text", "narrative_text", "biography_text", "profile_text", "about_text", "introduction_text", "conclusion_text", "explanation_text", "instruction_text", "specification_text", "requirement_text", "terms_text", "conditions_text"], "guid": ["uuid_value", "identifier_value", "unique_id", "record_id", "entity_id", "object_id", "item_id", "reference_id", "foreign_key", "primary_key", "composite_key", "natural_key", "surrogate_key", "business_key", "technical_key", "system_id", "external_id", "internal_id", "correlation_id", "transaction_id"], "rating": ["user_rating", "customer_rating", "product_rating", "service_rating", "quality_rating", "performance_rating", "satisfaction_rating", "feedback_rating", "review_rating", "star_rating", "five_star", "rating_scale", "evaluation_score", "assessment_score", "grade_score", "mark_score", "points_score", "ranking_score"], "day": ["day_of_week", "weekday_name", "business_day", "working_day", "calendar_day", "day_abbreviation", "weekday_number", "day_index", "day_code", "day_short"], "color": ["hex_value", "rgb_value", "rgba_value", "hsl_value", "hsla_value", "color_hex", "color_rgb", "color_hsl", "background_colour", "foreground_colour", "text_colour", "border_colour", "theme_colour", "brand_colour", "accent_colour"], "multiselect": ["multi_choice", "multiple_selection", "checkbox_group", "option_group", "choice_group", "selection_group", "multi_option", "multiple_option", "tags_field", "categories_field", "features_field", "attributes_field"], "month": ["month_value", "calendar_month", "fiscal_month", "month_abbreviation", "month_short", "month_code", "month_index", "month_number"], "select": ["dropdown_list", "combo_list", "pick_list", "option_menu", "choice_menu", "selection_menu", "single_option", "exclusive_option", "category_dropdown", "status_dropdown", "type_dropdown"], "file": ["file_field", "upload_field", "attachment_field", "document_field", "media_field", "asset_field", "binary_field", "file_input", "upload_input", "attachment_input"], "richtext": ["rich_editor", "html_editor", "text_editor", "content_editor", "wysiwyg_editor", "formatted_editor", "markup_editor", "styled_editor", "article_editor", "blog_editor"], "image": ["image_field", "picture_field", "photo_field", "avatar_field", "thumbnail_field", "banner_field", "logo_field", "icon_field", "graphic_field", "illustration_field"], "year": ["year_field", "year_input", "calendar_year", "fiscal_year", "academic_year", "model_year", "vintage_year", "birth_year", "graduation_year", "publication_year"], "radio": ["radio_group", "radio_list", "radio_options", "exclusive_choice", "single_choice", "either_or", "yes_no_choice", "true_false_choice", "gender_choice", "status_choice"], "tag": ["tag_field", "tags_input", "keyword_field", "label_field", "category_tags", "topic_tags", "hashtag_field", "chip_field", "badge_field", "classification_field"], "slider": ["range_input", "scale_input", "level_input", "intensity_input", "volume_input", "brightness_input", "opacity_input", "zoom_input", "progress_input", "completion_input"], "checkbox": ["checkbox_input", "check_input", "boolean_input", "flag_input", "switch_input", "toggle_input", "on_off_input", "yes_no_input", "true_false_input", "enabled_disabled"]}