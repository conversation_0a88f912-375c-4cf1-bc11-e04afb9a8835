using Abstraction.Database.Repositories;
using Application.ComprehensiveEntityData.DTOs;
using Application.DataTransformation.DTOs;
using Application.FieldMappings.Specifications;
using Application.Objects.Specifications;
using DocumentFormat.OpenXml.InkML;
using Domain.Common.Contracts;
using Domain.Entities;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;
using System.Text.Json;

namespace Application.DataTransformation.Services;

/// <summary>
/// Service for transforming incoming JSON data according to field mappings
/// </summary>
public class DataTransformationService : IDataTransformationService
{
    private readonly IRepository<FieldMapping> _fieldMappingRepository;
    private readonly IRepository<ObjectValue> _objectValueRepository;
    private readonly IRepository<ObjectMetadata> _objectMetadataRepository;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly ILogger<DataTransformationService> _logger;

    public DataTransformationService(
        IRepository<FieldMapping> fieldMappingRepository,
        IRepository<ObjectValue> objectValueRepository,
        IRepository<ObjectMetadata> objectMetadataRepository,
        IRepository<Domain.Entities.Object> objectRepository,
        ILogger<DataTransformationService> logger)
    {
        _fieldMappingRepository = fieldMappingRepository;
        _objectValueRepository = objectValueRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _objectRepository = objectRepository;
        _logger = logger;
    }

    /// <summary>
    /// Transform incoming JSON data according to field mappings for a specific API
    /// </summary>
    public async Task<Result<DataTransformationResultDto>> TransformDataAsync(
        string apiName,
        string jsonData,
        string tenantId,
        Guid? userId = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting data transformation for API: {ApiName}", apiName);

            // Validate inputs - early return on failure
            ValidateInputs(apiName, jsonData, tenantId);

            // Parse JSON - early return on failure
            var jsonDocument = ParseJsonData(jsonData, apiName);

            // Get field mappings - early return on failure
            var fieldMappings = await GetFieldMappingsByApiNameAsync(apiName, cancellationToken);
            if (!fieldMappings.Any())
            {
                throw new InvalidOperationException($"No field mappings found for API: {apiName}");
            }

            // Generate reference ID for grouping
            var refId = Guid.NewGuid();

            // Process and validate all field mappings using LINQ
            var processedFields = await ProcessFieldMappingsAsync(fieldMappings, jsonDocument.RootElement, refId, userId, cancellationToken);

            // Batch insert all ObjectValue records
            var insertedObjectValues = await BatchInsertObjectValuesAsync(processedFields, cancellationToken);

            stopwatch.Stop();

            var result = new DataTransformationResultDto
            {
                Success = true,
                Message = $"Successfully transformed and inserted {insertedObjectValues.Count} ObjectValue records",
                RefId = refId,
                ObjectValuesCreated = insertedObjectValues.Count,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };

            _logger.LogInformation(
                "Data transformation completed successfully for API: {ApiName}. Created: {Created}, Time: {Time}ms",
                apiName, result.ObjectValuesCreated, result.ProcessingTimeMs);

            return Result<DataTransformationResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Data transformation failed for API: {ApiName}", apiName);

            var result = new DataTransformationResultDto
            {
                Success = false,
                Message = ex.Message,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds
            };

            return Result<DataTransformationResultDto>.Success(result);
        }
    }

    /// <summary>
    /// Validate input parameters
    /// </summary>
    private static void ValidateInputs(string apiName, string jsonData, string tenantId)
    {
        if (string.IsNullOrWhiteSpace(apiName))
            throw new ArgumentException("API name is required");

        if (string.IsNullOrWhiteSpace(jsonData))
            throw new ArgumentException("JSON data is required");

        if (string.IsNullOrWhiteSpace(tenantId))
            throw new ArgumentException("Tenant ID is required");
    }

    /// <summary>
    /// Parse JSON data with error handling
    /// </summary>
    private static JsonDocument ParseJsonData(string jsonData, string apiName)
    {
        try
        {
            return JsonDocument.Parse(jsonData);
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Invalid JSON data for API '{apiName}': {ex.Message}");
        }
    }

    /// <summary>
    /// Get field mappings by API name
    /// </summary>
    private async Task<List<FieldMapping>> GetFieldMappingsByApiNameAsync(string apiName, CancellationToken cancellationToken)
    {
        var spec = new FieldMappingByApiNameSpec(apiName);
        return await _fieldMappingRepository.ListAsync(spec, cancellationToken);
    }

    /// <summary>
    /// Process all field mappings using LINQ operations
    /// </summary>
    private async Task<List<FieldProcessingResult>> ProcessFieldMappingsAsync(
        List<FieldMapping> fieldMappings,
        JsonElement jsonElement,
        Guid refId,
        Guid? userId,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing {Count} field mappings", fieldMappings.Count);

        // Validate all field mappings have required data
        var invalidMappings = fieldMappings.Where(fm => !fm.ObjectMetadataId.HasValue || string.IsNullOrEmpty(fm.TargetObjectName)).ToList();
        if (invalidMappings.Any())
        {
            var invalidFields = string.Join(", ", invalidMappings.Select(fm => fm.SourceField));
            throw new InvalidOperationException($"Invalid field mappings found (missing ObjectMetadataId or TargetObjectName): {invalidFields}");
        }

        // Get all unique target object names for batch lookup
        var targetObjectNames = fieldMappings.Select(fm => fm.TargetObjectName).Distinct().ToList();
        var objectLookup = await GetObjectLookupAsync(targetObjectNames, cancellationToken);

        // Process all field mappings using LINQ
        var results = fieldMappings.Select(mapping => ProcessSingleFieldMapping(mapping, jsonElement, objectLookup?.FirstOrDefault().Value, refId, userId))
                                  .Where(result => result != null)
                                  .ToList();

        // Check if any processing failed
        if (results.Count != fieldMappings.Count)
        {
            var failedCount = fieldMappings.Count - results.Count;
            throw new InvalidOperationException($"{failedCount} field mapping(s) failed processing");
        }

        _logger.LogInformation("Successfully processed {Count} field mappings", results.Count);
        return results!;
    }

    /// <summary>
    /// Get object lookup dictionary for batch processing
    /// </summary>
    private async Task<Dictionary<string, Guid>> GetObjectLookupAsync(List<string> targetObjectNames, CancellationToken cancellationToken)
    {
        var objectLookup = new Dictionary<string, Guid>();

        foreach (var objectName in targetObjectNames)
        {
            var objectSpec = new ObjectByNameSpec(objectName);
            var obj = await _objectRepository.FirstOrDefaultAsync(objectSpec, cancellationToken);

            if (obj == null)
            {
                throw new InvalidOperationException($"Target object '{objectName}' not found");
            }
            if (obj.ParentObject != null)
            {
                objectLookup[obj.ParentObject?.Name ?? string.Empty] = obj.ParentObject?.Id ?? Guid.Empty;
            }
        }

        return objectLookup;
    }

    /// <summary>
    /// Process a single field mapping
    /// </summary>
    private FieldProcessingResult? ProcessSingleFieldMapping(
        FieldMapping mapping,
        JsonElement jsonElement,
        Guid? parentObjId,
        Guid refId,
        Guid? userId)
    {
        try
        {
            // Extract value from JSON
            var sourceValue = ExtractValueFromJson(jsonElement, mapping.SourceField);

            // Convert value according to source type
            var (convertedValue, errorMessage) = DataTypeConversionService.ConvertValue(sourceValue, mapping.SourceType);

            if (errorMessage != null)
            {
                throw new InvalidOperationException($"Data conversion failed for field '{mapping.SourceField}': {errorMessage}");
            }

            return new FieldProcessingResult
            {
                ObjectMetadataId = mapping.ObjectMetadataId!.Value,
                TransformedValue = convertedValue,
                ParentObjectId = parentObjId != Guid.Empty ? parentObjId : null,
                SourceField = mapping.SourceField,
                RefId = refId,
                UserId = userId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process field mapping: {SourceField}", mapping.SourceField);
            throw new InvalidOperationException($"Field '{mapping.SourceField}' processing failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Batch insert ObjectValue records
    /// </summary>
    private async Task<List<ObjectValue>> BatchInsertObjectValuesAsync(
        List<FieldProcessingResult> processedFields,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Batch inserting {Count} ObjectValue records", processedFields.Count);

        // Create ObjectValue entities using LINQ
        var objectValuesToInsert = processedFields.Select(field => new ObjectValue
        {
            ObjectMetadataId = field.ObjectMetadataId,
            RefId = field.RefId,
            Value = field.TransformedValue,
            ParentObjectValueId = field.ParentObjectId,
            // IsRefId and IsUserId properties removed from ObjectValue entity
            CreatedBy = field.UserId,
            ModifiedBy = field.UserId
        }).ToList();

        // Batch insert in single transaction
        var insertedObjectValues = await _objectValueRepository.AddRangeAsync(objectValuesToInsert, cancellationToken);
        var insertedList = insertedObjectValues.ToList();

        _logger.LogInformation("Successfully inserted {Count} ObjectValue records", insertedList.Count);
        return insertedList;
    }



    /// <summary>
    /// Extract value from JSON using field path (supports nested paths, arrays, and complex structures)
    /// Supports:
    /// - Simple paths: "name", "email"
    /// - Nested paths: "user.name", "booking.date"
    /// - Array indexing: "bookedDetails[0].bookedDate"
    /// - Array field extraction: "bookedDetails[].bookedDate" (gets first item)
    /// - Array as value: "projectsList" (returns entire array)
    /// </summary>
    private object? ExtractValueFromJson(JsonElement jsonElement, string fieldPath)
    {
        try
        {
            _logger.LogDebug("Extracting value for field path: {FieldPath}", fieldPath);

            var currentElement = jsonElement;
            var pathParts = ParseFieldPath(fieldPath);

            foreach (var part in pathParts)
            {
                currentElement = NavigateJsonPath(currentElement, part);
                if (currentElement.ValueKind == JsonValueKind.Undefined)
                {
                    _logger.LogDebug("Path not found at part: {Part} in path: {FieldPath}", part.PropertyName, fieldPath);
                    return null;
                }
            }

            var result = ExtractJsonValue(currentElement);
            _logger.LogDebug("Successfully extracted value for path {FieldPath}: {Value}", fieldPath, result?.ToString() ?? "null");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract value for field path: {FieldPath}", fieldPath);
            return null;
        }
    }

    /// <summary>
    /// Parse field path into components handling array notation
    /// </summary>
    private List<PathComponent> ParseFieldPath(string fieldPath)
    {
        var components = new List<PathComponent>();
        var parts = fieldPath.Split('.');

        foreach (var part in parts)
        {
            if (part.Contains('[') && part.Contains(']'))
            {
                // Handle array notation like "bookedDetails[0]" or "bookedDetails[]"
                var propertyName = part.Substring(0, part.IndexOf('['));
                var indexPart = part.Substring(part.IndexOf('[') + 1, part.IndexOf(']') - part.IndexOf('[') - 1);

                if (string.IsNullOrEmpty(indexPart))
                {
                    // Empty brackets [] means get first item
                    components.Add(new PathComponent { PropertyName = propertyName, IsArray = true, ArrayIndex = 0 });
                }
                else if (int.TryParse(indexPart, out int index))
                {
                    // Specific index
                    components.Add(new PathComponent { PropertyName = propertyName, IsArray = true, ArrayIndex = index });
                }
                else
                {
                    // Invalid array notation, treat as regular property
                    components.Add(new PathComponent { PropertyName = part, IsArray = false });
                }
            }
            else
            {
                // Regular property
                components.Add(new PathComponent { PropertyName = part, IsArray = false });
            }
        }

        return components;
    }

    /// <summary>
    /// Navigate through a single path component
    /// </summary>
    private JsonElement NavigateJsonPath(JsonElement currentElement, PathComponent component)
    {
        try
        {
            if (currentElement.ValueKind == JsonValueKind.Object && currentElement.TryGetProperty(component.PropertyName, out var property))
            {
                if (component.IsArray && property.ValueKind == JsonValueKind.Array)
                {
                    var array = property.EnumerateArray().ToArray();
                    if (component.ArrayIndex < array.Length)
                    {
                        return array[component.ArrayIndex];
                    }
                    else
                    {
                        _logger.LogDebug("Array index {Index} out of bounds for property {Property} (length: {Length})",
                            component.ArrayIndex, component.PropertyName, array.Length);
                        return default; // JsonValueKind.Undefined
                    }
                }
                else
                {
                    return property;
                }
            }
            else
            {
                _logger.LogDebug("Property {Property} not found in JSON object", component.PropertyName);
                return default; // JsonValueKind.Undefined
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error navigating path component: {Component}", component.PropertyName);
            return default;
        }
    }

    /// <summary>
    /// Extract the final value from JsonElement
    /// </summary>
    private object? ExtractJsonValue(JsonElement element)
    {
        return element.ValueKind switch
        {
            JsonValueKind.String => element.GetString(),
            JsonValueKind.Number => element.GetDecimal(),
            JsonValueKind.True => true,
            JsonValueKind.False => false,
            JsonValueKind.Null => null,
            JsonValueKind.Array => element.GetRawText(), // Return array as JSON string
            JsonValueKind.Object => element.GetRawText(), // Return object as JSON string
            _ => element.GetRawText()
        };
    }

    /// <summary>
    /// Path component for parsing field paths
    /// </summary>
    private class PathComponent
    {
        public string PropertyName { get; set; } = string.Empty;
        public bool IsArray { get; set; }
        public int ArrayIndex { get; set; }
    }
}
