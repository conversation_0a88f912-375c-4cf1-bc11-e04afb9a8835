# Context-Based Field Detection System

## ✅ **Context-Based Field Detection Implementation Complete**

### **🎯 Overview:**

The Context-Based Field Detection System automatically identifies fields that should use Context table relationships instead of regular data types. When field names like "Currency", "Phone", "Mobile", "PhoneNumber", "MobileNumber", "Country" are detected in metaJson, the system creates relationships with the Context table for lookup functionality.

### **🔧 Enhanced Services:**

#### **1. NestedFeatureCreationService**
- **Enhanced Method**: `CreateOrGetMetadataAsync`
- **New Functionality**: Detects context-based fields and creates Context relationships
- **Context Creation**: Automatically creates Context records for lookup categories

#### **2. DynamicProductOperationCommandHandler** 
- **Enhanced Method**: `GetOrCreateMetadata`
- **New Functionality**: Integrates context detection with metadata creation
- **Dynamic Context Management**: Creates contexts on-demand during bulk operations

### **📊 Context-Based Field Patterns:**

#### **Automatically Detected Fields:**
```
Currency Fields:
- "currency", "currencycode", "currencytype"

Phone Fields:
- "phone", "mobile", "phonenumber", "mobilenumber"
- "telephone", "tel", "cell", "cellular"

Location Fields:
- "country", "state", "province", "region", "city"
- "countrycode", "regioncode", "areacode"

Communication Fields:
- "timezone", "language", "locale", "nationality"
- "languagecode", "localecode", "dialcode"
```

#### **Context Mapping:**
```csharp
Field Pattern → Context Name
"currency" → "Currencies"
"phone" → "PhoneTypes"
"mobile" → "PhoneTypes"
"country" → "Countries"
"state" → "States"
"province" → "Provinces"
"region" → "Regions"
"city" → "Cities"
"timezone" → "TimeZones"
"language" → "Languages"
"locale" → "Locales"
"nationality" → "Nationalities"
```

### **🚀 Implementation Details:**

#### **1. Context Detection Logic:**
```csharp
// Check if field should use Context relationships
var isContextField = DataTypeSynonymMappings.IsContextBasedField(fieldName);

// Get suggested context name
var suggestedContextName = DataTypeSynonymMappings.GetSuggestedContextName(fieldName);

// Create or find context
var contextId = await GetOrCreateContextAsync(suggestedContextName, cancellationToken);
```

#### **2. Metadata Enhancement:**
```csharp
// Enhanced metadata creation with context relationships
var metadata = new Metadata
{
    MetadataKey = fieldName,
    DataTypeId = dataTypeId,
    DisplayLabel = FormatDisplayLabel(fieldName),
    // Context relationship properties
    OverrideLookupType = isContextField ? "master" : null,
    OverrideMasterContextId = contextId
};
```

#### **3. Automatic Context Creation:**
```csharp
// Auto-generated context structure
var context = new Context
{
    Name = contextName,
    Description = $"Auto-generated context for {contextName} lookups",
    Category = "AutoGenerated",
    IsActive = true,
    IsDeleted = false
};
```

### **🔄 Processing Flow:**

#### **1. Field Detection:**
```
MetaJson Property → Context Detection → Context Creation → Metadata Enhancement
```

#### **2. Example Processing:**
```json
// Input MetaJson
{
  "Currency": "USD",
  "PhoneNumber": "+1234567890",
  "Country": "United States"
}

// Automatic Processing:
// 1. "Currency" → Creates "Currencies" context
// 2. "PhoneNumber" → Creates "PhoneTypes" context  
// 3. "Country" → Creates "Countries" context

// Result: Metadata records with Context relationships
```

### **📈 Benefits:**

#### **1. Intelligent Lookup Management:**
- **Automatic Context Creation**: No manual setup required
- **Standardized Lookups**: Consistent lookup categories across the system
- **Dynamic Expansion**: New context types added automatically

#### **2. Enhanced Data Relationships:**
- **Proper Normalization**: Lookup data stored in dedicated Context/Lookup tables
- **Referential Integrity**: Foreign key relationships ensure data consistency
- **Scalable Architecture**: Supports unlimited lookup categories

#### **3. Improved User Experience:**
- **Dynamic Dropdowns**: Fields automatically become dropdown selects
- **Consistent Data Entry**: Standardized options for common fields
- **Reduced Errors**: Prevents invalid data entry through controlled lists

### **🔧 Configuration:**

#### **Context-Based Field Patterns:**
```csharp
private static readonly FrozenSet<string> _contextBasedFields = new[]
{
    "currency", "phone", "mobile", "phonenumber", "mobilenumber", "country",
    "state", "province", "region", "city", "timezone", "language", "locale",
    "nationality", "countrycode", "currencycode", "phonecode", "areacode",
    "dialcode", "isocode", "languagecode", "localecode", "regioncode"
}.ToFrozenSet(StringComparer.OrdinalIgnoreCase);
```

#### **Context Name Mappings:**
```csharp
var contextMappings = new Dictionary<string, string>
{
    ["currency"] = "Currencies",
    ["phone"] = "PhoneTypes", 
    ["mobile"] = "PhoneTypes",
    ["phonenumber"] = "PhoneTypes",
    ["mobilenumber"] = "PhoneTypes",
    ["country"] = "Countries",
    ["state"] = "States",
    ["province"] = "Provinces", 
    ["region"] = "Regions",
    ["city"] = "Cities",
    ["timezone"] = "TimeZones",
    ["language"] = "Languages",
    ["locale"] = "Locales",
    ["nationality"] = "Nationalities"
};
```

### **🧪 Usage Examples:**

#### **1. Automatic Context Detection:**
```csharp
// These field names automatically create Context relationships:
"Currency" → Creates "Currencies" context + lookup relationship
"PhoneNumber" → Creates "PhoneTypes" context + lookup relationship
"Country" → Creates "Countries" context + lookup relationship
"MobileNumber" → Creates "PhoneTypes" context + lookup relationship
"CurrencyCode" → Creates "Currencies" context + lookup relationship
```

#### **2. MetaJson Processing:**
```json
// Input
{
  "ProductName": "Sample Product",
  "Currency": "USD",
  "PhoneNumber": "+1234567890",
  "Country": "United States",
  "Description": "Product description"
}

// Processing Result:
// - "ProductName" → Regular text field
// - "Currency" → Select field with Currencies context
// - "PhoneNumber" → Select field with PhoneTypes context
// - "Country" → Select field with Countries context
// - "Description" → Regular textarea field
```

### **🎯 Integration Points:**

#### **1. Services Updated:**
- ✅ `NestedFeatureCreationService.cs` - Enhanced metadata creation
- ✅ `DynamicProductOperationCommandHandler.cs` - Enhanced bulk operations
- ✅ `ObjectUpsertService.cs` - Inherits enhancements through NestedFeatureCreationService

#### **2. Synonym Mapping Integration:**
- ✅ `DataTypeSynonymMappings.cs` - Added context detection methods
- ✅ Context field patterns and mapping logic
- ✅ Suggested context name generation

#### **3. Database Integration:**
- ✅ Context table relationships
- ✅ Metadata override properties
- ✅ Lookup system integration

### **🎉 Implementation Complete!**

The Context-Based Field Detection System is now fully integrated and provides:
- ✅ **Automatic Context Detection** for common lookup fields
- ✅ **Dynamic Context Creation** during metadata processing
- ✅ **Enhanced Metadata Relationships** with Context table
- ✅ **Intelligent Field Processing** in both services
- ✅ **Scalable Lookup Management** for future expansion

The system automatically transforms fields like "Currency", "Phone", "Country" into proper lookup relationships, providing a more structured and user-friendly data management experience! 🚀
