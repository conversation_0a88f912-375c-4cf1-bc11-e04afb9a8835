using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Lookup entity
/// </summary>
public class LookupConfig : IEntityTypeConfiguration<Lookup>
{
    public void Configure(EntityTypeBuilder<Lookup> builder)
    {
        builder.ToTable("Lookups", "Genp");

        // Properties
        builder.Property(e => e.Value)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.IsDefault)
            .HasDefaultValue(false);

        builder.Property(e => e.Value1)
            .HasMaxLength(255);

        builder.Property(e => e.Value2)
            .HasMaxLength(255);

        builder.Property(e => e.ShowSequence)
            .HasDefaultValue(0);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(e => e.ContextId)
            .HasDatabaseName("IX_Lookups_ContextId");

        builder.HasIndex(e => e.Value)
            .HasDatabaseName("IX_Lookups_Value");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Lookups_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.ShowSequence)
            .HasDatabaseName("IX_Lookups_ShowSequence");

        // Relationships
        builder.HasOne(e => e.Context)
            .WithMany(e => e.Lookups)
            .HasForeignKey(e => e.ContextId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
