using System.ComponentModel.DataAnnotations;

namespace Application.Objects.DTOs;

/// <summary>
/// DTO representing a hierarchical object with metadata
/// </summary>
public class HierarchicalObjectDto
{
    /// <summary>
    /// Object name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Metadata schema definition (property names and their characteristics)
    /// </summary>
    public Dictionary<string, object?>? Meta<PERSON><PERSON> { get; set; }

    /// <summary>
    /// Instance values for this object type
    /// </summary>
    public List<Dictionary<string, object?>>? MetaValues { get; set; }

    /// <summary>
    /// Child objects in the hierarchy
    /// </summary>
    public List<HierarchicalObjectDto>? ChildObjects { get; set; }
}

/// <summary>
/// Result of hierarchical object creation
/// </summary>
public class HierarchicalObjectCreationResult
{
    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Summary message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Total number of objects created
    /// </summary>
    public int TotalObjectsCreated { get; set; }

    /// <summary>
    /// Total number of metadata entries created
    /// </summary>
    public int TotalMetadataCreated { get; set; }

    /// <summary>
    /// Total number of object-metadata links created
    /// </summary>
    public int TotalObjectMetadataCreated { get; set; }

    /// <summary>
    /// Total number of object values created
    /// </summary>
    public int TotalObjectValuesCreated { get; set; }

    /// <summary>
    /// List of created objects with their hierarchy
    /// </summary>
    public List<CreatedObjectInfo> CreatedObjects { get; set; } = new();

    /// <summary>
    /// Any validation errors or warnings
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Processing warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}

/// <summary>
/// Information about a created object
/// </summary>
public class CreatedObjectInfo
{
    /// <summary>
    /// Created object ID
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Parent object ID (if any)
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Hierarchy level (0 = root)
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// Number of metadata fields created for this object
    /// </summary>
    public int MetadataFieldsCount { get; set; }

    /// <summary>
    /// Number of instance values created for this object
    /// </summary>
    public int InstanceValuesCount { get; set; }

    /// <summary>
    /// Child objects created under this object
    /// </summary>
    public List<CreatedObjectInfo> Children { get; set; } = new();
}

/// <summary>
/// Result of hierarchical object validation
/// </summary>
public class HierarchicalObjectValidationResult
{
    /// <summary>
    /// Whether the validation passed
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Total number of objects to be created
    /// </summary>
    public int TotalObjects { get; set; }

    /// <summary>
    /// Maximum hierarchy depth
    /// </summary>
    public int MaxDepth { get; set; }

    /// <summary>
    /// Estimated number of metadata fields
    /// </summary>
    public int EstimatedMetadataFields { get; set; }

    /// <summary>
    /// Estimated number of values
    /// </summary>
    public int EstimatedValues { get; set; }

    /// <summary>
    /// Validation warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Processing context to track created entities and state
/// </summary>
internal class ProcessingContext
{
    public Guid ProductId { get; set; }
    public Dictionary<string, Domain.Entities.Metadata> AllMetadata { get; set; } = new();
    public Dictionary<string, Domain.Entities.DataType> AllDataTypes { get; set; } = new();
    public List<Domain.Entities.Object> CreatedObjects { get; set; } = new();
    public List<Domain.Entities.Metadata> CreatedMetadata { get; set; } = new();
    public List<Domain.Entities.ObjectMetadata> CreatedObjectMetadata { get; set; } = new();
    public List<Domain.Entities.ObjectValue> CreatedObjectValues { get; set; } = new();
    public int DatabaseQueriesCount { get; set; }
}
