using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;

namespace Application.Common.DataType;

/// <summary>
/// Configuration-based extension for the Data Type Synonym Mapping System
/// Allows loading additional synonyms from configuration files or external sources
/// </summary>
public class DataTypeSynonymConfiguration
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<DataTypeSynonymConfiguration> _logger;

    /// <summary>
    /// Configuration section name for synonym mappings
    /// </summary>
    public const string ConfigurationSectionName = "DataTypeSynonymMappings";

    /// <summary>
    /// Constructor
    /// </summary>
    public DataTypeSynonymConfiguration(
        IConfiguration configuration,
        ILogger<DataTypeSynonymConfiguration> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Load additional synonyms from configuration
    /// Expected configuration format:
    /// {
    ///   "DataTypeSynonymMappings": {
    ///     "number": ["custom_number_field", "numeric_value"],
    ///     "email": ["custom_email_field", "mail_address"],
    ///     "phone": ["custom_phone_field", "tel_number"]
    ///   }
    /// }
    /// </summary>
    public async Task<bool> LoadAdditionalSynonymsFromConfigurationAsync()
    {
        try
        {
            var synonymSection = _configuration.GetSection(ConfigurationSectionName);
            if (!synonymSection.Exists())
            {
                _logger.LogInformation("No additional synonym mappings found in configuration");
                return true;
            }

            var synonymMappings = synonymSection.Get<Dictionary<string, string[]>>();
            if (synonymMappings == null || !synonymMappings.Any())
            {
                _logger.LogInformation("No valid synonym mappings found in configuration");
                return true;
            }

            var addedCount = 0;
            foreach (var (dataType, synonyms) in synonymMappings)
            {
                if (synonyms != null && synonyms.Length > 0)
                {
                    var success = DataTypeSynonymMappings.TryAddSynonyms(dataType, synonyms);
                    if (success)
                    {
                        addedCount += synonyms.Length;
                        _logger.LogInformation("Added {Count} synonyms for data type '{DataType}': {Synonyms}",
                            synonyms.Length, dataType, string.Join(", ", synonyms));
                    }
                    else
                    {
                        _logger.LogWarning("Failed to add synonyms for data type '{DataType}'", dataType);
                    }
                }
            }

            _logger.LogInformation("Successfully loaded {Count} additional synonyms from configuration", addedCount);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading additional synonyms from configuration");
            return false;
        }
    }

    /// <summary>
    /// Load synonyms from a JSON file
    /// </summary>
    public async Task<bool> LoadSynonymsFromJsonFileAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("Synonym mapping file not found: {FilePath}", filePath);
                return false;
            }

            var jsonContent = await File.ReadAllTextAsync(filePath);
            var synonymMappings = JsonSerializer.Deserialize<Dictionary<string, string[]>>(jsonContent);

            if (synonymMappings == null || !synonymMappings.Any())
            {
                _logger.LogWarning("No valid synonym mappings found in file: {FilePath}", filePath);
                return false;
            }

            var addedCount = 0;
            foreach (var (dataType, synonyms) in synonymMappings)
            {
                if (synonyms != null && synonyms.Length > 0)
                {
                    var success = DataTypeSynonymMappings.TryAddSynonyms(dataType, synonyms);
                    if (success)
                    {
                        addedCount += synonyms.Length;
                        _logger.LogInformation("Added {Count} synonyms for data type '{DataType}' from file",
                            synonyms.Length, dataType);
                    }
                }
            }

            _logger.LogInformation("Successfully loaded {Count} synonyms from file: {FilePath}", addedCount, filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading synonyms from JSON file: {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// Export current synonym mappings to JSON file for backup or sharing
    /// </summary>
    public async Task<bool> ExportSynonymsToJsonFileAsync(string filePath)
    {
        try
        {
            var synonymMappings = new Dictionary<string, string[]>();

            foreach (var dataType in DataTypeSynonymMappings.GetSupportedDataTypes())
            {
                var synonyms = DataTypeSynonymMappings.GetSynonymsForDataType(dataType);
                if (synonyms.Count > 0)
                {
                    synonymMappings[dataType] = synonyms.ToArray();
                }
            }

            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var jsonContent = JsonSerializer.Serialize(synonymMappings, jsonOptions);
            await File.WriteAllTextAsync(filePath, jsonContent);

            _logger.LogInformation("Successfully exported synonym mappings to file: {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting synonyms to JSON file: {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// Validate synonym mappings for potential conflicts or issues
    /// </summary>
    public ValidationResult ValidateSynonymMappings()
    {
        var result = new ValidationResult();

        try
        {
            var allSynonyms = new Dictionary<string, List<string>>();

            // Collect all synonyms across data types
            foreach (var dataType in DataTypeSynonymMappings.GetSupportedDataTypes())
            {
                var synonyms = DataTypeSynonymMappings.GetSynonymsForDataType(dataType);
                foreach (var synonym in synonyms)
                {
                    if (!allSynonyms.ContainsKey(synonym))
                    {
                        allSynonyms[synonym] = new List<string>();
                    }
                    allSynonyms[synonym].Add(dataType);
                }
            }

            // Check for conflicts (synonyms mapped to multiple data types)
            foreach (var (synonym, dataTypes) in allSynonyms)
            {
                if (dataTypes.Count > 1)
                {
                    result.Conflicts.Add(new SynonymConflict
                    {
                        Synonym = synonym,
                        ConflictingDataTypes = dataTypes
                    });
                }
            }

            // Check for empty data types
            foreach (var dataType in DataTypeSynonymMappings.GetSupportedDataTypes())
            {
                var synonyms = DataTypeSynonymMappings.GetSynonymsForDataType(dataType);
                if (synonyms.Count == 0)
                {
                    result.EmptyDataTypes.Add(dataType);
                }
            }

            result.IsValid = result.Conflicts.Count == 0;
            result.TotalSynonyms = allSynonyms.Count;
            result.TotalDataTypes = DataTypeSynonymMappings.GetSupportedDataTypes().Count();

            _logger.LogInformation("Synonym mapping validation completed. Valid: {IsValid}, Conflicts: {Conflicts}, Total Synonyms: {TotalSynonyms}",
                result.IsValid, result.Conflicts.Count, result.TotalSynonyms);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating synonym mappings");
            result.IsValid = false;
            result.ValidationError = ex.Message;
            return result;
        }
    }
}

/// <summary>
/// Result of synonym mapping validation
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; } = true;
    public List<SynonymConflict> Conflicts { get; set; } = new();
    public List<string> EmptyDataTypes { get; set; } = new();
    public int TotalSynonyms { get; set; }
    public int TotalDataTypes { get; set; }
    public string? ValidationError { get; set; }
}

/// <summary>
/// Represents a synonym conflict where one synonym maps to multiple data types
/// </summary>
public class SynonymConflict
{
    public string Synonym { get; set; } = string.Empty;
    public List<string> ConflictingDataTypes { get; set; } = new();
}
