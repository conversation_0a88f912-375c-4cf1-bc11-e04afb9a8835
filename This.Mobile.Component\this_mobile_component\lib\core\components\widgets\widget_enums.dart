/// Shared enums for all custom widgets to avoid conflicts

/// Display format for checkbox output widgets
enum DisplayFormat {
  list,
  chips,
  badges,
  inline,
}

/// Display style for radio output widgets
enum DisplayStyle {
  simple,
  detailed,
  badge,
  card,
}

/// Display format for month components
enum MonthDisplayFormat {
  full, // January, February, etc.
  short, // Jan, Feb, etc.
  number, // 01, 02, etc.
}

/// Display format for day components
enum DayDisplayFormat {
  number, // 1, 2, 3, etc.
  paddedNumber, // 01, 02, 03, etc.
  ordinal, // 1st, 2nd, 3rd, etc.
}

/// Display format for year components
enum YearDisplayFormat {
  full, // 2024
  short, // 24
  withSuffix, // 2024 AD
}

/// Display format for time components
enum TimeDisplayFormat {
  hhMm, // 14:30 or 2:30 PM
  hhMmSs, // 14:30:00 or 2:30:00 PM
  descriptive, // Quarter past two, Half past midnight, etc.
}

/// Time format for time input components
enum TimeFormat {
  hhMm,
  hhMmSs,
}
