using Ardalis.Specification;
using Domain.Entities;

namespace Application.MetadataManagement.Specifications;

/// <summary>
/// Specification to count Metadata with filters
/// </summary>
public class MetadataCountSpec : Specification<Domain.Entities.Metadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public MetadataCountSpec(string? searchTerm, Guid? dataTypeId, bool? isVisible)
    {
        Query.Where(m => !m.IsDeleted);

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(m => m.Name.Contains(searchTerm) || // Updated: Name is now Name property
                             (m.DisplayLabel != null && m.DisplayLabel.Contains(searchTerm)));
        }

        if (dataTypeId.HasValue)
        {
            Query.Where(m => m.DataTypeId == dataTypeId.Value);
        }

        if (isVisible.HasValue)
        {
            Query.Where(m => m.IsVisible == isVisible.Value);
        }
    }
}
