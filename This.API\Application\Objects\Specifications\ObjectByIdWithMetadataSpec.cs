// COMMENTED OUT - Contains Feature references that have been removed
// TODO: Update to use Product references or remove if not needed

/*
using Ardalis.Specification;
using ObjectEntity = Domain.Entities.Object;

namespace Application.Objects.Specifications;

/// <summary>
/// Specification to get Object by ID with metadata information
/// </summary>
public class ObjectByIdWithMetadataSpec : Specification<ObjectEntity>, ISingleResultSpecification<ObjectEntity>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectByIdWithMetadataSpec(Guid id)
    {
        Query.Where(o => o.Id == id && !o.IsDeleted);

        // Include related data
        Query.Include(o => o.Feature);
        Query.Include(o => o.ParentObject);
        Query.Include(o => o.ObjectMetadata.Where(om => om.IsActive && !om.IsDeleted))
             .ThenInclude(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        // Include child objects count
        Query.Include(o => o.ChildObjects.Where(co => co.IsActive && !co.IsDeleted));
    }
}
*/
