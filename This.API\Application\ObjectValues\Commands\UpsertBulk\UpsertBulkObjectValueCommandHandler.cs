// COMMENTED OUT - Contains IsRefId and IsUserId properties that have been removed
// TODO: Update to remove these properties or remove if not needed

/*
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using Application.ObjectValues.Commands.UpsertSingle;

namespace Application.ObjectValues.Commands.UpsertBulk;

/// <summary>
/// Handler for UpsertBulkObjectValueCommand
/// </summary>
public class UpsertBulkObjectValueCommandHandler : IRequestHandler<UpsertBulkObjectValueCommand, Result<UpsertBulkObjectValueResponse>>
{
    private readonly IMediator _mediator;
    private readonly ILogger<UpsertBulkObjectValueCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertBulkObjectValueCommandHandler(
        IMediator mediator,
        ILogger<UpsertBulkObjectValueCommandHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<UpsertBulkObjectValueResponse>> Handle(UpsertBulkObjectValueCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting bulk upsert of {Count} ObjectValue instances", request.Instances.Count);

            var response = new UpsertBulkObjectValueResponse
            {
                TotalInstancesProcessed = request.Instances.Count
            };

            foreach (var instanceCommand in request.Instances)
            {
                try
                {
                    var result = await _mediator.Send(instanceCommand, cancellationToken);

                    if (result.Succeeded)
                    {
                        response.Instances.Add(result.Data!);
                        response.TotalRecordsProcessed += result.Data!.TotalProcessed;
                        response.TotalInsertedCount += result.Data!.InsertedCount;
                        response.TotalUpdatedCount += result.Data!.UpdatedCount;
                        response.TotalFailedCount += result.Data!.FailedCount;

                        if (result.Data!.Errors.Any())
                        {
                            response.Errors.AddRange(result.Data!.Errors.Select(e => $"RefId {result.Data.RefId}: {e}"));
                        }
                    }
                    else
                    {
                        response.FailedInstancesCount++;
                        response.Errors.Add($"RefId {instanceCommand.RefId}: {result.Message}");
                        _logger.LogWarning("Failed to upsert ObjectValue instance for RefId: {RefId}. Error: {Error}",
                            instanceCommand.RefId, result.Message);
                    }
                }
                catch (Exception ex)
                {
                    response.FailedInstancesCount++;
                    var errorMessage = $"RefId {instanceCommand.RefId}: {ex.Message}";
                    response.Errors.Add(errorMessage);
                    _logger.LogError(ex, "Exception occurred while upserting ObjectValue instance for RefId: {RefId}",
                        instanceCommand.RefId);
                }
            }

            response.Message = $"Bulk upsert completed. Instances: {response.TotalInstancesProcessed}, Records Inserted: {response.TotalInsertedCount}, Updated: {response.TotalUpdatedCount}, Failed: {response.TotalFailedCount}, Failed Instances: {response.FailedInstancesCount}";

            _logger.LogInformation("Completed bulk upsert of ObjectValue instances. Instances: {Instances}, Records Inserted: {Inserted}, Updated: {Updated}, Failed: {Failed}, Failed Instances: {FailedInstances}",
                response.TotalInstancesProcessed, response.TotalInsertedCount, response.TotalUpdatedCount, response.TotalFailedCount, response.FailedInstancesCount);

            return Result<UpsertBulkObjectValueResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk upsert of ObjectValue instances");
            return Result<UpsertBulkObjectValueResponse>.Failure($"Error during bulk upsert: {ex.Message}");
        }
    }
}
*/
