using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to find ObjectValue by RefId and Name
/// </summary>
public class ObjectValueByRefIdAndMetadataKeySpecification : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValueByRefIdAndMetadataKeySpecification(Guid refId, string metadataKey, string tenantId)
    {
        Query.Where(ov => ov.RefId == refId &&
                         ov.ObjectMetadata.Metadata.Name == metadataKey && // Updated: Name is now Name property
                         !ov.IsDeleted);

        // Include metadata for validation and response
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Metadata);

        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object);
    }
}
