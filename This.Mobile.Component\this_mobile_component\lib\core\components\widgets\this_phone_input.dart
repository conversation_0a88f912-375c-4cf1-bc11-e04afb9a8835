import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// Country model for phone input
class PhoneCountry {
  final String name;
  final String code;
  final String dialCode;
  final String flag;
  final String? format;
  final int? minLength;
  final int? maxLength;

  const PhoneCountry({
    required this.name,
    required this.code,
    required this.dialCode,
    required this.flag,
    this.format,
    this.minLength,
    this.maxLength,
  });
}

/// A customizable phone input widget following the 'this_componentName_relatedTo' naming convention
/// This widget handles phone number input with country selection and validation
class ThisPhoneInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final String value;
  final ValueChanged<String> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final ValueChanged<PhoneCountry>? onCountryChange;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final String defaultCountry;
  final List<String>? allowedCountries;
  final List<String>? blockedCountries;
  final bool showFlag;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool allowInternational;
  final bool requireCountryCode;
  final int minLength;
  final int maxLength;
  final bool allowExtensions;
  final bool autoFocus;
  final String? Function(String, PhoneCountry?)? customValidation;

  const ThisPhoneInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.onCountryChange,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.defaultCountry = 'US',
    this.allowedCountries,
    this.blockedCountries,
    this.showFlag = true,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.allowInternational = true,
    this.requireCountryCode = true,
    this.minLength = 7,
    this.maxLength = 15,
    this.allowExtensions = false,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisPhoneInput> createState() => _ThisPhoneInputState();
}

class _ThisPhoneInputState extends State<ThisPhoneInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;
  PhoneCountry? _selectedCountry;

  // Common countries with their data
  static const List<PhoneCountry> _countries = [
    PhoneCountry(name: 'United States', code: 'US', dialCode: '+1', flag: '🇺🇸', format: '(XXX) XXX-XXXX', minLength: 10, maxLength: 10),
    PhoneCountry(name: 'United Kingdom', code: 'GB', dialCode: '+44', flag: '🇬🇧', format: 'XXXX XXX XXXX', minLength: 10, maxLength: 11),
    PhoneCountry(name: 'Canada', code: 'CA', dialCode: '+1', flag: '🇨🇦', format: '(XXX) XXX-XXXX', minLength: 10, maxLength: 10),
    PhoneCountry(name: 'Australia', code: 'AU', dialCode: '+61', flag: '🇦🇺', format: 'XXXX XXX XXX', minLength: 9, maxLength: 10),
    PhoneCountry(name: 'Germany', code: 'DE', dialCode: '+49', flag: '🇩🇪', format: 'XXX XXXXXXXX', minLength: 11, maxLength: 12),
    PhoneCountry(name: 'France', code: 'FR', dialCode: '+33', flag: '🇫🇷', format: 'XX XX XX XX XX', minLength: 10, maxLength: 10),
    PhoneCountry(name: 'India', code: 'IN', dialCode: '+91', flag: '🇮🇳', format: 'XXXXX XXXXX', minLength: 10, maxLength: 10),
    PhoneCountry(name: 'China', code: 'CN', dialCode: '+86', flag: '🇨🇳', format: 'XXX XXXX XXXX', minLength: 11, maxLength: 11),
    PhoneCountry(name: 'Japan', code: 'JP', dialCode: '+81', flag: '🇯🇵', format: 'XXX-XXXX-XXXX', minLength: 10, maxLength: 11),
    PhoneCountry(name: 'Brazil', code: 'BR', dialCode: '+55', flag: '🇧🇷', format: '(XX) XXXXX-XXXX', minLength: 11, maxLength: 11),
    PhoneCountry(name: 'Mexico', code: 'MX', dialCode: '+52', flag: '🇲🇽', format: 'XXX XXX XXXX', minLength: 10, maxLength: 10),
    PhoneCountry(name: 'Spain', code: 'ES', dialCode: '+34', flag: '🇪🇸', format: 'XXX XX XX XX', minLength: 9, maxLength: 9),
    PhoneCountry(name: 'Italy', code: 'IT', dialCode: '+39', flag: '🇮🇹', format: 'XXX XXX XXXX', minLength: 10, maxLength: 11),
    PhoneCountry(name: 'Netherlands', code: 'NL', dialCode: '+31', flag: '🇳🇱', format: 'XX XXX XXXX', minLength: 9, maxLength: 9),
    PhoneCountry(name: 'South Korea', code: 'KR', dialCode: '+82', flag: '🇰🇷', format: 'XXX-XXXX-XXXX', minLength: 10, maxLength: 11),
  ];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
    _focusNode = FocusNode();
    _selectedCountry = _getCurrentCountry();
    
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void didUpdateWidget(ThisPhoneInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value;
      _selectedCountry = _getCurrentCountry();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  List<PhoneCountry> get _availableCountries {
    return _countries.where((country) {
      if (widget.allowedCountries != null && widget.allowedCountries!.isNotEmpty) {
        return widget.allowedCountries!.contains(country.code);
      }
      if (widget.blockedCountries != null && widget.blockedCountries!.isNotEmpty) {
        return !widget.blockedCountries!.contains(country.code);
      }
      return true;
    }).toList();
  }

  PhoneCountry? _getCurrentCountry() {
    if (widget.value.startsWith('+')) {
      // Find country by dial code in the value
      final phoneWithCode = widget.value.split(' ')[0];
      return _availableCountries.firstWhere(
        (country) => phoneWithCode.startsWith(country.dialCode),
        orElse: () => _availableCountries.firstWhere(
          (country) => country.code == widget.defaultCountry,
          orElse: () => _availableCountries.first,
        ),
      );
    }
    return _availableCountries.firstWhere(
      (country) => country.code == widget.defaultCountry,
      orElse: () => _availableCountries.first,
    );
  }

  String _extractPhoneNumber(String fullValue) {
    if (_selectedCountry == null) return fullValue;

    if (fullValue.startsWith(_selectedCountry!.dialCode)) {
      return fullValue.substring(_selectedCountry!.dialCode.length).trim();
    }

    if (fullValue.startsWith('+')) {
      final parts = fullValue.split(' ');
      return parts.skip(1).join(' ');
    }

    return fullValue;
  }

  List<String> _validateValue(String value) {
    final errors = <String>[];

    // 1. Required validation
    if (widget.required && value.trim().isEmpty) {
      errors.add('${widget.label} is required');
      return errors;
    }

    // Skip other validations if empty and not required
    if (value.trim().isEmpty && !widget.required) {
      return errors;
    }

    // 2. Country code validation
    if (widget.requireCountryCode) {
      if (!value.startsWith('+') && _selectedCountry == null) {
        errors.add('${widget.label} must include a country code');
        return errors;
      }
    }

    // 3. Phone number format validation
    final phoneNumber = _extractPhoneNumber(value);
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'\D'), '');

    if (digitsOnly.isEmpty) {
      errors.add('${widget.label} must be a valid phone number');
      return errors;
    }

    // Country-specific length validation
    if (_selectedCountry != null) {
      final minLen = _selectedCountry!.minLength ?? widget.minLength;
      final maxLen = _selectedCountry!.maxLength ?? widget.maxLength;
      if (digitsOnly.length < minLen || digitsOnly.length > maxLen) {
        errors.add('${widget.label} must be a valid phone number');
        return errors;
      }
    } else {
      if (digitsOnly.length < widget.minLength || digitsOnly.length > widget.maxLength) {
        errors.add('${widget.label} must be a valid phone number');
        return errors;
      }
    }

    // 4. Extension validation
    if (!widget.allowExtensions) {
      if (value.toLowerCase().contains('ext') || value.contains('#')) {
        errors.add('${widget.label} cannot contain extensions');
        return errors;
      }
    }

    // 5. International format validation
    if (!widget.allowInternational && widget.requireCountryCode) {
      if (_selectedCountry?.code != widget.defaultCountry) {
        errors.add('${widget.label} must be a domestic number');
        return errors;
      }
    }

    // 6. Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(value, _selectedCountry);
      if (customError != null) {
        errors.add(customError);
        return errors;
      }
    }

    return errors;
  }

  void _handleCountrySelect(PhoneCountry country) {
    setState(() {
      _selectedCountry = country;
    });

    // Update phone number with new country code
    final phoneNumber = _extractPhoneNumber(widget.value);
    final newValue = phoneNumber.isNotEmpty ? '${country.dialCode} $phoneNumber' : '${country.dialCode} ';
    widget.onChanged(newValue);
    widget.onCountryChange?.call(country);

    // Validate with new country
    final newErrors = _validateValue(newValue);
    setState(() {
      _errors = newErrors;
      _isValidated = newValue.trim().isNotEmpty;
    });
    widget.onValidation?.call(newErrors);
  }

  void _handlePhoneChange(String value) {
    // If no country selected and value starts with +, try to detect country
    if (_selectedCountry == null && value.startsWith('+')) {
      final detectedCountry = _availableCountries.firstWhere(
        (country) => value.startsWith(country.dialCode),
        orElse: () => _availableCountries.first,
      );
      setState(() {
        _selectedCountry = detectedCountry;
      });
      widget.onCountryChange?.call(detectedCountry);
    }

    // Ensure country code is included if required
    if (_selectedCountry != null && widget.requireCountryCode && !value.startsWith(_selectedCountry!.dialCode)) {
      if (!value.startsWith('+')) {
        // Add country code
        value = '${_selectedCountry!.dialCode} $value';
      }
    }

    widget.onChanged(value);

    // Real-time validation (only if not validating on blur)
    if (!widget.validateOnBlur) {
      final errors = _validateValue(value);
      setState(() {
        _errors = errors;
        _isValidated = value.trim().isNotEmpty;
      });
      widget.onValidation?.call(errors);
    }
  }

  void _handleBlur() {
    // Validate on blur if enabled
    if (widget.validateOnBlur) {
      final errors = _validateValue(widget.value);
      setState(() {
        _errors = errors;
        _isValidated = widget.value.trim().isNotEmpty;
      });
      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated || widget.value.trim().isEmpty) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  Future<void> _showCountryPicker() async {
    final selectedCountry = await showModalBottomSheet<PhoneCountry>(
      context: context,
      backgroundColor: ColorPalette.darkToneInk,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Select Country',
              style: LexendTextStyles.lexend16Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _availableCountries.length,
                itemBuilder: (context, index) {
                  final country = _availableCountries[index];
                  return ListTile(
                    leading: widget.showFlag ? Text(country.flag, style: const TextStyle(fontSize: 24)) : null,
                    title: Text(
                      country.name,
                      style: LexendTextStyles.lexend14Regular.copyWith(
                        color: ColorPalette.white,
                      ),
                    ),
                    trailing: Text(
                      country.dialCode,
                      style: LexendTextStyles.lexend14Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                    onTap: () => Navigator.of(context).pop(country),
                    selected: _selectedCountry?.code == country.code,
                    selectedTileColor: ColorPalette.white.withValues(alpha: 0.1),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );

    if (selectedCountry != null) {
      _handleCountrySelect(selectedCountry);
    }
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors && widget.value.trim().isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled 
                    ? ColorPalette.placeHolderTextColor 
                    : ColorPalette.white,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        
        // Input Field with Country Selector
        Row(
          children: [
            // Country Selector
            if (_selectedCountry != null)
              GestureDetector(
                onTap: widget.disabled || widget.readOnly ? null : _showCountryPicker,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: hasErrors 
                          ? const Color(0xFFC73E1D)
                          : (isValid ? ColorPalette.green : ColorPalette.gray300),
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(6),
                      bottomLeft: Radius.circular(6),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.showFlag) ...[
                        Text(_selectedCountry!.flag, style: const TextStyle(fontSize: 20)),
                        const SizedBox(width: 4),
                      ],
                      Text(
                        _selectedCountry!.dialCode,
                        style: LexendTextStyles.lexend14Regular.copyWith(
                          color: ColorPalette.white,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.arrow_drop_down,
                        size: 20,
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ],
                  ),
                ),
              ),
            
            // Phone Input
            Expanded(
              child: TextFormField(
                controller: _controller,
                focusNode: _focusNode,
                enabled: !widget.disabled,
                readOnly: widget.readOnly,
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.next,
                onChanged: _handlePhoneChange,
                onFieldSubmitted: (_) => _handleBlur(),
                onTapOutside: (_) => _handleBlur(),
                decoration: InputDecoration(
                  hintText: widget.placeholder ?? 'Enter phone number...',
                  hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                  prefixIcon: widget.showIcon 
                      ? Icon(Icons.phone, size: 20, color: ColorPalette.placeHolderTextColor)
                      : null,
                  suffixIcon: _getValidationIcon(),
                  errorText: hasErrors ? _errors.first : null,
                  errorStyle: LexendTextStyles.lexend12Regular.copyWith(
                    color: const Color(0xFFC73E1D),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.only(
                      topRight: const Radius.circular(6),
                      bottomRight: const Radius.circular(6),
                      topLeft: _selectedCountry != null ? Radius.zero : const Radius.circular(6),
                      bottomLeft: _selectedCountry != null ? Radius.zero : const Radius.circular(6),
                    ),
                    borderSide: BorderSide(
                      color: hasErrors 
                          ? const Color(0xFFC73E1D)
                          : (isValid ? ColorPalette.green : ColorPalette.gray300),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.only(
                      topRight: const Radius.circular(6),
                      bottomRight: const Radius.circular(6),
                      topLeft: _selectedCountry != null ? Radius.zero : const Radius.circular(6),
                      bottomLeft: _selectedCountry != null ? Radius.zero : const Radius.circular(6),
                    ),
                    borderSide: BorderSide(
                      color: hasErrors 
                          ? const Color(0xFFC73E1D)
                          : (isValid ? ColorPalette.green : ColorPalette.gray300),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.only(
                      topRight: const Radius.circular(6),
                      bottomRight: const Radius.circular(6),
                      topLeft: _selectedCountry != null ? Radius.zero : const Radius.circular(6),
                      bottomLeft: _selectedCountry != null ? Radius.zero : const Radius.circular(6),
                    ),
                    borderSide: BorderSide(
                      color: hasErrors 
                          ? const Color(0xFFC73E1D)
                          : (isValid ? ColorPalette.green : ColorPalette.white),
                      width: 2,
                    ),
                  ),
                ),
                style: LexendTextStyles.lexend14Regular.copyWith(
                  color: widget.disabled 
                      ? ColorPalette.placeHolderTextColor 
                      : ColorPalette.white,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9+\-\s\(\)#ext]')),
                ],
              ),
            ),
          ],
        ),
        
        // Helper text
        if (!hasErrors && _selectedCountry != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_selectedCountry!.format != null)
                  Text(
                    'Format: ${_selectedCountry!.format} (${_selectedCountry!.name})',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                if (widget.allowExtensions)
                  Text(
                    'Extensions allowed (use "ext" or "#")',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                if (!widget.allowInternational)
                  Text(
                    'Domestic numbers only',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
              ],
            ),
          ),
      ],
    );
  }
}
