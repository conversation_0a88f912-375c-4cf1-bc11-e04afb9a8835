using Abstraction.Common;
using Domain.Common.Contracts;
using Domain.Entities;
using Finbuckle.MultiTenant;
using Infrastructure.Database.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Database;

/// <summary>
/// Application database context
/// </summary>
public class ApplicationDbContext : BaseDbContext
{
    /// <summary>
    /// Current user service
    /// </summary>
    private readonly ICurrentUser _currentUser;

    /// <summary>
    /// Tenant ID
    /// </summary>
    public string? TenantId => TenantInfo?.Id;

    /// <summary>
    /// Constructor
    /// </summary>
    public ApplicationDbContext(ITenantInfo tenantInfo, DbContextOptions options, ICurrentUser currentUser)
        : base(tenantInfo, options)
    {
        _currentUser = currentUser;
    }

    // Core entities DbSets
    /// <summary>
    /// Data types
    /// </summary>
    public DbSet<DataType> DataTypes => Set<DataType>();

    /// <summary>
    /// Metadata definitions
    /// </summary>
    public DbSet<Metadata> Metadata => Set<Metadata>();

    /// <summary>
    /// Products
    /// </summary>
    public DbSet<Product> Products => Set<Product>();

    /// <summary>
    /// Objects
    /// </summary>
    public DbSet<Domain.Entities.Object> Objects => Set<Domain.Entities.Object>();

    /// <summary>
    /// Subscriptions
    /// </summary>
    public DbSet<Subscription> Subscriptions => Set<Subscription>();

    // Metadata link entities
    /// <summary>
    /// Tenant info metadata
    /// </summary>
    public DbSet<TenantInfoMetadata> TenantInfoMetadata => Set<TenantInfoMetadata>();

    /// <summary>
    /// Product metadata
    /// </summary>
    public DbSet<ProductMetadata> ProductMetadata => Set<ProductMetadata>();

    /// <summary>
    /// Role metadata
    /// </summary>
    public DbSet<RoleMetadata> RoleMetadata => Set<RoleMetadata>();

    /// <summary>
    /// User metadata
    /// </summary>
    public DbSet<UserMetadata> UserMetadata => Set<UserMetadata>();

    /// <summary>
    /// Object metadata
    /// </summary>
    public DbSet<ObjectMetadata> ObjectMetadata => Set<ObjectMetadata>();

    /// <summary>
    /// Subscription metadata
    /// </summary>
    public DbSet<SubscriptionMetadata> SubscriptionMetadata => Set<SubscriptionMetadata>();

    // Value storage entities
    /// <summary>
    /// Tenant info values
    /// </summary>
    public DbSet<TenantInfoValue> TenantInfoValues => Set<TenantInfoValue>();

    /// <summary>
    /// Product values
    /// </summary>
    public DbSet<ProductValue> ProductValues => Set<ProductValue>();

    /// <summary>
    /// Role values
    /// </summary>
    public DbSet<RoleValue> RoleValues => Set<RoleValue>();

    /// <summary>
    /// User values
    /// </summary>
    public DbSet<UserValue> UserValues => Set<UserValue>();

    /// <summary>
    /// Object values
    /// </summary>
    public DbSet<ObjectValue> ObjectValues => Set<ObjectValue>();

    /// <summary>
    /// Subscription values
    /// </summary>
    public DbSet<SubscriptionValue> SubscriptionValues => Set<SubscriptionValue>();

    // Integration entities
    /// <summary>
    /// Integrations
    /// </summary>
    public DbSet<Integration> Integrations => Set<Integration>();

    /// <summary>
    /// Integration APIs
    /// </summary>
    public DbSet<IntegrationApi> IntegrationApis => Set<IntegrationApi>();

    /// <summary>
    /// Integration configurations
    /// </summary>
    public DbSet<IntegrationConfiguration> IntegrationConfigurations => Set<IntegrationConfiguration>();

    /// <summary>
    /// Field mappings
    /// </summary>
    public DbSet<FieldMapping> FieldMappings => Set<FieldMapping>();

    /// <summary>
    /// Conflict resolutions
    /// </summary>
    public DbSet<ConflictResolution> ConflictResolutions => Set<ConflictResolution>();

    /// <summary>
    /// Sync histories
    /// </summary>
    public DbSet<SyncHistory> SyncHistories => Set<SyncHistory>();

    // Lookup system entities
    /// <summary>
    /// Contexts (global lookup categories)
    /// </summary>
    public DbSet<Context> Contexts => Set<Context>();

    /// <summary>
    /// Lookups (global lookup values)
    /// </summary>
    public DbSet<Lookup> Lookups => Set<Lookup>();

    /// <summary>
    /// Tenant contexts (tenant-specific lookup categories)
    /// </summary>
    public DbSet<TenantContext> TenantContexts => Set<TenantContext>();

    /// <summary>
    /// Tenant lookups (tenant-specific lookup values)
    /// </summary>
    public DbSet<TenantLookup> TenantLookups => Set<TenantLookup>();

    /// <summary>
    /// Object lookups (object-based lookup configurations)
    /// </summary>
    public DbSet<ObjectLookup> ObjectLookups => Set<ObjectLookup>();


    /// <summary>
    /// Configure the model
    /// </summary>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // QueryFilters need to be applied before base.OnModelCreating
        modelBuilder.AppendGlobalQueryFilter<ISoftDelete>(s => s.IsDeleted == false);

        base.OnModelCreating(modelBuilder);

        modelBuilder.ApplyConfigurationsFromAssembly(GetType().Assembly);
    }
    /// <summary>
    /// Configure the context
    /// </summary>
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        // TODO: We want this only for development probably... maybe better make it configurable in logger.json config?
        optionsBuilder.EnableSensitiveDataLogging();

        // If you want to see the sql queries that efcore executes:

        // Uncomment the next line to see them in the output window of visual studio
        // optionsBuilder.LogTo(m => System.Diagnostics.Debug.WriteLine(m), Microsoft.Extensions.Logging.LogLevel.Information);

        // Or uncomment the next line if you want to see them in the console
        // optionsBuilder.LogTo(Console.WriteLine, Microsoft.Extensions.Logging.LogLevel.Information);

        if (!string.IsNullOrWhiteSpace(TenantInfo?.ConnectionString))
        {
            optionsBuilder.UseDatabase("PostgreSQL", TenantInfo.ConnectionString);
        }
        optionsBuilder.EnableSensitiveDataLogging(true);
    }

    /// <summary>
    /// Save changes
    /// </summary>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        var userId = _currentUser.GetUserId();
        var auditEntries = HandleAuditingBeforeSaveChanges(userId);
        int result = await base.SaveChangesAsync(cancellationToken);
        await HandleAuditingAfterSaveChangesAsync(auditEntries, cancellationToken);
        return result;
    }

    /// <summary>
    /// Handle auditing before save changes
    /// </summary>
    private List<AuditTrail> HandleAuditingBeforeSaveChanges(string? userId)
    {
        foreach (var entry in ChangeTracker.Entries<IAuditableEntity>().ToList())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedBy = string.IsNullOrEmpty(userId) ? Guid.Empty : Guid.Parse(userId);
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    break;

                case EntityState.Modified:
                    entry.Entity.ModifiedBy = string.IsNullOrEmpty(userId) ? Guid.Empty : Guid.Parse(userId);
                    entry.Entity.ModifiedAt = DateTime.UtcNow;
                    break;

                case EntityState.Deleted:
                    if (entry.Entity is ISoftDelete softDeleteEntity)
                    {
                        softDeleteEntity.IsDeleted = true;
                        entry.State = EntityState.Modified;
                    }
                    break;
            }
        }

        ChangeTracker.DetectChanges();

        var trailEntries = new List<AuditTrail>();
        foreach (var entry in ChangeTracker.Entries<IAuditableEntity>()
            .Where(e => e.State is EntityState.Added or EntityState.Deleted or EntityState.Modified)
            .ToList())
        {
            var trailEntry = new AuditTrail(entry, _currentUser)
            {
                TableName = entry.Entity.GetType().Name,
                UserId = userId
            };
            trailEntries.Add(trailEntry);
            foreach (var property in entry.Properties)
            {
                if (property.IsTemporary)
                {
                    trailEntry.TemporaryProperties.Add(property);
                    continue;
                }

                string propertyName = property.Metadata.Name;
                if (property.Metadata.IsPrimaryKey())
                {
                    trailEntry.KeyValues[propertyName] = property.CurrentValue;
                    continue;
                }

                switch (entry.State)
                {
                    case EntityState.Added:
                        trailEntry.TrailType = TrailType.Create;
                        trailEntry.NewValues[propertyName] = property.CurrentValue;
                        break;

                    case EntityState.Deleted:
                        trailEntry.TrailType = TrailType.Delete;
                        trailEntry.OldValues[propertyName] = property.OriginalValue;
                        break;

                    case EntityState.Modified:
                        if (property.IsModified && entry.Entity is ISoftDelete && property.OriginalValue == null && property.CurrentValue != null)
                        {
                            trailEntry.ChangedColumns.Add(propertyName);
                            trailEntry.TrailType = TrailType.Delete;
                            trailEntry.OldValues[propertyName] = property.OriginalValue;
                            trailEntry.NewValues[propertyName] = property.CurrentValue;
                        }
                        else if (property.IsModified && property.OriginalValue?.Equals(property.CurrentValue) == false)
                        {
                            trailEntry.ChangedColumns.Add(propertyName);
                            trailEntry.TrailType = TrailType.Update;
                            trailEntry.OldValues[propertyName] = property.OriginalValue;
                            trailEntry.NewValues[propertyName] = property.CurrentValue;
                        }
                        break;
                }
            }
        }

        foreach (var auditEntry in trailEntries.Where(e => !e.HasTemporaryProperties))
        {
            // TODO: Save audit trails to database or other storage
        }

        return trailEntries.Where(e => e.HasTemporaryProperties).ToList();
    }

    /// <summary>
    /// Handle auditing after save changes
    /// </summary>
    private Task HandleAuditingAfterSaveChangesAsync(List<AuditTrail> trailEntries, CancellationToken cancellationToken = default)
    {
        if (trailEntries == null || trailEntries.Count == 0)
            return Task.CompletedTask;

        foreach (var entry in trailEntries)
        {
            foreach (var prop in entry.TemporaryProperties)
            {
                if (prop.Metadata.IsPrimaryKey())
                {
                    entry.KeyValues[prop.Metadata.Name] = prop.CurrentValue;
                }
                else
                {
                    entry.NewValues[prop.Metadata.Name] = prop.CurrentValue;
                }
            }
        }

        // TODO: Save audit trails to database or other storage
        return Task.CompletedTask;
    }
}
