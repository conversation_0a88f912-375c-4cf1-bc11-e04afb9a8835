using Application.FieldMappings.DTOs;
using Application.FieldMappings.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Queries;

/// <summary>
/// Get field mappings query handler
/// </summary>
public class GetFieldMappingsQueryHandler : IRequestHandler<GetFieldMappingsQuery, PaginatedResult<ViewFieldMappingDto>>
{
    private readonly IReadRepository<FieldMapping> _fieldMappingRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetFieldMappingsQueryHandler(IReadRepository<FieldMapping> fieldMappingRepository)
    {
        _fieldMappingRepository = fieldMappingRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<ViewFieldMappingDto>> Handle(GetFieldMappingsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Calculate pagination
            var skip = (request.PageNumber - 1) * request.PageSize;

            // Create specifications for data and count
            var dataSpec = new FieldMappingsWithFiltersSpec(
                request.SearchTerm,
                request.ApiName,
                request.SourceType,
                request.ObjectMetadataId,
                request.UserId,
                request.RoleId,
                request.TargetObjectName,
                skip,
                request.PageSize);

            var countSpec = new FieldMappingsCountSpec(
                request.SearchTerm,
                request.ApiName,
                request.SourceType,
                request.ObjectMetadataId,
                request.UserId,
                request.RoleId,
                request.TargetObjectName);

            // Get data and count
            var fieldMappings = await _fieldMappingRepository.ListAsync(dataSpec, cancellationToken);
            var totalCount = await _fieldMappingRepository.CountAsync(countSpec, cancellationToken);

            // Map to view DTOs
            var viewDtos = fieldMappings.Select(fm => new ViewFieldMappingDto
            {
                Id = fm.Id,
                IntegrationId = fm.IntegrationId,
                ApiName = fm.ApiName,
                SourceField = fm.SourceField,
                SourceType = fm.SourceType,
                ObjectMetadataId = fm.ObjectMetadataId,
                ObjectMetadataKey = fm.ObjectMetadata?.Metadata?.Name, // Updated: Name is now Name property
                UserId = fm.UserId,
                UserName = fm.User?.UserName,
                RoleId = fm.RoleId,
                RoleName = fm.Role?.Name,
                TargetObjectName = fm.TargetObjectName,
                Notes = fm.Notes,
                CreatedAt = fm.CreatedAt,
                CreatedBy = fm.CreatedBy,
                ModifiedAt = fm.ModifiedAt,
                ModifiedBy = fm.ModifiedBy
            }).ToList();

            return new PaginatedResult<ViewFieldMappingDto>(
                viewDtos,
                request.PageNumber,
                request.PageSize,
                totalCount);
        }
        catch (Exception ex)
        {
            return PaginatedResult<ViewFieldMappingDto>.Failure($"Failed to get field mappings: {ex.Message}");
        }
    }
}
