using Ardalis.Specification;
using Domain.Entities;

namespace Application.Templates.Specifications;

/// <summary>
/// Specification to get template by ID
/// </summary>
public class TemplateByIdSpec : Specification<Template>
{
    public TemplateByIdSpec(Guid id)
    {
        Query.Where(t => t.Id == id && !t.IsDeleted);
    }
}

/// <summary>
/// Specification to get templates with filters and pagination
/// </summary>
public class TemplatesWithFiltersSpec : Specification<Template>
{
    public TemplatesWithFiltersSpec(
        string? searchTerm = null,
        Guid? productId = null,
        string? stage = null,
        bool? isActive = null,
        bool includeDeleted = false,
        int skip = 0,
        int take = 10)
    {
        // Apply filters
        if (!includeDeleted)
        {
            Query.Where(t => !t.IsDeleted);
        }

        if (productId.HasValue)
        {
            Query.Where(t => t.ProductId == productId.Value);
        }

        if (!string.IsNullOrWhiteSpace(stage))
        {
            Query.Where(t => t.Stage.ToLower() == stage.ToLower());
        }

        if (isActive.HasValue)
        {
            Query.Where(t => t.IsActive == isActive.Value);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            Query.Where(t =>
                t.Version.ToLower().Contains(searchTermLower) ||
                t.Stage.ToLower().Contains(searchTermLower));
        }

        Query.OrderByDescending(t => t.CreatedAt);
        Query.Skip(skip).Take(take);
    }
}

/// <summary>
/// Specification to count templates with filters
/// </summary>
public class TemplatesCountSpec : Specification<Template>
{
    public TemplatesCountSpec(
        string? searchTerm = null,
        Guid? productId = null,
        string? stage = null,
        bool? isActive = null,
        bool includeDeleted = false)
    {
        // Apply filters
        if (!includeDeleted)
        {
            Query.Where(t => !t.IsDeleted);
        }

        if (productId.HasValue)
        {
            Query.Where(t => t.ProductId == productId.Value);
        }

        if (!string.IsNullOrWhiteSpace(stage))
        {
            Query.Where(t => t.Stage.ToLower() == stage.ToLower());
        }

        if (isActive.HasValue)
        {
            Query.Where(t => t.IsActive == isActive.Value);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            Query.Where(t =>
                t.Version.ToLower().Contains(searchTermLower) ||
                t.Stage.ToLower().Contains(searchTermLower));
        }
    }
}

/// <summary>
/// Specification to check for duplicate template (same product, version, stage)
/// </summary>
public class TemplateDuplicateSpec : Specification<Template>
{
    public TemplateDuplicateSpec(Guid productId, string version, string stage, Guid? excludeId = null)
    {
        Query.Where(t => t.ProductId == productId &&
                        t.Version == version &&
                        t.Stage == stage &&
                        !t.IsDeleted);

        if (excludeId.HasValue)
        {
            Query.Where(t => t.Id != excludeId.Value);
        }
    }
}

/// <summary>
/// Specification to get templates by product ID
/// </summary>
public class TemplatesByProductIdSpec : Specification<Template>
{
    public TemplatesByProductIdSpec(Guid productId, bool includeDeleted = false)
    {
        Query.Where(t => t.ProductId == productId);

        if (!includeDeleted)
        {
            Query.Where(t => !t.IsDeleted);
        }

        Query.OrderByDescending(t => t.CreatedAt);
    }
}

/// <summary>
/// Specification to get templates by stage
/// </summary>
public class TemplatesByStageSpec : Specification<Template>
{
    public TemplatesByStageSpec(string stage, bool includeDeleted = false)
    {
        Query.Where(t => t.Stage.ToLower() == stage.ToLower());

        if (!includeDeleted)
        {
            Query.Where(t => !t.IsDeleted);
        }

        Query.OrderByDescending(t => t.CreatedAt);
    }
}
