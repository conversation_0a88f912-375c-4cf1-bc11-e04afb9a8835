using Application.FieldMappings.DTOs;
using Application.FieldMappings.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Queries;

/// <summary>
/// Get field mapping by ID query handler
/// </summary>
public class GetFieldMappingByIdQueryHandler : IRequestHandler<GetFieldMappingByIdQuery, Result<ViewFieldMappingDto>>
{
    private readonly IReadRepository<FieldMapping> _fieldMappingRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetFieldMappingByIdQueryHandler(IReadRepository<FieldMapping> fieldMappingRepository)
    {
        _fieldMappingRepository = fieldMappingRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ViewFieldMappingDto>> Handle(GetFieldMappingByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var spec = new FieldMappingWithDetailsSpec(request.Id);
            var fieldMapping = await _fieldMappingRepository.GetBySpecAsync(spec, cancellationToken);
            
            if (fieldMapping == null)
            {
                return Result<ViewFieldMappingDto>.Failure("Field mapping not found.");
            }

            var viewDto = new ViewFieldMappingDto
            {
                Id = fieldMapping.Id,
                IntegrationId = fieldMapping.IntegrationId,
                ApiName = fieldMapping.ApiName,
                SourceField = fieldMapping.SourceField,
                SourceType = fieldMapping.SourceType,
                ObjectMetadataId = fieldMapping.ObjectMetadataId,
                ObjectMetadataKey = fieldMapping.ObjectMetadata?.Metadata?.Name,
                UserId = fieldMapping.UserId,
                UserName = fieldMapping.User?.UserName,
                RoleId = fieldMapping.RoleId,
                RoleName = fieldMapping.Role?.Name,
                TargetObjectName = fieldMapping.TargetObjectName,
                Notes = fieldMapping.Notes,
                CreatedAt = fieldMapping.CreatedAt,
                CreatedBy = fieldMapping.CreatedBy,
                ModifiedAt = fieldMapping.ModifiedAt,
                ModifiedBy = fieldMapping.ModifiedBy
            };

            return Result<ViewFieldMappingDto>.Success(viewDto);
        }
        catch (Exception ex)
        {
            return Result<ViewFieldMappingDto>.Failure($"Failed to get field mapping: {ex.Message}");
        }
    }
}
