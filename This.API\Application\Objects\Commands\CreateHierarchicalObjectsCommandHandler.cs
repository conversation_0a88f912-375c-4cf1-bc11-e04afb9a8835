using Application.Objects.DTOs;
using Application.Objects.Services;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Objects.Commands;

/// <summary>
/// Handler for creating hierarchical objects with metadata
/// </summary>
public class CreateHierarchicalObjectsCommandHandler : IRequestHandler<CreateHierarchicalObjectsCommand, Result<HierarchicalObjectCreationResult>>
{
    private readonly IHierarchicalObjectCreationService _hierarchicalObjectService;
    private readonly ILogger<CreateHierarchicalObjectsCommandHandler> _logger;

    public CreateHierarchicalObjectsCommandHandler(
        IHierarchicalObjectCreationService hierarchicalObjectService,
        ILogger<CreateHierarchicalObjectsCommandHandler> logger)
    {
        _hierarchicalObjectService = hierarchicalObjectService;
        _logger = logger;
    }

    public async Task<Result<HierarchicalObjectCreationResult>> Handle(
        CreateHierarchicalObjectsCommand request, 
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing hierarchical object creation for ProductId: {ProductId} with {ObjectCount} root objects",
                request.ProductId, request.Objects?.Count ?? 0);

            var result = await _hierarchicalObjectService.CreateHierarchicalObjectsAsync(
                request.ProductId, 
                request.Objects, 
                cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation("Hierarchical object creation completed successfully for ProductId: {ProductId}. " +
                    "Created: {Objects} objects, {Metadata} metadata, {Values} values in {Time}ms",
                    request.ProductId, result.TotalObjectsCreated, result.TotalMetadataCreated,
                    result.TotalObjectValuesCreated, result.ProcessingTimeMs);

                return Result<HierarchicalObjectCreationResult>.Success(result, result.Message);
            }
            else
            {
                _logger.LogWarning("Hierarchical object creation failed for ProductId: {ProductId}. Errors: {Errors}",
                    request.ProductId, string.Join(", ", result.Errors));

                return Result<HierarchicalObjectCreationResult>.Failure(result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during hierarchical object creation for ProductId: {ProductId}",
                request.ProductId);

            return Result<HierarchicalObjectCreationResult>.Failure("An unexpected error occurred during object creation");
        }
    }
}

/// <summary>
/// Handler for validating hierarchical objects structure
/// </summary>
public class ValidateHierarchicalObjectsQueryHandler : IRequestHandler<ValidateHierarchicalObjectsQuery, Result<HierarchicalObjectValidationResult>>
{
    private readonly IHierarchicalObjectCreationService _hierarchicalObjectService;
    private readonly ILogger<ValidateHierarchicalObjectsQueryHandler> _logger;

    public ValidateHierarchicalObjectsQueryHandler(
        IHierarchicalObjectCreationService hierarchicalObjectService,
        ILogger<ValidateHierarchicalObjectsQueryHandler> logger)
    {
        _hierarchicalObjectService = hierarchicalObjectService;
        _logger = logger;
    }

    public async Task<Result<HierarchicalObjectValidationResult>> Handle(
        ValidateHierarchicalObjectsQuery request, 
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing hierarchical object validation for ProductId: {ProductId}",
                request.ProductId);

            var result = await _hierarchicalObjectService.ValidateHierarchicalObjectsAsync(
                request.ProductId, 
                request.Objects, 
                cancellationToken);

            if (result.IsValid)
            {
                _logger.LogInformation("Hierarchical object validation completed successfully for ProductId: {ProductId}. " +
                    "Objects: {Objects}, MaxDepth: {MaxDepth}, EstimatedFields: {Fields}",
                    request.ProductId, result.TotalObjects, result.MaxDepth, result.EstimatedMetadataFields);

                return Result<HierarchicalObjectValidationResult>.Success(result, "Validation completed successfully");
            }
            else
            {
                _logger.LogWarning("Hierarchical object validation failed for ProductId: {ProductId}. Errors: {Errors}",
                    request.ProductId, string.Join(", ", result.Errors));

                return Result<HierarchicalObjectValidationResult>.Success(result, "Validation completed with errors");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during hierarchical object validation for ProductId: {ProductId}",
                request.ProductId);

            return Result<HierarchicalObjectValidationResult>.Failure("An unexpected error occurred during validation");
        }
    }
}
