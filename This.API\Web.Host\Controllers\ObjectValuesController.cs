using Application.Objects.Commands.UpsertObjectWithMetadata;
using Application.ObjectValues.Commands.UpdateSingleValue;
using Application.ObjectValues.Commands.UpdateSingleValueByRefId;
using Application.ObjectValues.Commands.UpsertBulk;
using Application.ObjectValues.Commands.UpsertSingle;
using Application.ObjectValues.Queries.GetMetadataKeyValues;
using Application.ObjectValues.Queries.GetObjectInstanceView;
using Application.ObjectValues.Queries.GetObjectInstanceViewByRefId;
using Infrastructure.OpenApi;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Shared;

namespace Web.Host.Controllers;

/// <summary>
/// API controller for ObjectValues operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ObjectValuesController : BaseApiController
{
    private readonly IMediator _mediator;
    private readonly ILogger<ObjectValuesController> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValuesController(IMediator mediator, ILogger<ObjectValuesController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Validate tenant header
    /// </summary>
    private IActionResult ValidateTenantHeader()
    {
        if (!Request.Headers.ContainsKey("tenant"))
        {
            return BadRequest("Tenant header is required");
        }
        return null!;
    }

    /// <summary>
    /// Get tenant ID from header
    /// </summary>
    private string GetTenantIdFromHeader()
    {
        return Request.Headers["tenant"].FirstOrDefault() ?? string.Empty;
    }

    /// <summary>
    /// Update the Value field for a specific ObjectValue record
    /// </summary>
    [HttpPatch("single-value-update")]
    [TenantIdHeader]
    public async Task<IActionResult> SingleValueUpdate([FromBody] SingleValueUpdateRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new UpdateObjectValueCommand(request.Id, request.Value);
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ObjectValue with Id: {Id}", request.Id);
            return StatusCode(500, "An error occurred while updating ObjectValue");
        }
    }

    /// <summary>
    /// Update the Value field for a specific ObjectValue record by RefId and Name
    /// </summary>
    [HttpPatch("single-value-update-by-refid")]
    [TenantIdHeader]
    public async Task<IActionResult> SingleValueUpdateByRefId([FromBody] SingleValueUpdateByRefIdRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var tenantId = GetTenantIdFromHeader();
            if (string.IsNullOrEmpty(tenantId))
            {
                return BadRequest("Tenant ID is required");
            }

            if (string.IsNullOrEmpty(request.MetadataKey))
            {
                return BadRequest("MetadataKey is required");
            }

            var command = new UpdateObjectValueByRefIdCommand(request.RefId, request.MetadataKey, request.Value, tenantId);
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ObjectValue with RefId: {RefId}, MetadataKey: {MetadataKey}",
                request.RefId, request.MetadataKey);
            return StatusCode(500, "An error occurred while updating ObjectValue by RefId");
        }
    }

    /// <summary>
    /// Upsert a single ObjectValue (one metadata field for an object instance)
    /// </summary>
    [HttpPost("upsert-single")]
    [TenantIdHeader]
    public async Task<IActionResult> UpsertSingle([FromBody] UpsertSingleObjectValueCommand command)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting ObjectValue instance with RefId: {RefId}", command.RefId);
            return StatusCode(500, "An error occurred while upserting ObjectValue");
        }
    }

    /// <summary>
    /// Upsert multiple ObjectValues (complete object instance with all metadata values)
    /// </summary>
    [HttpPost("upsert-bulk")]
    [TenantIdHeader]
    public async Task<IActionResult> UpsertBulk([FromBody] UpsertBulkObjectValueCommand command)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk upsert of ObjectValues");
            return StatusCode(500, "An error occurred during bulk upsert of ObjectValues");
        }
    }

    /// <summary>
    /// Upsert ObjectValues with metadata in flat structure format (single object instance)
    /// Accepts ObjectId, RefId, ParentObjectValueId and metadata properties in a single dictionary
    /// </summary>
    [HttpPost("upsert-single-with-metadata")]
    [TenantIdHeader]
    public async Task<IActionResult> UpsertSingleWithMetadata([FromBody] ObjectUpsertWithMetadataRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new UpsertObjectWithMetadataCommand
            {
                MetadataProperties = request.MetadataProperties
            };

            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting ObjectValues with metadata in flat structure");
            return StatusCode(500, "An error occurred while upserting ObjectValues with metadata");
        }
    }

    /// <summary>
    /// Upsert multiple ObjectValues with metadata in flat structure format (bulk operation)
    /// Each object should contain ObjectId, RefId, ParentObjectValueId and metadata properties
    /// </summary>
    [HttpPost("upsert-bulk-with-metadata")]
    [TenantIdHeader]
    public async Task<IActionResult> UpsertBulkWithMetadata([FromBody] ObjectUpsertWithMetadataBulkRequest request)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new UpsertObjectWithMetadataBulkCommand
            {
                Objects = request.Objects.Select(o => o.MetadataProperties).ToList(),
                BatchSize = request.BatchSize
            };

            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk upserting ObjectValues with metadata in flat structure");
            return StatusCode(500, "An error occurred while bulk upserting ObjectValues with metadata");
        }
    }

    /// <summary>
    /// Get specific Object instance by RefId from view
    /// </summary>
    [HttpGet("instance-view-by-refid/{refId}")]
    [TenantIdHeader]
    public async Task<IActionResult> GetObjectInstanceViewByRefId(Guid refId, [FromQuery] string viewName)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var tenantId = GetTenantIdFromHeader();
            if (string.IsNullOrEmpty(tenantId))
            {
                return BadRequest("Tenant ID is required");
            }

            if (string.IsNullOrEmpty(viewName))
            {
                return BadRequest("View name is required");
            }

            var query = new GetObjectInstanceViewByRefIdQuery(refId, viewName, tenantId);
            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting object instance by RefId: {RefId} from view: {ViewName}", refId, viewName);
            return StatusCode(500, "An error occurred while getting object instance by RefId");
        }
    }

    /// <summary>
    /// Get Object instance view using stored procedure (creates dynamic view with metadata as columns) with pagination
    /// </summary>
    [HttpGet("instances-view/{objectName}")]
    [TenantIdHeader]
    public async Task<IActionResult> GetObjectInstanceView(
        string objectName,
        [FromQuery] bool createView = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var tenantId = GetTenantIdFromHeader();
            if (string.IsNullOrEmpty(tenantId))
            {
                return BadRequest("Tenant ID is required");
            }

            // Validate pagination parameters
            if (pageNumber < 1) pageNumber = 1;
            if (pageSize < 1) pageSize = 10;
            if (pageSize > 100) pageSize = 100; // Limit max page size

            var query = new GetObjectInstanceViewQuery(objectName, tenantId, createView, pageNumber, pageSize);
            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting object instance view for Object: {ObjectName}, Page: {PageNumber}, PageSize: {PageSize}",
                objectName, pageNumber, pageSize);
            return StatusCode(500, "An error occurred while getting object instance view");
        }
    }

    /// <summary>
    /// Get all values for a specific metadata key from object instance view with pagination
    /// </summary>
    [HttpGet("metadata-key-values/{objectName}/{metadataKey}")]
    [TenantIdHeader]
    public async Task<IActionResult> GetMetadataKeyValues(
        string objectName,
        string metadataKey,
        [FromQuery] bool createView = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var tenantId = GetTenantIdFromHeader();
            if (string.IsNullOrEmpty(tenantId))
            {
                return BadRequest("Tenant ID is required");
            }

            if (string.IsNullOrEmpty(objectName))
            {
                return BadRequest("Object name is required");
            }

            if (string.IsNullOrEmpty(metadataKey))
            {
                return BadRequest("Metadata key is required");
            }

            // Validate pagination parameters
            if (pageNumber < 1) pageNumber = 1;
            if (pageSize < 1) pageSize = 10;
            if (pageSize > 100) pageSize = 100; // Limit max page size

            var query = new GetMetadataKeyValuesQuery(objectName, metadataKey, tenantId, createView, pageNumber, pageSize);
            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metadata key values for Object: {ObjectName}, MetadataKey: {MetadataKey}, Page: {PageNumber}, PageSize: {PageSize}",
                objectName, metadataKey, pageNumber, pageSize);
            return StatusCode(500, "An error occurred while getting metadata key values");
        }
    }
}
