using Abstraction.Database.Repositories;
using Application.MetadataManagement.DTOs;
using Application.MetadataManagement.Specifications;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Commands;

/// <summary>
/// Create Metadata command handler
/// </summary>
public class CreateMetadataCommandHandler : IRequestHandler<CreateMetadataCommand, Result<MetadataDto>>
{
    private readonly IRepository<Domain.Entities.Metadata> _repository;
    private readonly IRepository<DataType> _dataTypeRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateMetadataCommandHandler(
        IRepository<Domain.Entities.Metadata> repository,
        IRepository<DataType> dataTypeRepository)
    {
        _repository = repository;
        _dataTypeRepository = dataTypeRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<MetadataDto>> Handle(CreateMetadataCommand request, CancellationToken cancellationToken)
    {
        // Validate DataType exists
        var dataType = await _dataTypeRepository.GetByIdAsync(request.DataTypeId, cancellationToken);
        if (dataType == null)
        {
            return Result<MetadataDto>.Failure($"DataType with ID '{request.DataTypeId}' not found.");
        }

        // Check if Metadata with same key already exists
        var existingMetadata = await _repository.GetBySpecAsync(new MetadataByKeySpec(request.MetadataKey), cancellationToken);
        if (existingMetadata != null)
        {
            return Result<MetadataDto>.Failure($"Metadata with key '{request.MetadataKey}' already exists.");
        }

        // Create new Metadata
        var metadata = new Domain.Entities.Metadata
        {
            Name = request.MetadataKey, // Updated: Name maps to Name property
            DataTypeId = request.DataTypeId,
            ValidationPattern = request.ValidationPattern, // Updated property name
            MinLength = request.MinLength, // Updated property name
            MaxLength = request.MaxLength, // Updated property name
            MinValue = request.MinValue, // Updated property name
            MaxValue = request.MaxValue, // Updated property name
            IsRequired = request.IsRequired, // Updated property name
            Placeholder = request.Placeholder, // Updated property name
            DefaultOptions = request.DefaultOptions, // Updated property name
            MaxSelections = request.MaxSelections, // Updated property name
            AllowedFileTypes = request.AllowedFileTypes, // Updated property name
            MaxFileSize = request.MaxFileSize, // Updated property name
            ErrorMessage = request.ErrorMessage, // Updated property name
            DisplayLabel = request.DisplayLabel,
            HelpText = request.HelpText,
            FieldOrder = request.FieldOrder,
            IsVisible = request.IsVisible,
            IsReadonly = request.IsReadonly
        };

        var createdMetadata = await _repository.AddAsync(metadata, cancellationToken);

        var dto = new MetadataDto
        {
            Id = createdMetadata.Id,
            Name = createdMetadata.Name, // Updated: Name property maps to Name
            DataTypeId = createdMetadata.DataTypeId,
            DataTypeName = dataType.Name,
            ValidationPattern = createdMetadata.ValidationPattern, // Updated property name
            MinLength = createdMetadata.MinLength, // Updated property name
            MaxLength = createdMetadata.MaxLength, // Updated property name
            MinValue = createdMetadata.MinValue, // Updated property name
            MaxValue = createdMetadata.MaxValue, // Updated property name
            IsRequired = createdMetadata.IsRequired, // Updated property name
            Placeholder = createdMetadata.Placeholder, // Updated property name
            DefaultOptions = createdMetadata.DefaultOptions, // Updated property name
            MaxSelections = createdMetadata.MaxSelections, // Updated property name
            AllowedFileTypes = createdMetadata.AllowedFileTypes, // Updated property name
            MaxFileSize = createdMetadata.MaxFileSize, // Updated property name
            ErrorMessage = createdMetadata.ErrorMessage, // Updated property name
            DisplayLabel = createdMetadata.DisplayLabel,
            HelpText = createdMetadata.HelpText,
            FieldOrder = createdMetadata.FieldOrder,
            IsVisible = createdMetadata.IsVisible,
            IsReadonly = createdMetadata.IsReadonly,
            CreatedAt = createdMetadata.CreatedAt,
            CreatedBy = createdMetadata.CreatedBy ?? Guid.Empty,
            ModifiedAt = createdMetadata.ModifiedAt,
            ModifiedBy = createdMetadata.ModifiedBy
        };

        return Result<MetadataDto>.Success(dto);
    }
}
