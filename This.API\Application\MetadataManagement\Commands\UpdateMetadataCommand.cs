using Application.MetadataManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Commands;

/// <summary>
/// Update Metadata command
/// </summary>
public class UpdateMetadataCommand : IRequest<Result<MetadataDto>>
{
    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Metadata key
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// Data type ID
    /// </summary>
    public Guid DataTypeId { get; set; }

    /// <summary>
    /// Validation pattern
    /// </summary>
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// Minimum length
    /// </summary>
    public int? MinLength { get; set; }

    /// <summary>
    /// Maximum length
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Minimum value
    /// </summary>
    public decimal? MinValue { get; set; }

    /// <summary>
    /// Maximum value
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// Is required
    /// </summary>
    public bool? IsRequired { get; set; }

    /// <summary>
    /// Placeholder
    /// </summary>
    public string? Placeholder { get; set; }

    /// <summary>
    /// Options
    /// </summary>
    public string? DefaultOptions { get; set; }

    /// <summary>
    /// Max selections
    /// </summary>
    public int? MaxSelections { get; set; }

    /// <summary>
    /// Allowed file types
    /// </summary>
    public string? AllowedFileTypes { get; set; }

    /// <summary>
    /// Max file size
    /// </summary>
    public long? MaxFileSize { get; set; }

    /// <summary>
    /// Error message
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Display label
    /// </summary>
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// Help text
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// Field order
    /// </summary>
    public int? FieldOrder { get; set; }

    /// <summary>
    /// Is visible
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// Is readonly
    /// </summary>
    public bool IsReadonly { get; set; } = false;
}
