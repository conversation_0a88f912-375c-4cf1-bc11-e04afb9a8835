using Application.UserValues.DTOs;
using Application.MetadataManagement.Specifications;
using Application.UserMetadataManagement.Specifications;
using Application.DataTypes.Specifications;
using Abstraction.Database.Repositories;
using Abstraction.Common;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;
using System.Text.Json;

namespace Application.UserValues.Commands;

/// <summary>
/// Handler for CreateUserDataCommand
/// </summary>
public class CreateUserDataCommandHandler : IRequestHandler<CreateUserDataCommand, Result<UserDataResponseDto>>
{
    private readonly IRepository<Metadata> _metadataRepository;
    private readonly IRepository<Domain.Entities.UserMetadata> _userMetadataRepository;
    private readonly IRepository<Domain.Entities.UserValue> _userValueRepository;
    private readonly IRepository<User> _userRepository;
    private readonly IRepository<DataType> _dataTypeRepository;
    private readonly ICurrentUser _currentUser;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateUserDataCommandHandler(
        IRepository<Metadata> metadataRepository,
        IRepository<Domain.Entities.UserMetadata> userMetadataRepository,
        IRepository<Domain.Entities.UserValue> userValueRepository,
        IRepository<User> userRepository,
        IRepository<DataType> dataTypeRepository,
        ICurrentUser currentUser)
    {
        _metadataRepository = metadataRepository;
        _userMetadataRepository = userMetadataRepository;
        _userValueRepository = userValueRepository;
        _userRepository = userRepository;
        _dataTypeRepository = dataTypeRepository;
        _currentUser = currentUser;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<UserDataResponseDto>> Handle(CreateUserDataCommand request, CancellationToken cancellationToken)
    {
        // Verify user exists
        var user = await _userRepository.GetByIdAsync(request.UserId, cancellationToken);
        if (user == null)
        {
            return Result<UserDataResponseDto>.Failure($"User with ID '{request.UserId}' not found.");
        }

        var response = new UserDataResponseDto
        {
            UserId = request.UserId
        };

        var currentUserId = _currentUser.GetUserId();
        var refId = Guid.NewGuid(); // Group all values for this user data insert

        // Get default data types
        var textDataType = await GetOrCreateDataType("Text", "Text", "Text", "input");
        var numberDataType = await GetOrCreateDataType("Number", "Number", "Number", "number");
        var booleanDataType = await GetOrCreateDataType("Boolean", "Boolean", "Boolean", "checkbox");
        var jsonDataType = await GetOrCreateDataType("JSON", "JSON", "JSON", "textarea");

        // Process each field
        await ProcessField("UserName", request.UserName, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("FirstName", request.FirstName, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("LastName", request.LastName, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("IsActive", request.IsActive.ToString(), booleanDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("EmailConfirmed", request.EmailConfirmed.ToString(), booleanDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("ImageUrl", request.ImageUrl, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("AltPhoneNumber", request.AltPhoneNumber, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("Address", request.Address, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("Email", request.Email, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("AltEmail", request.AltEmail, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("BloodGroup", request.BloodGroup?.ToString(), numberDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("Gender", request.Gender?.ToString(), numberDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("PermanentAddress", request.PermanentAddress, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("PhoneNumber", request.PhoneNumber, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("ProfileCompletion", request.ProfileCompletion?.ToString(), numberDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("EmpNo", request.EmpNo, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("OfficeName", request.OfficeName, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("OfficeAddress", request.OfficeAddress, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("ReportsTo", request.ReportsTo?.ToString(), textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("GeneralManager", request.GeneralManager?.ToString(), textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("Description", request.Description, textDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("Documents", request.Documents, jsonDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("LeadCount", request.LeadCount?.ToString(), numberDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("IsAutomationEnabled", request.IsAutomationEnabled?.ToString(), booleanDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("ShouldShowTimeZone", request.ShouldShowTimeZone?.ToString(), booleanDataType, request.UserId, refId, response, cancellationToken);
        await ProcessField("LicenseNo", request.LicenseNo, textDataType, request.UserId, refId, response, cancellationToken);

        // Process complex objects
        if (request.Department != null)
        {
            await ProcessField("DepartmentId", request.Department.Id.ToString(), textDataType, request.UserId, refId, response, cancellationToken);
            await ProcessField("DepartmentName", request.Department.Name, textDataType, request.UserId, refId, response, cancellationToken);
        }

        if (request.Designation != null)
        {
            await ProcessField("DesignationId", request.Designation.Id.ToString(), textDataType, request.UserId, refId, response, cancellationToken);
            await ProcessField("DesignationName", request.Designation.Name, textDataType, request.UserId, refId, response, cancellationToken);
        }

        if (request.TimeZoneInfo != null)
        {
            var timeZoneJson = JsonSerializer.Serialize(request.TimeZoneInfo);
            await ProcessField("TimeZoneInfo", timeZoneJson, jsonDataType, request.UserId, refId, response, cancellationToken);
        }

        if (request.UserRoles != null && request.UserRoles.Any())
        {
            var userRolesJson = JsonSerializer.Serialize(request.UserRoles);
            await ProcessField("UserRoles", userRolesJson, jsonDataType, request.UserId, refId, response, cancellationToken);
        }

        if (request.RolePermission != null && request.RolePermission.Any())
        {
            var rolePermissionJson = JsonSerializer.Serialize(request.RolePermission);
            await ProcessField("RolePermission", rolePermissionJson, jsonDataType, request.UserId, refId, response, cancellationToken);
        }

        response.Message = $"Successfully created user data for user {request.UserId}";
        return Result<UserDataResponseDto>.Success(response);
    }

    /// <summary>
    /// Process a single field - create metadata if needed, create user metadata link, and create user value
    /// </summary>
    private async Task ProcessField(string fieldKey, string? fieldValue, DataType dataType, Guid userId, Guid refId, UserDataResponseDto response, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(fieldValue))
            return;

        // Check if metadata exists
        var metadata = await _metadataRepository.GetBySpecAsync(
            new MetadataByKeySpec(fieldKey), cancellationToken);

        if (metadata == null)
        {
            // Create new metadata
            metadata = new Metadata
            {
                Id = Guid.NewGuid(),
                Name = fieldKey, // Updated: Name is now Name property
                DisplayLabel = GetDisplayLabel(fieldKey),
                HelpText = $"User {fieldKey} information",
                DataTypeId = dataType.Id,
                IsVisible = true,
                IsReadonly = false,
                FieldOrder = GetFieldOrder(fieldKey),
                CreatedBy = Guid.TryParse(_currentUser.GetUserId(), out var metadataUserId) ? metadataUserId : null,
                CreatedAt = DateTime.UtcNow
            };

            await _metadataRepository.AddAsync(metadata, cancellationToken);
            response.MetadataCreated++;
            response.CreatedMetadataKeys.Add(fieldKey);
        }

        // Check if user metadata link exists
        var userMetadata = await _userMetadataRepository.GetBySpecAsync(
            new UserMetadataByUserAndMetadataSpec(userId, metadata.Id), cancellationToken);

        if (userMetadata == null)
        {
            // Create user metadata link
            userMetadata = new Domain.Entities.UserMetadata
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                MetadataId = metadata.Id,
                IsUnique = IsUniqueField(fieldKey),
                IsActive = true,
                IsVisibleInList = ShouldBeVisibleInList(fieldKey),
                IsVisibleInEdit = true,
                IsVisibleInCreate = true,
                IsVisibleInView = true,
                IsCalculated = false,
                CreatedBy = Guid.TryParse(_currentUser.GetUserId(), out var userMetaUserId) ? userMetaUserId : null,
                CreatedAt = DateTime.UtcNow
            };

            await _userMetadataRepository.AddAsync(userMetadata, cancellationToken);
            response.UserMetadataCreated++;
        }

        // Create user value
        var userValue = new Domain.Entities.UserValue
        {
            Id = Guid.NewGuid(),
            UserMetadataId = userMetadata.Id,
            RefId = refId,
            Value = fieldValue,
            CreatedBy = Guid.TryParse(_currentUser.GetUserId(), out var userValueUserId) ? userValueUserId : null,
            CreatedAt = DateTime.UtcNow
        };

        await _userValueRepository.AddAsync(userValue, cancellationToken);
        response.UserValuesCreated++;
    }

    /// <summary>
    /// Get or create data type
    /// </summary>
    private async Task<DataType> GetOrCreateDataType(string name, string displayName, string category, string uiComponent)
    {
        var dataType = await _dataTypeRepository.GetBySpecAsync(
            new DataTypeByNameSpec(name), CancellationToken.None);

        if (dataType == null)
        {
            dataType = new DataType
            {
                Id = Guid.NewGuid(),
                Name = name,
                DisplayName = displayName,
                Category = category,
                UiComponent = uiComponent,
                CreatedBy = Guid.TryParse(_currentUser.GetUserId(), out var dataTypeUserId) ? dataTypeUserId : null,
                CreatedAt = DateTime.UtcNow
            };

            await _dataTypeRepository.AddAsync(dataType, CancellationToken.None);
        }

        return dataType;
    }

    /// <summary>
    /// Get display label for field
    /// </summary>
    private static string GetDisplayLabel(string fieldKey)
    {
        return fieldKey switch
        {
            "UserName" => "User Name",
            "FirstName" => "First Name",
            "LastName" => "Last Name",
            "IsActive" => "Is Active",
            "EmailConfirmed" => "Email Confirmed",
            "ImageUrl" => "Profile Image",
            "AltPhoneNumber" => "Alternative Phone",
            "AltEmail" => "Alternative Email",
            "BloodGroup" => "Blood Group",
            "PermanentAddress" => "Permanent Address",
            "PhoneNumber" => "Phone Number",
            "ProfileCompletion" => "Profile Completion %",
            "EmpNo" => "Employee Number",
            "OfficeName" => "Office Name",
            "OfficeAddress" => "Office Address",
            "ReportsTo" => "Reports To",
            "GeneralManager" => "General Manager",
            "LeadCount" => "Lead Count",
            "IsAutomationEnabled" => "Automation Enabled",
            "ShouldShowTimeZone" => "Show Time Zone",
            "LicenseNo" => "License Number",
            "DepartmentId" => "Department ID",
            "DepartmentName" => "Department Name",
            "DesignationId" => "Designation ID",
            "DesignationName" => "Designation Name",
            "TimeZoneInfo" => "Time Zone Information",
            "UserRoles" => "User Roles",
            "RolePermission" => "Role Permissions",
            _ => fieldKey
        };
    }

    /// <summary>
    /// Get field order for sorting
    /// </summary>
    private static int GetFieldOrder(string fieldKey)
    {
        return fieldKey switch
        {
            "UserName" => 1,
            "FirstName" => 2,
            "LastName" => 3,
            "Email" => 4,
            "PhoneNumber" => 5,
            "IsActive" => 6,
            "DepartmentName" => 10,
            "DesignationName" => 11,
            "OfficeName" => 12,
            _ => 999
        };
    }

    /// <summary>
    /// Check if field should be unique
    /// </summary>
    private static bool IsUniqueField(string fieldKey)
    {
        return fieldKey switch
        {
            "UserName" => true,
            "Email" => true,
            "EmpNo" => true,
            _ => false
        };
    }

    /// <summary>
    /// Check if field should be visible in list
    /// </summary>
    private static bool ShouldBeVisibleInList(string fieldKey)
    {
        return fieldKey switch
        {
            "UserName" => true,
            "FirstName" => true,
            "LastName" => true,
            "Email" => true,
            "PhoneNumber" => true,
            "DepartmentName" => true,
            "DesignationName" => true,
            "IsActive" => true,
            _ => false
        };
    }
}
