import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/app_theme.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widgets.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'API-Based Widget Components',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.darkThemeMode,
      home: const ApiComponentsTestPage(),
    );
  }
}

class ApiComponentsTestPage extends StatefulWidget {
  const ApiComponentsTestPage({super.key});

  @override
  State<ApiComponentsTestPage> createState() => _ApiComponentsTestPageState();
}

class _ApiComponentsTestPageState extends State<ApiComponentsTestPage> {
  // Form state variables for new components
  String _currencyValue = '';
  String _percentageValue = '';
  String _dropdownValue = '';
  List<String> _multiDropdownValue = [];
  double _sliderValue = 50.0;
  List<SelectedFile> _fileValue = [];
  List<SelectedImage> _imageValue = [];
  List<SelectedVideo> _videoValue = [];

  // Sample dropdown options (simulating API response)
  final List<DropdownOption> _dropdownOptions = [
    const DropdownOption(value: 'option1', label: 'Option 1', description: 'First option'),
    const DropdownOption(value: 'option2', label: 'Option 2', description: 'Second option'),
    const DropdownOption(value: 'option3', label: 'Option 3', description: 'Third option'),
    const DropdownOption(value: 'option4', label: 'Option 4', description: 'Fourth option'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorPalette.primaryDarkColor,
        title: Text(
          'API-Based Widget Components',
          style: LexendTextStyles.lexend16ExtraLight.copyWith(
            color: ColorPalette.white,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Testing API-Configured Components',
              style: LexendTextStyles.lexend18Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 24),

            // Currency Input Section
            _buildSection(
              'Currency Input',
              [
                ThisCurrencyInput(
                  id: 'currency_test',
                  label: 'Amount',
                  value: _currencyValue,
                  onChanged: (value) => setState(() => _currencyValue = value),
                  placeholder: 'Enter amount...',
                  required: true,
                  // API-based parameters
                  validationPattern: r'^[0-9]+(\.[0-9]{1,2})?$',
                  minValue: 0,
                  maxValue: 10000,
                  decimalPlaces: 2,
                  stepValue: 0.01,
                  requiredErrorMessage: 'This field is required',
                  patternErrorMessage: 'Please enter a valid amount',
                  minValueErrorMessage: 'Amount cannot be negative',
                  maxValueErrorMessage: 'Amount is too large',
                  showCurrencySelector: true,
                  helpText: 'Enter a monetary amount with currency selection',
                ),
              ],
            ),

            // Percentage Input Section
            _buildSection(
              'Percentage Input',
              [
                ThisPercentageInput(
                  id: 'percentage_test',
                  label: 'Completion Rate',
                  value: _percentageValue,
                  onChanged: (value) => setState(() => _percentageValue = value),
                  placeholder: 'Enter percentage...',
                  required: true,
                  // API-based parameters
                  validationPattern: r'^[0-9]+(\.[0-9]{1,2})?$',
                  minValue: 0,
                  maxValue: 100,
                  decimalPlaces: 2,
                  stepValue: 0.1,
                  requiredErrorMessage: 'This field is required',
                  patternErrorMessage: 'Please enter a valid percentage',
                  minValueErrorMessage: 'Percentage cannot be negative',
                  maxValueErrorMessage: 'Percentage cannot exceed 100%',
                  showPercentageSymbol: true,
                  allowDecimals: true,
                  helpText: 'Enter completion percentage (0-100%)',
                ),
              ],
            ),

            // Dropdown Input Section
            _buildSection(
              'Dropdown Input',
              [
                ThisDropdownInput(
                  id: 'dropdown_test',
                  label: 'Select Option',
                  options: _dropdownOptions,
                  value: _dropdownValue,
                  onChanged: (value) => setState(() => _dropdownValue = value),
                  placeholder: 'Choose an option...',
                  required: true,
                  // API-based parameters
                  allowsMultiple: false,
                  allowsCustomOptions: false,
                  maxSelections: 1,
                  requiredErrorMessage: 'This field is required',
                  searchable: true,
                  helpText: 'Select one option from the dropdown',
                ),
              ],
            ),

            // Multi-Select Dropdown Section
            _buildSection(
              'Multi-Select Dropdown',
              [
                ThisDropdownInput(
                  id: 'multi_dropdown_test',
                  label: 'Select Multiple Options',
                  options: _dropdownOptions,
                  value: _multiDropdownValue,
                  onChanged: (value) => setState(() => _multiDropdownValue = value),
                  placeholder: 'Choose options...',
                  required: false,
                  // API-based parameters
                  allowsMultiple: true,
                  allowsCustomOptions: false,
                  maxSelections: 3,
                  minLength: 1,
                  maxLength: 3,
                  requiredErrorMessage: 'This field is required',
                  minLengthErrorMessage: 'Select at least 1 option',
                  maxLengthErrorMessage: 'Select at most 3 options',
                  searchable: true,
                  helpText: 'Select 1-3 options from the dropdown',
                ),
              ],
            ),

            // Slider Input Section
            _buildSection(
              'Slider Input',
              [
                ThisSliderInput(
                  id: 'slider_test',
                  label: 'Rating',
                  value: _sliderValue,
                  onChanged: (value) => setState(() => _sliderValue = value),
                  required: true,
                  // API-based parameters
                  minValue: 0,
                  maxValue: 100,
                  decimalPlaces: 0,
                  stepValue: 5,
                  requiredErrorMessage: 'This field is required',
                  minValueErrorMessage: 'Value too small',
                  maxValueErrorMessage: 'Value too large',
                  showValue: true,
                  showMinMaxLabels: true,
                  showTicks: true,
                  suffix: ' pts',
                  helpText: 'Rate from 0 to 100 points in steps of 5',
                ),
              ],
            ),

            // File Input Section
            _buildSection(
              'File Upload',
              [
                ThisFileInput(
                  id: 'file_test',
                  label: 'Upload Documents',
                  value: _fileValue,
                  onChanged: (value) => setState(() => _fileValue = value),
                  placeholder: 'Choose files...',
                  required: false,
                  // API-based parameters from API response
                  allowsMultiple: true,
                  allowedFileTypes: ['pdf', 'doc', 'docx', 'txt'],
                  maxFileSizeBytes: 10485760, // 10MB
                  maxSelections: 5,
                  requiredErrorMessage: 'This field is required',
                  fileTypeErrorMessage: 'Invalid file type',
                  fileSizeErrorMessage: 'File size exceeds limit',
                  helpText: 'Upload up to 5 documents (PDF, DOC, DOCX, TXT)',
                ),
              ],
            ),

            // Image Input Section
            _buildSection(
              'Image Upload',
              [
                ThisImageInput(
                  id: 'image_test',
                  label: 'Upload Images',
                  value: _imageValue,
                  onChanged: (value) => setState(() => _imageValue = value),
                  placeholder: 'Choose images...',
                  required: false,
                  // API-based parameters
                  allowsMultiple: true,
                  allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif'],
                  maxFileSizeBytes: 5242880, // 5MB
                  maxSelections: 3,
                  maxWidth: 1920,
                  maxHeight: 1080,
                  minWidth: 100,
                  minHeight: 100,
                  requiredErrorMessage: 'This field is required',
                  fileTypeErrorMessage: 'Invalid image type',
                  fileSizeErrorMessage: 'Image size exceeds limit',
                  showPreview: true,
                  allowCamera: true,
                  allowGallery: true,
                  helpText: 'Upload up to 3 images (JPG, PNG, GIF)',
                ),
              ],
            ),

            // Video Input Section
            _buildSection(
              'Video Upload',
              [
                ThisVideoInput(
                  id: 'video_test',
                  label: 'Upload Videos',
                  value: _videoValue,
                  onChanged: (value) => setState(() => _videoValue = value),
                  placeholder: 'Choose videos...',
                  required: false,
                  // API-based parameters
                  allowsMultiple: false,
                  allowedFileTypes: ['mp4', 'mov', 'avi'],
                  maxFileSizeBytes: 104857600, // 100MB
                  maxSelections: 1,
                  maxDuration: const Duration(minutes: 5),
                  minDuration: const Duration(seconds: 10),
                  requiredErrorMessage: 'This field is required',
                  fileTypeErrorMessage: 'Invalid video type',
                  fileSizeErrorMessage: 'Video size exceeds limit',
                  showPreview: true,
                  allowCamera: true,
                  allowGallery: true,
                  helpText: 'Upload one video (MP4, MOV, AVI) - 10s to 5min',
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Summary Section
            _buildSection(
              'API Configuration Summary',
              [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorPalette.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: ColorPalette.green),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '✅ All API-Based Components Loaded Successfully!',
                        style: LexendTextStyles.lexend14Bold.copyWith(
                          color: ColorPalette.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Total Components: 18 input widgets\n'
                        'New Components: Currency, Percentage, Dropdown, Slider, File, Image, Video\n'
                        'API Integration: All components support API-based validation and configuration\n'
                        'Features: Dynamic validation, file handling, multi-selection, real-time feedback',
                        style: LexendTextStyles.lexend12Regular.copyWith(
                          color: ColorPalette.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: LexendTextStyles.lexend16Bold.copyWith(
              color: ColorPalette.green,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: ColorPalette.gray700),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
