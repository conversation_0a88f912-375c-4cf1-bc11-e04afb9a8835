import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/app_theme.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widgets.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Simple Widget Test',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.darkThemeMode,
      home: const SimpleTestPage(),
    );
  }
}

class SimpleTestPage extends StatefulWidget {
  const SimpleTestPage({super.key});

  @override
  State<SimpleTestPage> createState() => _SimpleTestPageState();
}

class _SimpleTestPageState extends State<SimpleTestPage> {
  String _textValue = '';
  String _emailValue = '';
  String _currencyValue = '';
  List<SelectedFile> _fileValue = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorPalette.primaryDarkColor,
        title: Text(
          'Simple Widget Test',
          style: LexendTextStyles.lexend16ExtraLight.copyWith(
            color: ColorPalette.white,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Testing New Components',
              style: LexendTextStyles.lexend18Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 24),

            // Text Input Test
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.gray700),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Text Input Test',
                    style: LexendTextStyles.lexend16Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ThisTextInput(
                    id: 'text_test',
                    label: 'Name',
                    value: _textValue,
                    onChanged: (value) => setState(() => _textValue = value),
                    placeholder: 'Enter your name...',
                    required: true,
                    maxLength: 50,
                    showCharacterCount: true,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),

            // Email Input Test
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.gray700),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Email Input Test',
                    style: LexendTextStyles.lexend16Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ThisEmailInput(
                    id: 'email_test',
                    label: 'Email Address',
                    value: _emailValue,
                    onChanged: (value) => setState(() => _emailValue = value),
                    placeholder: 'Enter your email...',
                    required: true,
                    showValidationIcon: true,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Currency Input Test
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.gray700),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Currency Input Test',
                    style: LexendTextStyles.lexend16Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ThisCurrencyInput(
                    id: 'currency_test',
                    label: 'Amount',
                    value: _currencyValue,
                    onChanged: (value) => setState(() => _currencyValue = value),
                    placeholder: 'Enter amount...',
                    required: true,
                    minValue: 0,
                    maxValue: 10000,
                    decimalPlaces: 2,
                    showCurrencySelector: true,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // File Input Test
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.gray700),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'File Input Test',
                    style: LexendTextStyles.lexend16Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ThisFileInput(
                    id: 'file_test',
                    label: 'Upload Files',
                    value: _fileValue,
                    onChanged: (value) => setState(() => _fileValue = value),
                    placeholder: 'Choose files...',
                    allowsMultiple: true,
                    allowedFileTypes: ['pdf', 'doc', 'txt'],
                    maxFileSizeBytes: 10485760, // 10MB
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Status
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.green),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ New Components Working!',
                    style: LexendTextStyles.lexend14Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Text: ${_textValue.isEmpty ? "Empty" : _textValue}\n'
                    'Email: ${_emailValue.isEmpty ? "Empty" : _emailValue}\n'
                    'Currency: ${_currencyValue.isEmpty ? "Empty" : _currencyValue}\n'
                    'Files: ${_fileValue.length} selected',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
