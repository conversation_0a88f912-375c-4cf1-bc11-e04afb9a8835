using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Global metadata field definitions for all entity types
/// </summary>
public class Metadata : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Metadata name (not unique)
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Reference to the data type
    /// </summary>
    public Guid DataTypeId { get; set; }

    // Customer-specific validation overrides
    /// <summary>
    /// Custom validation pattern override
    /// </summary>
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// Custom minimum length override
    /// </summary>
    public int? MinLength { get; set; }

    /// <summary>
    /// Custom maximum length override
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Custom minimum value override
    /// </summary>
    public decimal? MinValue { get; set; }

    /// <summary>
    /// Custom maximum value override
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// Custom required field override
    /// </summary>
    public bool? IsRequired { get; set; }

    // Customer-specific UI overrides
    /// <summary>
    /// Custom placeholder text override
    /// </summary>
    public string? Placeholder { get; set; }

    /// <summary>
    /// Custom maximum selections override
    /// </summary>
    public int? MaxSelections { get; set; }

    /// <summary>
    /// Custom allowed file types override
    /// </summary>
    public string? AllowedFileTypes { get; set; }

    /// <summary>
    /// Custom maximum file size override
    /// </summary>
    public long? MaxFileSize { get; set; }

    // Customer-specific error messages
    /// <summary>
    /// Custom error message override
    /// </summary>
    public string? ErrorMessage { get; set; }

    // UI Display Properties
    /// <summary>
    /// Display label for the field
    /// </summary>
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// Help text for the field
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// Order of the field in forms
    /// </summary>
    public int? FieldOrder { get; set; }

    /// <summary>
    /// Whether the field is visible
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// Whether the field is readonly
    /// </summary>
    public bool IsReadonly { get; set; } = false;

    /// <summary>
    /// Error message shown when a required field is not provided
    /// </summary>
    public string? RequiredErrorMessage { get; set; }

    /// <summary>
    /// Error message shown when input does not match a specified pattern
    /// </summary>
    public string? PatternErrorMessage { get; set; }

    /// <summary>
    /// Error message shown when input is shorter than the minimum allowed length
    /// </summary>
    public string? MinLengthErrorMessage { get; set; }

    /// <summary>
    /// Error message shown when input exceeds the maximum allowed length
    /// </summary>
    public string? MaxLengthErrorMessage { get; set; }

    /// <summary>
    /// Error message shown when the value is less than the minimum allowed value
    /// </summary>
    public string? MinValueErrorMessage { get; set; }

    /// <summary>
    /// Error message shown when the value exceeds the maximum allowed value
    /// </summary>
    public string? MaxValueErrorMessage { get; set; }

    /// <summary>
    /// Error message shown when the uploaded file type is not allowed
    /// </summary>
    public string? FileTypeErrorMessage { get; set; }

    /// <summary>
    /// Maximum allowed file size in bytes (as string)
    /// </summary>
    public string? MaxFileSizeBytes { get; set; }

    /// <summary>
    /// Comma-separated default options for dropdowns or multiple choice fields
    /// </summary>
    public string? DefaultOptions { get; set; }

    /// <summary>
    /// Indicates whether multiple selections are allowed (true/false)
    /// </summary>
    public bool? AllowsMultiple { get; set; }

    /// <summary>
    /// Indicates whether custom user-defined options are allowed (true/false)
    /// </summary>
    public bool? AllowsCustomOptions { get; set; }

    /// <summary>
    /// Type of input field (e.g., text, number, email, date)
    /// </summary>
    public string? InputType { get; set; }

    /// <summary>
    /// Input mask pattern to guide user input formatting
    /// </summary>
    public string? InputMask { get; set; }

    // Lookup Override Properties
    /// <summary>
    /// Override lookup type for this metadata field
    /// </summary>
    public string? OverrideLookupType { get; set; }

    /// <summary>
    /// Override master Context ID for this metadata field
    /// </summary>
    public Guid? OverrideMasterContextId { get; set; }

    /// <summary>
    /// Override TenantContext ID for this metadata field
    /// </summary>
    public Guid? OverrideTenantContextId { get; set; }

    /// <summary>
    /// Override ObjectLookup ID for this metadata field
    /// </summary>
    public Guid? OverrideObjectLookupId { get; set; }

    // Navigation Properties
    /// <summary>
    /// Data type definition
    /// </summary>
    public virtual DataType DataType { get; set; } = null!;
    /// <summary>
    /// Tenant info metadata links
    /// </summary>
    public virtual ICollection<TenantInfoMetadata> TenantInfoMetadata { get; set; } = new List<TenantInfoMetadata>();

    /// <summary>
    /// Product metadata links
    /// </summary>
    public virtual ICollection<ProductMetadata> ProductMetadata { get; set; } = new List<ProductMetadata>();

    /// <summary>
    /// Role metadata links
    /// </summary>
    public virtual ICollection<RoleMetadata> RoleMetadata { get; set; } = new List<RoleMetadata>();

    /// <summary>
    /// User metadata links
    /// </summary>
    public virtual ICollection<UserMetadata> UserMetadata { get; set; } = new List<UserMetadata>();

    /// <summary>
    /// Object metadata links
    /// </summary>
    public virtual ICollection<ObjectMetadata> ObjectMetadata { get; set; } = new List<ObjectMetadata>();

    /// <summary>
    /// Subscription metadata links
    /// </summary>
    public virtual ICollection<SubscriptionMetadata> SubscriptionMetadata { get; set; } = new List<SubscriptionMetadata>();

    /// <summary>
    /// Navigation property to override master Context
    /// </summary>
    public virtual Context? OverrideMasterContext { get; set; }

    /// <summary>
    /// Navigation property to override TenantContext
    /// </summary>
    public virtual TenantContext? OverrideTenantContext { get; set; }

    /// <summary>
    /// Navigation property to override ObjectLookup
    /// </summary>
    public virtual ObjectLookup? OverrideObjectLookup { get; set; }
}
