using MediatR;
using Shared.Common.Response;

namespace Application.Templates.Commands;

/// <summary>
/// Command to delete a template (soft delete)
/// </summary>
public class DeleteTemplateCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteTemplateCommand(Guid id)
    {
        Id = id;
    }
}
