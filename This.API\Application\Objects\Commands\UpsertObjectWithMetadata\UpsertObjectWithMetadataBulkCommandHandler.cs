using Abstraction.Common;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using Shared.Common.Response;

namespace Application.Objects.Commands.UpsertObjectWithMetadata;

/// <summary>
/// Handler for UpsertObjectWithMetadataBulkCommand
/// </summary>
public class UpsertObjectWithMetadataBulkCommandHandler : IRequestHandler<UpsertObjectWithMetadataBulkCommand, Result<UpsertObjectWithMetadataBulkResponse>>
{
    private readonly IMediator _mediator;
    private readonly ILogger<UpsertObjectWithMetadataBulkCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertObjectWithMetadataBulkCommandHandler(
        IMediator mediator,
        ILogger<UpsertObjectWithMetadataBulkCommandHandler> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Handle the bulk command
    /// </summary>
    public async Task<Result<UpsertObjectWithMetadataBulkResponse>> Handle(UpsertObjectWithMetadataBulkCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting bulk upsert of {Count} objects with metadata", request.Objects.Count);

            var response = new UpsertObjectWithMetadataBulkResponse
            {
                TotalObjectsProcessed = request.Objects.Count
            };

            // Process objects in batches
            var batchSize = Math.Min(request.BatchSize, 1000); // Cap at 1000
            var batches = request.Objects
                .Select((obj, index) => new { Object = obj, Index = index })
                .GroupBy(x => x.Index / batchSize)
                .Select(g => g.ToList())
                .ToList();

            _logger.LogInformation("Processing {BatchCount} batches with batch size {BatchSize}", batches.Count, batchSize);

            foreach (var batch in batches)
            {
                _logger.LogInformation("Processing batch with {Count} objects", batch.Count);
                await ProcessBatch(batch.Cast<dynamic>().ToList(), response, cancellationToken);
                _logger.LogInformation("Completed batch. Successful: {Successful}, Failed: {Failed}",
                    response.SuccessfulObjects.Count, response.FailedObjectsCount);
            }

            // Calculate totals
            response.TotalMetadataProcessed = response.SuccessfulObjects.Sum(o => o.TotalProcessed);
            response.TotalInsertedCount = response.SuccessfulObjects.Sum(o => o.InsertedCount);
            response.TotalUpdatedCount = response.SuccessfulObjects.Sum(o => o.UpdatedCount);
            response.TotalSkippedCount = response.SuccessfulObjects.Sum(o => o.SkippedCount);

            stopwatch.Stop();
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Completed bulk upsert. Total: {Total}, Successful: {Successful}, Failed: {Failed}, Time: {Time}ms",
                response.TotalObjectsProcessed, 
                response.SuccessfulObjects.Count, 
                response.FailedObjectsCount, 
                response.ProcessingTimeMs);

            return Result<UpsertObjectWithMetadataBulkResponse>.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error during bulk upsert of objects with metadata");
            return Result<UpsertObjectWithMetadataBulkResponse>.Failure($"Bulk upsert failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Process a batch of objects sequentially to avoid DbContext threading issues
    /// </summary>
    private async Task ProcessBatch(
        List<dynamic> batch,
        UpsertObjectWithMetadataBulkResponse response,
        CancellationToken cancellationToken)
    {
        // Process objects sequentially to avoid DbContext concurrency issues
        foreach (var item in batch)
        {
            var objectData = (Dictionary<string, object?>)item.Object;
            var objectIndex = (int)item.Index;

            try
            {
                var command = new UpsertObjectWithMetadataCommand
                {
                    MetadataProperties = objectData
                };

                var result = await _mediator.Send(command, cancellationToken);

                if (result.Succeeded && result.Data != null)
                {
                    response.SuccessfulObjects.Add(result.Data);
                }
                else
                {
                    response.FailedObjectsCount++;
                    response.FailedObjects.Add(new FailedObjectInfo
                    {
                        ObjectIndex = objectIndex,
                        ErrorMessage = result.Message ?? "Unknown error",
                        OriginalData = objectData
                    });
                }
            }
            catch (Exception ex)
            {
                response.FailedObjectsCount++;
                response.FailedObjects.Add(new FailedObjectInfo
                {
                    ObjectIndex = objectIndex,
                    ErrorMessage = $"An error occurred: {ex.Message}",
                    OriginalData = objectData
                });
                _logger.LogError(ex, "Error processing object at index {Index}", objectIndex);
            }
        }
    }
}
