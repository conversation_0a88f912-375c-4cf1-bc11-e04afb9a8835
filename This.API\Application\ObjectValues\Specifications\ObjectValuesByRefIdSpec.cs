using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get ObjectValues by RefId with tenant isolation
/// </summary>
public class ObjectValuesByRefIdSpec : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValuesByRefIdSpec(Guid refId, string tenantId, bool includeInactive = false)
    {
        Query.Where(ov => ov.RefId == refId && !ov.IsDeleted);

        // Include all related data
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object)
             .ThenInclude(o => o.ParentObject);

        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        // Active filters (tenant isolation handled by multi-tenant framework)
        Query.Where(ov => !ov.ObjectMetadata.Object.IsDeleted &&
                         ov.ObjectMetadata.IsActive &&
                         !ov.ObjectMetadata.IsDeleted);

        // Include inactive objects if requested
        if (!includeInactive)
        {
            Query.Where(ov => ov.ObjectMetadata.Object.IsActive);
        }

        // Order by field order and metadata key
        Query.OrderBy(ov => ov.ObjectMetadata.Metadata.FieldOrder ?? int.MaxValue)
             .ThenBy(ov => ov.ObjectMetadata.Metadata.Name);
    }
}
