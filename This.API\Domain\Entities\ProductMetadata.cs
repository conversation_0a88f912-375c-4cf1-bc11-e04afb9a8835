using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links Product types to their metadata with IsUnique support
/// </summary>
public class ProductMetadata : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the product
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInList { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInEdit { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInCreate { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInView { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsCalculated { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Product
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// Product values
    /// </summary>
    public virtual ICollection<ProductValue> ProductValues { get; set; } = new List<ProductValue>();
}
