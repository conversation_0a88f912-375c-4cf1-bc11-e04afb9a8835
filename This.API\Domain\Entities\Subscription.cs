using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Subscription entity - stores subscription types/templates
/// </summary>
public class Subscription : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Product ID this subscription is for
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Subscription type
    /// </summary>
    public string SubscriptionType { get; set; } = "standard";

    /// <summary>
    /// Subscription status
    /// </summary>
    public string Status { get; set; } = "active";

    /// <summary>
    /// Subscription start date
    /// </summary>
    public DateTime StartDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Subscription end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Whether auto-renewal is enabled
    /// </summary>
    public bool AutoRenew { get; set; } = true;

    /// <summary>
    /// Pricing tier
    /// </summary>
    public string? PricingTier { get; set; }

    // Navigation properties
    /// <summary>
    /// Product this subscription is for
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// Subscription metadata links
    /// </summary>
    public virtual ICollection<SubscriptionMetadata> SubscriptionMetadata { get; set; } = new List<SubscriptionMetadata>();
}
