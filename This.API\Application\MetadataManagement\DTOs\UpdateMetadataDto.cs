using System.ComponentModel.DataAnnotations;

namespace Application.MetadataManagement.DTOs;

/// <summary>
/// Update Metadata DTO
/// </summary>
public class UpdateMetadataDto
{
    /// <summary>
    /// Unique metadata key
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Metada<PERSON><PERSON>ey { get; set; } = string.Empty;

    /// <summary>
    /// Reference to the data type
    /// </summary>
    [Required]
    public Guid DataTypeId { get; set; }

    /// <summary>
    /// Validation pattern override
    /// </summary>
    [MaxLength(500)]
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// Minimum length override
    /// </summary>
    public int? MinLength { get; set; }

    /// <summary>
    /// Maximum length override
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Minimum value override
    /// </summary>
    public decimal? MinValue { get; set; }

    /// <summary>
    /// Maximum value override
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// Required field override
    /// </summary>
    public bool? IsRequired { get; set; }

    /// <summary>
    /// Placeholder text override
    /// </summary>
    [MaxLength(255)]
    public string? Placeholder { get; set; }

    /// <summary>
    /// Options override
    /// </summary>
    public string? DefaultOptions { get; set; }

    /// <summary>
    /// Maximum selections override
    /// </summary>
    public int? MaxSelections { get; set; }

    /// <summary>
    /// Allowed file types override
    /// </summary>
    [MaxLength(500)]
    public string? AllowedFileTypes { get; set; }

    /// <summary>
    /// Maximum file size override
    /// </summary>
    public long? MaxFileSize { get; set; }

    /// <summary>
    /// Error message override
    /// </summary>
    [MaxLength(255)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Display label for the field
    /// </summary>
    [MaxLength(255)]
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// Help text for the field
    /// </summary>
    [MaxLength(500)]
    public string? HelpText { get; set; }

    /// <summary>
    /// Order of the field in forms
    /// </summary>
    public int? FieldOrder { get; set; }

    /// <summary>
    /// Whether the field is visible
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// Whether the field is readonly
    /// </summary>
    public bool IsReadonly { get; set; } = false;
}
