using Abstraction.Database.Repositories;
using Application.Templates.DTOs;
using Application.Templates.Specifications;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Templates.Queries;

/// <summary>
/// Handler for GetTemplatesQuery
/// </summary>
public class GetTemplatesQueryHandler : IRequestHandler<GetTemplatesQuery, PaginatedResult<TemplateSummaryDto>>
{
    private readonly IReadRepository<Template> _templateRepository;
    private readonly ILogger<GetTemplatesQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetTemplatesQueryHandler(
        IReadRepository<Template> templateRepository,
        ILogger<GetTemplatesQueryHandler> logger)
    {
        _templateRepository = templateRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<TemplateSummaryDto>> Handle(GetTemplatesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting templates with pagination. Page: {PageNumber}, Size: {PageSize}",
                request.PageNumber, request.PageSize);

            // Calculate pagination
            var skip = (request.PageNumber - 1) * request.PageSize;

            // Create specifications for data and count
            var dataSpec = new TemplatesWithFiltersSpec(
                request.SearchTerm,
                request.ProductId,
                request.Stage,
                request.IsActive,
                request.IncludeDeleted,
                skip,
                request.PageSize);

            var countSpec = new TemplatesCountSpec(
                request.SearchTerm,
                request.ProductId,
                request.Stage,
                request.IsActive,
                request.IncludeDeleted);

            // Get data and count using specifications
            var templates = await _templateRepository.ListAsync(dataSpec, cancellationToken);
            var totalCount = await _templateRepository.CountAsync(countSpec, cancellationToken);

            // Map to DTOs
            var templateDtos = templates.Select(t => new TemplateSummaryDto
            {
                Id = t.Id,
                ProductId = t.ProductId,
                ProductName = "Unknown", // No relationship with Product table
                Version = t.Version,
                Stage = t.Stage,
                CreatedAt = t.CreatedAt,
                PublishedAt = t.PublishedAt,
                IsActive = t.IsActive
            }).ToList();

            _logger.LogInformation("Retrieved {Count} templates out of {Total}", templateDtos.Count, totalCount);

            return new PaginatedResult<TemplateSummaryDto>(templateDtos, request.PageNumber, request.PageSize, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting templates");
            return PaginatedResult<TemplateSummaryDto>.Failure("Failed to retrieve templates");
        }
    }
}
