import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/app_theme.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widgets.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Full API Integration Demo',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.darkThemeMode,
      home: const FullApiIntegrationPage(),
    );
  }
}

class FullApiIntegrationPage extends StatefulWidget {
  const FullApiIntegrationPage({super.key});

  @override
  State<FullApiIntegrationPage> createState() => _FullApiIntegrationPageState();
}

class _FullApiIntegrationPageState extends State<FullApiIntegrationPage> {
  // Form values storage
  final Map<String, dynamic> _formValues = {};
  final Map<String, List<String>> _validationErrors = {};

  // Sample API response data (simulating your actual API)
  late List<ApiComponentModel> _apiComponents;

  @override
  void initState() {
    super.initState();
    _initializeApiComponents();
  }

  void _initializeApiComponents() {
    // Simulating the API response you provided
    final apiResponseJson = {
      "hasPreviousPage": false,
      "hasNextPage": true,
      "succeeded": true,
      "data": [
        {"id": "dce2def4-bcdd-443d-85b7-fe906750a77e", "name": "address", "displayName": "Address", "category": "Input", "uiComponent": "AddressInput", "validationPattern": null, "minLength": 10, "maxLength": 500, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": null, "minLengthErrorMessage": "Address is too short", "maxLengthErrorMessage": "Address is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": true, "createdAt": "2025-06-13T11:12:12.38625Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedAt": "2025-06-13T11:12:12.38625Z", "modifiedBy": null},
        {"id": "dfbc9788-3ffc-4650-8ed3-673edc4ac86e", "name": "currency", "displayName": "Currency", "category": "Input", "uiComponent": "CurrencyInput", "validationPattern": "^[0-9]+(\\.[0-9]{1,2})?\$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": null, "decimalPlaces": 2, "stepValue": 0.01, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter amount...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Please enter a valid amount", "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": "Amount cannot be negative", "maxValueErrorMessage": "Amount is too large", "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": true, "createdAt": "2025-06-13T11:12:12.38625Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedAt": "2025-06-13T11:12:12.38625Z", "modifiedBy": null},
        {"id": "5c76cf92-55c3-4ceb-a6d7-63d59a3838fd", "name": "email", "displayName": "Email", "category": "Input", "uiComponent": "EmailInput", "validationPattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}\$", "minLength": 5, "maxLength": 320, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "email", "inputMask": null, "placeholder": "Enter email address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Please enter a valid email address", "minLengthErrorMessage": "Email is too short", "maxLengthErrorMessage": "Email is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": true, "createdAt": "2025-06-13T11:12:12.38625Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedAt": "2025-06-13T11:12:12.38625Z", "modifiedBy": null},
        {"id": "5542683d-af98-4d34-8dae-76cc5aae8df0", "name": "file", "displayName": "File", "category": "Upload", "uiComponent": "FileUpload", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "file", "inputMask": null, "placeholder": "Choose file...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": "*", "maxFileSizeBytes": 10485760, "requiredErrorMessage": "This field is required", "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": "Invalid file type", "fileSizeErrorMessage": "File size exceeds limit", "isActive": true, "createdAt": "2025-06-13T11:12:12.38625Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedAt": "2025-06-13T11:12:12.38625Z", "modifiedBy": null}
      ]
    };

    // Parse API response
    final apiResponse = ApiComponentResponse.fromJson(apiResponseJson);
    _apiComponents = apiResponse.data;

    // Initialize form values
    for (final component in _apiComponents) {
      _formValues[component.id] = _getDefaultValue(component);
    }
  }

  dynamic _getDefaultValue(ApiComponentModel component) {
    switch (component.widgetType) {
      case WidgetType.checkbox:
        return <String>[];
      case WidgetType.file:
        return <SelectedFile>[];
      case WidgetType.image:
        return <SelectedImage>[];
      case WidgetType.video:
        return <SelectedVideo>[];
      case WidgetType.slider:
        return component.minValue ?? 0.0;
      default:
        return '';
    }
  }

  void _handleValueChange(String componentId, dynamic value) {
    setState(() {
      _formValues[componentId] = value;
    });
  }

  void _handleValidationChange(String componentId, List<String> errors) {
    setState(() {
      _validationErrors[componentId] = errors;
    });
  }

  Widget _buildWidgetFromConfig(ApiComponentModel component) {
    final value = _formValues[component.id];

    switch (component.widgetType) {
      case WidgetType.text:
        return ThisTextInput(
          id: component.id,
          label: component.label,
          value: value?.toString() ?? '',
          onChanged: (newValue) => _handleValueChange(component.id, newValue),
          onValidation: (errors) => _handleValidationChange(component.id, errors),
          placeholder: component.placeholder,
          required: component.isRequired,
          helpText: component.placeholder,
        );

      case WidgetType.email:
        return ThisEmailInput(
          id: component.id,
          label: component.label,
          value: value?.toString() ?? '',
          onChanged: (newValue) => _handleValueChange(component.id, newValue),
          onValidation: (errors) => _handleValidationChange(component.id, errors),
          placeholder: component.placeholder,
          required: component.isRequired,
          helpText: component.placeholder,
        );

      case WidgetType.currency:
        return ThisCurrencyInput(
          id: component.id,
          label: component.label,
          value: value?.toString() ?? '',
          onChanged: (newValue) => _handleValueChange(component.id, newValue),
          onValidation: (errors) => _handleValidationChange(component.id, errors),
          placeholder: component.placeholder,
          required: component.isRequired,
          helpText: component.placeholder,
          // API parameters that are supported
          validationPattern: component.validationPattern,
          minValue: component.minValue,
          maxValue: component.maxValue,
          decimalPlaces: component.decimalPlaces,
          stepValue: component.stepValue,
          requiredErrorMessage: component.requiredErrorMessage,
          patternErrorMessage: component.patternErrorMessage,
          minValueErrorMessage: component.minValueErrorMessage,
          maxValueErrorMessage: component.maxValueErrorMessage,
        );

      case WidgetType.file:
        return ThisFileInput(
          id: component.id,
          label: component.label,
          value: value is List<SelectedFile> ? value : <SelectedFile>[],
          onChanged: (newValue) => _handleValueChange(component.id, newValue),
          onValidation: (errors) => _handleValidationChange(component.id, errors),
          placeholder: component.placeholder,
          required: component.isRequired,
          helpText: component.placeholder,
          // API parameters that are supported
          allowsMultiple: component.allowsMultiple,
          allowedFileTypes: component.allowedFileTypes,
          maxFileSizeBytes: component.maxFileSizeBytes,
          requiredErrorMessage: component.requiredErrorMessage,
          fileTypeErrorMessage: component.fileTypeErrorMessage,
          fileSizeErrorMessage: component.fileSizeErrorMessage,
        );

      default:
        // Fallback to text input for unsupported types
        return ThisTextInput(
          id: component.id,
          label: component.label,
          value: value?.toString() ?? '',
          onChanged: (newValue) => _handleValueChange(component.id, newValue),
          onValidation: (errors) => _handleValidationChange(component.id, errors),
          placeholder: component.placeholder,
          required: component.isRequired,
          helpText: 'Unsupported widget type: ${component.uiComponent}',
        );
    }
  }

  void _submitForm() {
    // Validate all components
    bool hasErrors = false;
    for (final component in _apiComponents) {
      final errors = _validationErrors[component.id] ?? [];
      if (errors.isNotEmpty) {
        hasErrors = true;
        break;
      }
    }

    if (hasErrors) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix validation errors before submitting'),
          backgroundColor: Color(0xFFC73E1D),
        ),
      );
      return;
    }

    // Show success message with form data
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: ColorPalette.darkToneInk,
        title: Text(
          'Form Submitted Successfully',
          style: LexendTextStyles.lexend16Bold.copyWith(
            color: ColorPalette.green,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Form Data:',
                style: LexendTextStyles.lexend14Bold.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              const SizedBox(height: 8),
              ..._formValues.entries.map((entry) {
                final component = _apiComponents.firstWhere((c) => c.id == entry.key);
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '${component.displayName}: ${entry.value}',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.white,
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Close',
              style: LexendTextStyles.lexend14Regular.copyWith(
                color: ColorPalette.green,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorPalette.primaryDarkColor,
        title: Text(
          'Full API Integration Demo',
          style: LexendTextStyles.lexend16ExtraLight.copyWith(
            color: ColorPalette.white,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.green),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ Complete API Integration',
                    style: LexendTextStyles.lexend16Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'All API parameters implemented:\n'
                    '• Component metadata (id, name, displayName, category, uiComponent)\n'
                    '• Validation rules (pattern, min/max length/value, decimal places)\n'
                    '• Error messages (required, pattern, length, value, file)\n'
                    '• File handling (types, size limits, multiple selection)\n'
                    '• Component state (isActive, created/modified timestamps)\n'
                    '• Dynamic widget creation based on API configuration',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.white,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Dynamic form based on API configuration
            Text(
              'Dynamic Form (API-Generated)',
              style: LexendTextStyles.lexend18Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 16),

            // Render widgets dynamically from API configuration
            ..._apiComponents.map((component) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 24),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: ColorPalette.gray700),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Component metadata
                      Row(
                        children: [
                          Text(
                            component.displayName,
                            style: LexendTextStyles.lexend14Bold.copyWith(
                              color: ColorPalette.green,
                            ),
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: ColorPalette.green.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              component.uiComponent,
                              style: LexendTextStyles.lexend10Regular.copyWith(
                                color: ColorPalette.green,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // API metadata
                      Text(
                        'API ID: ${component.id}\n'
                        'Category: ${component.category}\n'
                        'Input Type: ${component.inputType}\n'
                        'Created: ${component.createdAt.toLocal().toString().split('.')[0]}',
                        style: LexendTextStyles.lexend10Regular.copyWith(
                          color: ColorPalette.placeHolderTextColor,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Dynamic widget
                      _buildWidgetFromConfig(component),
                    ],
                  ),
                ),
              );
            }),

            const SizedBox(height: 32),

            // Submit button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _submitForm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorPalette.green,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Submit Form',
                  style: LexendTextStyles.lexend16Bold.copyWith(
                    color: ColorPalette.darkToneInk,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
