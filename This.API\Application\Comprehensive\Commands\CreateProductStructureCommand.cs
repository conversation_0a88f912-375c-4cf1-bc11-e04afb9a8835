using Application.Comprehensive.DTOs;
using MediatR;
using Shared.Common.Response;
using System.ComponentModel.DataAnnotations;

namespace Application.Comprehensive.Commands;

/// <summary>
/// Command for creating comprehensive product structure from JSON
/// </summary>
public class CreateProductStructureCommand : IRequest<Result<ProductStructureCreationResult>>
{
    /// <summary>
    /// List of products to create with their complete structure
    /// </summary>
    [Required]
    public List<ProductStructureDto> Products { get; set; } = new();
}

/// <summary>
/// Query for validating product structure without creating
/// </summary>
public class ValidateProductStructureQuery : IRequest<Result<ProductStructureCreationResult>>
{
    /// <summary>
    /// List of products to validate
    /// </summary>
    [Required]
    public List<ProductStructureDto> Products { get; set; } = new();
}
