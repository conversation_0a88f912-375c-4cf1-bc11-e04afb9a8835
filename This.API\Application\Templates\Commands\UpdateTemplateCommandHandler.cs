using Abstraction.Database.Repositories;
using Application.Templates.DTOs;
using Application.Templates.Specifications;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Templates.Commands;

/// <summary>
/// Handler for UpdateTemplateCommand
/// </summary>
public class UpdateTemplateCommandHandler : IRequestHandler<UpdateTemplateCommand, Result<TemplateDto>>
{
    private readonly IRepository<Template> _templateRepository;
    private readonly ILogger<UpdateTemplateCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateTemplateCommandHandler(
        IRepository<Template> templateRepository,
        ILogger<UpdateTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<TemplateDto>> Handle(UpdateTemplateCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating template {TemplateId}", request.Id);

            // Get template using specification
            var templateSpec = new TemplateByIdSpec(request.Id);
            var template = await _templateRepository.GetBySpecAsync(templateSpec, cancellationToken);

            if (template == null)
            {
                _logger.LogWarning("Template not found: {TemplateId}", request.Id);
                return Result<TemplateDto>.Failure("Template not found");
            }

            // Check for duplicate if version or stage is being changed
            if (!string.IsNullOrWhiteSpace(request.Version) || !string.IsNullOrWhiteSpace(request.Stage))
            {
                var newVersion = request.Version ?? template.Version;
                var newStage = request.Stage ?? template.Stage;

                if (newVersion != template.Version || newStage != template.Stage)
                {
                    // Check for duplicate using specification
                    var duplicateSpec = new TemplateDuplicateSpec(template.ProductId, newVersion, newStage, request.Id);
                    var duplicateExists = await _templateRepository.GetBySpecAsync(duplicateSpec, cancellationToken);

                    if (duplicateExists != null)
                    {
                        _logger.LogWarning("Template with version {Version} and stage {Stage} already exists for product {ProductId}",
                            newVersion, newStage, template.ProductId);
                        return Result<TemplateDto>.Failure("Template with this version and stage already exists for this product");
                    }
                }
            }

            // Update template properties
            if (!string.IsNullOrWhiteSpace(request.Version))
                template.Version = request.Version;

            if (!string.IsNullOrWhiteSpace(request.Stage))
                template.Stage = request.Stage;

            if (request.TemplateJson.HasValue)
                template.TemplateJson = request.TemplateJson.Value;

            if (request.IsActive.HasValue)
                template.IsActive = request.IsActive.Value;

            template.ModifiedAt = DateTime.UtcNow;

            await _templateRepository.UpdateAsync(template, cancellationToken);

            var updatedTemplate = new TemplateDto
            {
                Id = template.Id,
                ProductId = template.ProductId,
                ProductName = "Unknown", // No relationship with Product table
                Version = template.Version,
                Stage = template.Stage,
                TemplateJson = template.TemplateJson,
                CreatedAt = template.CreatedAt,
                CreatedBy = template.CreatedBy,
                PublishedAt = template.PublishedAt,
                IsActive = template.IsActive,
                IsDeleted = template.IsDeleted
            };

            _logger.LogInformation("Successfully updated template {TemplateId}", request.Id);
            return Result<TemplateDto>.Success(updatedTemplate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating template {TemplateId}", request.Id);
            return Result<TemplateDto>.Failure("Failed to update template");
        }
    }
}
