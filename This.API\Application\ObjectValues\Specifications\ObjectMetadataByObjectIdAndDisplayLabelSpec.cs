using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get ObjectMetadata by ObjectId and DisplayLabel
/// </summary>
public class ObjectMetadataByObjectIdAndDisplayLabelSpec : Specification<ObjectMetadata>, ISingleResultSpecification<ObjectMetadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectMetadataByObjectIdAndDisplayLabelSpec(Guid objectId, string displayLabel, bool onlyActive = true)
    {
        Query.Where(om => om.ObjectId == objectId &&
                         !om.IsDeleted);

        // Try exact match first, then case-insensitive match, then contains match
        Query.Where(om => om.Metadata.DisplayLabel == displayLabel ||
                         (om.Metadata.DisplayLabel != null && om.Metadata.DisplayLabel.ToLower() == displayLabel.ToLower()) ||
                         (om.Metadata.DisplayLabel != null && om.Metadata.DisplayLabel.Contains(displayLabel)) ||
                         (om.Metadata.Name != null && om.Metadata.Name.ToLower() == displayLabel.ToLower()));

        if (onlyActive)
        {
            Query.Where(om => om.IsActive && om.Object.IsActive && om.Metadata.IsVisible != false);
        }

        // Include related data
        Query.Include(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        Query.Include(om => om.Object);

        // Order by exact match first, then case match, then contains
        Query.OrderBy(om => om.Metadata.DisplayLabel == displayLabel ? 0 :
                           (om.Metadata.DisplayLabel != null && om.Metadata.DisplayLabel.ToLower() == displayLabel.ToLower()) ? 1 : 2);

        // Take only one record
        Query.Take(1);
    }
}
