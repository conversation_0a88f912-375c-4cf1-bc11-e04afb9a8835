import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:intl/intl.dart';

/// Currency model for currency input
class Currency {
  final String code;
  final String symbol;
  final String name;
  final int decimalPlaces;

  const Currency({
    required this.code,
    required this.symbol,
    required this.name,
    this.decimalPlaces = 2,
  });
}

/// A customizable currency input widget following the 'this_componentName_input' naming convention
/// This widget handles currency input with validation and formatting based on API configuration
class ThisCurrencyInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final String value;
  final ValueChanged<String> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  
  // API-based validation parameters
  final String? validationPattern;
  final int? minLength;
  final int? maxLength;
  final double? minValue;
  final double? maxValue;
  final int? decimalPlaces;
  final double? stepValue;
  final String? inputMask;
  final String? requiredErrorMessage;
  final String? patternErrorMessage;
  final String? minLengthErrorMessage;
  final String? maxLengthErrorMessage;
  final String? minValueErrorMessage;
  final String? maxValueErrorMessage;
  
  // Currency-specific parameters
  final Currency defaultCurrency;
  final List<Currency>? availableCurrencies;
  final bool showCurrencySelector;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool autoFocus;
  final String? Function(String)? customValidation;

  const ThisCurrencyInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.validationPattern,
    this.minLength,
    this.maxLength,
    this.minValue,
    this.maxValue,
    this.decimalPlaces,
    this.stepValue,
    this.inputMask,
    this.requiredErrorMessage,
    this.patternErrorMessage,
    this.minLengthErrorMessage,
    this.maxLengthErrorMessage,
    this.minValueErrorMessage,
    this.maxValueErrorMessage,
    this.defaultCurrency = const Currency(code: 'USD', symbol: '\$', name: 'US Dollar'),
    this.availableCurrencies,
    this.showCurrencySelector = true,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisCurrencyInput> createState() => _ThisCurrencyInputState();
}

class _ThisCurrencyInputState extends State<ThisCurrencyInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;
  Currency? _selectedCurrency;

  // Common currencies
  static const List<Currency> _defaultCurrencies = [
    Currency(code: 'USD', symbol: '\$', name: 'US Dollar'),
    Currency(code: 'EUR', symbol: '€', name: 'Euro'),
    Currency(code: 'GBP', symbol: '£', name: 'British Pound'),
    Currency(code: 'JPY', symbol: '¥', name: 'Japanese Yen', decimalPlaces: 0),
    Currency(code: 'CAD', symbol: 'C\$', name: 'Canadian Dollar'),
    Currency(code: 'AUD', symbol: 'A\$', name: 'Australian Dollar'),
    Currency(code: 'CHF', symbol: 'CHF', name: 'Swiss Franc'),
    Currency(code: 'CNY', symbol: '¥', name: 'Chinese Yuan'),
    Currency(code: 'INR', symbol: '₹', name: 'Indian Rupee'),
  ];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
    _focusNode = FocusNode();
    _selectedCurrency = widget.defaultCurrency;
    
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void didUpdateWidget(ThisCurrencyInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  List<Currency> get _availableCurrencies {
    return widget.availableCurrencies ?? _defaultCurrencies;
  }

  double? _parseAmount(String value) {
    if (value.trim().isEmpty) return null;
    
    // Remove currency symbol and formatting
    String cleanValue = value.replaceAll(_selectedCurrency?.symbol ?? '', '');
    cleanValue = cleanValue.replaceAll(',', '').trim();
    
    return double.tryParse(cleanValue);
  }

  List<String> _validateValue(String value) {
    final errors = <String>[];

    // 1. Required validation
    if (widget.required && value.trim().isEmpty) {
      errors.add(widget.requiredErrorMessage ?? '${widget.label} is required');
      return errors;
    }

    // Skip other validations if empty and not required
    if (value.trim().isEmpty && !widget.required) {
      return errors;
    }

    // 2. Pattern validation (from API)
    if (widget.validationPattern != null) {
      final regex = RegExp(widget.validationPattern!);
      if (!regex.hasMatch(value)) {
        errors.add(widget.patternErrorMessage ?? 'Please enter a valid amount');
        return errors;
      }
    }

    // 3. Length validation
    if (widget.minLength != null && value.length < widget.minLength!) {
      errors.add(widget.minLengthErrorMessage ?? '${widget.label} is too short');
      return errors;
    }

    if (widget.maxLength != null && value.length > widget.maxLength!) {
      errors.add(widget.maxLengthErrorMessage ?? '${widget.label} is too long');
      return errors;
    }

    // 4. Numeric value validation
    final amount = _parseAmount(value);
    if (amount == null && value.trim().isNotEmpty) {
      errors.add('Please enter a valid amount');
      return errors;
    }

    if (amount != null) {
      // Min value validation
      if (widget.minValue != null && amount < widget.minValue!) {
        errors.add(widget.minValueErrorMessage ?? 'Amount cannot be negative');
        return errors;
      }

      // Max value validation
      if (widget.maxValue != null && amount > widget.maxValue!) {
        errors.add(widget.maxValueErrorMessage ?? 'Amount is too large');
        return errors;
      }

      // Decimal places validation
      final decimalPlaces = widget.decimalPlaces ?? _selectedCurrency?.decimalPlaces ?? 2;
      if (decimalPlaces >= 0) {
        final decimalPart = value.split('.').length > 1 ? value.split('.')[1] : '';
        if (decimalPart.length > decimalPlaces) {
          errors.add('Maximum $decimalPlaces decimal places allowed');
          return errors;
        }
      }

      // Step validation
      if (widget.stepValue != null && widget.minValue != null) {
        final remainder = (amount - widget.minValue!) % widget.stepValue!;
        if (remainder.abs() >= 0.0001) {
          errors.add('Value must be in increments of ${widget.stepValue}');
          return errors;
        }
      }
    }

    // 5. Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(value);
      if (customError != null) {
        errors.add(customError);
        return errors;
      }
    }

    return errors;
  }

  String _formatCurrency(String value) {
    final amount = _parseAmount(value);
    if (amount == null) return value;

    final decimalPlaces = widget.decimalPlaces ?? _selectedCurrency?.decimalPlaces ?? 2;
    final formatter = NumberFormat.currency(
      symbol: _selectedCurrency?.symbol ?? '\$',
      decimalDigits: decimalPlaces,
    );
    
    return formatter.format(amount);
  }

  void _handleCurrencyChange(Currency currency) {
    setState(() {
      _selectedCurrency = currency;
    });

    // Reformat the current value with new currency
    if (widget.value.isNotEmpty) {
      final amount = _parseAmount(widget.value);
      if (amount != null) {
        final formatted = _formatCurrency(amount.toString());
        widget.onChanged(formatted);
      }
    }
  }

  void _handleChange(String value) {
    widget.onChanged(value);
    
    // Real-time validation (only if not validating on blur)
    if (!widget.validateOnBlur) {
      final errors = _validateValue(value);
      setState(() {
        _errors = errors;
        _isValidated = value.trim().isNotEmpty;
      });
      
      widget.onValidation?.call(errors);
    }
  }

  void _handleBlur() {
    // Format the currency on blur if it's valid
    String formattedValue = widget.value;
    final amount = _parseAmount(widget.value);
    
    if (amount != null && _errors.isEmpty) {
      formattedValue = _formatCurrency(widget.value);
      if (formattedValue != widget.value) {
        widget.onChanged(formattedValue);
      }
    }

    // Validate on blur if enabled
    if (widget.validateOnBlur) {
      final errors = _validateValue(formattedValue);
      setState(() {
        _errors = errors;
        _isValidated = formattedValue.trim().isNotEmpty;
      });
      
      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated || widget.value.trim().isEmpty) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  Future<void> _showCurrencyPicker() async {
    final selectedCurrency = await showModalBottomSheet<Currency>(
      context: context,
      backgroundColor: ColorPalette.darkToneInk,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Select Currency',
              style: LexendTextStyles.lexend16Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _availableCurrencies.length,
                itemBuilder: (context, index) {
                  final currency = _availableCurrencies[index];
                  return ListTile(
                    leading: Text(
                      currency.symbol,
                      style: LexendTextStyles.lexend16Bold.copyWith(
                        color: ColorPalette.white,
                      ),
                    ),
                    title: Text(
                      currency.name,
                      style: LexendTextStyles.lexend14Regular.copyWith(
                        color: ColorPalette.white,
                      ),
                    ),
                    trailing: Text(
                      currency.code,
                      style: LexendTextStyles.lexend14Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                    onTap: () => Navigator.of(context).pop(currency),
                    selected: _selectedCurrency?.code == currency.code,
                    selectedTileColor: ColorPalette.white.withValues(alpha: 0.1),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );

    if (selectedCurrency != null) {
      _handleCurrencyChange(selectedCurrency);
    }
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors && widget.value.trim().isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled 
                    ? ColorPalette.placeHolderTextColor 
                    : ColorPalette.white,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        
        // Input Field with Currency Selector
        Row(
          children: [
            // Currency Selector
            if (widget.showCurrencySelector && _selectedCurrency != null)
              GestureDetector(
                onTap: widget.disabled || widget.readOnly ? null : _showCurrencyPicker,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: hasErrors 
                          ? const Color(0xFFC73E1D)
                          : (isValid ? ColorPalette.green : ColorPalette.gray300),
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(6),
                      bottomLeft: Radius.circular(6),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _selectedCurrency!.symbol,
                        style: LexendTextStyles.lexend14Bold.copyWith(
                          color: ColorPalette.white,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.arrow_drop_down,
                        size: 20,
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ],
                  ),
                ),
              ),
            
            // Currency Input
            Expanded(
              child: TextFormField(
                controller: _controller,
                focusNode: _focusNode,
                enabled: !widget.disabled,
                readOnly: widget.readOnly,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                textInputAction: TextInputAction.next,
                onChanged: _handleChange,
                onFieldSubmitted: (_) => _handleBlur(),
                onTapOutside: (_) => _handleBlur(),
                decoration: InputDecoration(
                  hintText: widget.placeholder ?? 'Enter amount...',
                  hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                  prefixIcon: widget.showIcon && !widget.showCurrencySelector
                      ? Icon(Icons.attach_money, size: 20, color: ColorPalette.placeHolderTextColor)
                      : null,
                  suffixIcon: _getValidationIcon(),
                  errorText: hasErrors ? _errors.first : null,
                  errorStyle: LexendTextStyles.lexend12Regular.copyWith(
                    color: const Color(0xFFC73E1D),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.only(
                      topRight: const Radius.circular(6),
                      bottomRight: const Radius.circular(6),
                      topLeft: widget.showCurrencySelector ? Radius.zero : const Radius.circular(6),
                      bottomLeft: widget.showCurrencySelector ? Radius.zero : const Radius.circular(6),
                    ),
                    borderSide: BorderSide(
                      color: hasErrors 
                          ? const Color(0xFFC73E1D)
                          : (isValid ? ColorPalette.green : ColorPalette.gray300),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.only(
                      topRight: const Radius.circular(6),
                      bottomRight: const Radius.circular(6),
                      topLeft: widget.showCurrencySelector ? Radius.zero : const Radius.circular(6),
                      bottomLeft: widget.showCurrencySelector ? Radius.zero : const Radius.circular(6),
                    ),
                    borderSide: BorderSide(
                      color: hasErrors 
                          ? const Color(0xFFC73E1D)
                          : (isValid ? ColorPalette.green : ColorPalette.gray300),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.only(
                      topRight: const Radius.circular(6),
                      bottomRight: const Radius.circular(6),
                      topLeft: widget.showCurrencySelector ? Radius.zero : const Radius.circular(6),
                      bottomLeft: widget.showCurrencySelector ? Radius.zero : const Radius.circular(6),
                    ),
                    borderSide: BorderSide(
                      color: hasErrors 
                          ? const Color(0xFFC73E1D)
                          : (isValid ? ColorPalette.green : ColorPalette.white),
                      width: 2,
                    ),
                  ),
                ),
                style: LexendTextStyles.lexend14Regular.copyWith(
                  color: widget.disabled 
                      ? ColorPalette.placeHolderTextColor 
                      : ColorPalette.white,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9\.\,]')),
                ],
              ),
            ),
          ],
        ),
        
        // Helper text
        if (!hasErrors && _selectedCurrency != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Currency: ${_selectedCurrency!.name} (${_selectedCurrency!.code})',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          ),
      ],
    );
  }
}
