{"inputs": ["C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\.dart_tool\\flutter_build\\34b474776d999cc0c44336a6869ac499\\app.dill", "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.19.6-stable\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.19.6-stable\\flutter\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.19.6-stable\\flutter\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.19.6-stable\\flutter\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.19.6-stable\\flutter\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\pubspec.yaml", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\config\\.env", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\fonts\\lexend\\LexendDeca-Thin.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\fonts\\lexend\\LexendDeca-Regular.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\fonts\\lexend\\LexendDeca-Medium.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\fonts\\lexend\\LexendDeca-SemiBold.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\fonts\\lexend\\LexendDeca-Bold.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\fonts\\lexend\\LexendDeca-ExtraBold.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\fonts\\lexend\\LexendDeca-Black.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\fonts\\lexend\\LexendDeca-Light.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\assets\\fonts\\lexend\\LexendDeca-ExtraLight.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.19.6-stable\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.19.6-stable\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-8.3.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_macos-0.9.4+3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.28\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_android-0.8.12+23\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_for_web-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_linux-0.2.1+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_macos-0.2.1+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.19.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.16+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.15.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.10.1\\LICENSE", "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.19.6-stable\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.19.6-stable\\flutter\\packages\\flutter\\LICENSE", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD547467438"], "outputs": ["C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/config/.env", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/lexend/LexendDeca-Thin.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/lexend/LexendDeca-Regular.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/lexend/LexendDeca-Medium.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/lexend/LexendDeca-SemiBold.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/lexend/LexendDeca-Bold.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/lexend/LexendDeca-ExtraBold.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/lexend/LexendDeca-Black.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/lexend/LexendDeca-Light.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/lexend/LexendDeca-ExtraLight.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\Desktop\\LRB_Projects\\this-applications\\This.Mobile.Component\\this_mobile_component\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z"]}