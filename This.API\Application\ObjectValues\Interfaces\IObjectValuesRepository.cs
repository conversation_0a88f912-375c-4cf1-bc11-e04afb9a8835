using Application.ObjectValues.DTOs;
using Domain.Entities;
using Shared.Common.Response;

namespace Application.ObjectValues.Interfaces;

/// <summary>
/// Repository interface for ObjectValues operations
/// </summary>
public interface IObjectValuesRepository
{
    /// <summary>
    /// Get ObjectValues by RefId with complete metadata information
    /// </summary>
    /// <param name="refId">Reference ID to group ObjectValues</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="includeInactive">Whether to include inactive records</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>ObjectValues response DTO or null if not found</returns>
    Task<ObjectValuesResponseDto?> GetByRefIdAsync(
        Guid refId, 
        string tenantId, 
        bool includeInactive = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get optimized RefId list with high-performance LINQ operations
    /// </summary>
    /// <param name="objectId">Object ID to filter by</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="refId">Optional RefId to filter by specific RefId</param>
    /// <param name="searchTerm">Optional search term for values</param>
    /// <param name="onlyActive">Whether to include only active records</param>
    /// <param name="pageNumber">Page number for pagination</param>
    /// <param name="pageSize">Page size for pagination</param>
    /// <param name="orderBy">Order by field</param>
    /// <param name="orderDirection">Order direction (asc/desc)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of RefId summaries</returns>
    Task<PaginatedResult<ObjectValuesRefIdSummaryDto>> GetOptimizedRefIdListAsync(
        Guid objectId,
        string tenantId,
        Guid? refId = null,
        string? searchTerm = null,
        bool onlyActive = true,
        int pageNumber = 1,
        int pageSize = 50,
        string? orderBy = null,
        string orderDirection = "desc",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get ObjectValues in dictionary format with optimized LINQ operations
    /// </summary>
    /// <param name="objectId">Object ID that the RefId belongs to</param>
    /// <param name="refId">Reference ID that groups related ObjectValues</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="includeInactive">Whether to include inactive records</param>
    /// <param name="onlyVisible">Whether to include only visible metadata</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary with Name as key and ObjectValueDictionaryDto as value</returns>
    Task<Dictionary<string, ObjectValueDictionaryDto>?> GetObjectValuesDictionaryAsync(
        Guid objectId,
        Guid refId,
        string tenantId,
        bool includeInactive = false,
        bool onlyVisible = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Upsert ObjectValues for a specific RefId
    /// </summary>
    /// <param name="request">Upsert request with RefId and values</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="userId">User ID for audit</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Upsert response with statistics and updated data</returns>
    Task<UpsertObjectValuesResponseDto> UpsertAsync(
        UpsertObjectValuesRequestDto request,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if RefId exists for an ObjectId
    /// </summary>
    /// <param name="refId">Reference ID to check</param>
    /// <param name="objectId">Object ID to verify against</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if RefId exists for the ObjectId</returns>
    Task<bool> RefIdExistsAsync(
        Guid refId,
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get Object information by ObjectId
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Object entity or null if not found</returns>
    Task<Domain.Entities.Object?> GetObjectByIdAsync(
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all metadata for an ObjectId with data type information
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="onlyActive">Whether to include only active metadata</param>
    /// <param name="onlyVisible">Whether to include only visible metadata</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of metadata with data type information</returns>
    Task<List<ObjectValueDetailDto>> GetObjectMetadataAsync(
        Guid objectId,
        string tenantId,
        bool onlyActive = true,
        bool onlyVisible = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate values against metadata constraints
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="values">Values to validate</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="strictValidation">Whether to apply strict validation</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of validation errors</returns>
    Task<List<string>> ValidateValuesAsync(
        Guid objectId,
        Dictionary<string, string> values,
        string tenantId,
        bool strictValidation = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Auto-create missing metadata for new keys
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="metadataKeys">List of metadata keys to ensure exist</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="userId">User ID for audit</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of metadata records created</returns>
    Task<int> EnsureMetadataExistsAsync(
        Guid objectId,
        List<string> metadataKeys,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete ObjectValues by RefId
    /// </summary>
    /// <param name="refId">Reference ID to delete</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of records deleted</returns>
    Task<int> DeleteByRefIdAsync(
        Guid refId,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get ObjectValues statistics for an ObjectId
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Statistics about ObjectValues for the ObjectId</returns>
    Task<ObjectValuesStatisticsDto> GetStatisticsAsync(
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistics DTO for ObjectValues
/// </summary>
public class ObjectValuesStatisticsDto
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid ObjectId { get; set; }
    
    /// <summary>
    /// Total number of unique RefIds
    /// </summary>
    public int TotalRefIds { get; set; }
    
    /// <summary>
    /// Total number of ObjectValues records
    /// </summary>
    public int TotalValues { get; set; }
    
    /// <summary>
    /// Number of metadata fields defined
    /// </summary>
    public int MetadataFieldCount { get; set; }
    
    /// <summary>
    /// Number of complete RefIds (all required fields have values)
    /// </summary>
    public int CompleteRefIds { get; set; }
    
    /// <summary>
    /// Average completion percentage
    /// </summary>
    public decimal AverageCompletionPercentage { get; set; }
    
    /// <summary>
    /// Last modified date
    /// </summary>
    public DateTime? LastModified { get; set; }
}
