using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Lookup entity for global lookup values
/// Represents master lookup values that can be used across all tenants
/// </summary>
public class Lookup : AuditableEntity, IAggregateRoot
{
    
    /// <summary>
    /// Foreign key to the Context
    /// </summary>
    public Guid ContextId { get; set; }

    /// <summary>
    /// Primary value of the lookup item
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default value for the context
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Additional value field 1 (for extended data)
    /// </summary>
    public string? Value1 { get; set; }

    /// <summary>
    /// Additional value field 2 (for extended data)
    /// </summary>
    public string? Value2 { get; set; }

    /// <summary>
    /// Display sequence order
    /// </summary>
    public int ShowSequence { get; set; } = 0;

    /// <summary>
    /// Navigation property to the parent Context
    /// </summary>
    public virtual Context Context { get; set; } = null!;
}
