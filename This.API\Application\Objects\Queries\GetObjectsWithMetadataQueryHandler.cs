// COMMENTED OUT - Contains Feature references that have been removed
// TODO: Update to use Product references or remove if not needed

/*
using Application.Objects.DTOs;
using Application.Objects.Specifications;
using Application.ObjectMetadataManagement.DTOs;
using Abstraction.Database.Repositories;
using MediatR;
using Shared.Common.Response;
using ObjectEntity = Domain.Entities.Object;

namespace Application.Objects.Queries;

/// <summary>
/// Get Objects with metadata query handler
/// </summary>
public class GetObjectsWithMetadataQueryHandler : IRequestHandler<GetObjectsWithMetadataQuery, PaginatedResult<ObjectWithMetadataDto>>
{
    private readonly IReadRepository<ObjectEntity> _objectRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectsWithMetadataQueryHandler(IReadRepository<ObjectEntity> objectRepository)
    {
        _objectRepository = objectRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<ObjectWithMetadataDto>> Handle(GetObjectsWithMetadataQuery request, CancellationToken cancellationToken)
    {
        var skip = (request.PageNumber - 1) * request.PageSize;

        // Create specifications
        var dataSpec = new ObjectWithMetadataSpec(
            searchTerm: request.SearchTerm,
            featureId: request.FeatureId,
            isActive: request.IsActive,
            orderBy: request.OrderBy,
            skip: skip,
            take: request.PageSize);

        var countSpec = new ObjectCountSpec(
            searchTerm: request.SearchTerm,
            featureId: request.FeatureId,
            isActive: request.IsActive);

        // Get data and count using specifications (tenant isolation handled by Finbuckle.MultiTenant)
        var objects = await _objectRepository.ListAsync(dataSpec, cancellationToken);
        var totalCount = await _objectRepository.CountAsync(countSpec, cancellationToken);

        var objectDtos = objects.Select(obj => new ObjectWithMetadataDto
        {
            Id = obj.Id,
            FeatureId = obj.FeatureId,
            FeatureName = obj.Feature?.Name,
            ParentObjectId = obj.ParentObjectId,
            ParentObjectName = obj.ParentObject?.Name,
            Name = obj.Name,
            Description = obj.Description,
            IsActive = obj.IsActive,
            ChildObjectsCount = obj.ChildObjects?.Count(co => co.IsActive && !co.IsDeleted) ?? 0,
            Metadata = obj.ObjectMetadata?
                .Where(om => om.IsActive && !om.IsDeleted)
                .OrderBy(om => om.Metadata.FieldOrder ?? int.MaxValue)
                .ThenBy(om => om.Metadata.Name) // Updated: Name is now Name property
                .Select(om => new ObjectMetadataDto
                {
                    Id = om.Id,
                    ObjectId = om.ObjectId,
                    ObjectName = obj.Name,
                    MetadataId = om.MetadataId,
                    Name = om.Metadata.Name, // Updated: Name is now Name property
                    MetadataDisplayLabel = om.Metadata.DisplayLabel,
                    IsUnique = om.IsUnique,
                    IsActive = om.IsActive,
                    ValuesCount = 0, // TODO: Calculate actual values count if needed
                    ShouldVisibleInList = om.IsVisibleInList,
                    ShouldVisibleInEdit = om.IsVisibleInEdit,
                    ShouldVisibleInCreate = om.IsVisibleInCreate,
                    ShouldVisibleInView = om.IsVisibleInView,
                    IsCalculate = om.IsCalculated,
                    DataTypeId = om.Metadata.DataTypeId,
                    DataTypeName = om.Metadata.DataType?.Name,
                    CreatedAt = om.CreatedAt,
                    CreatedBy = om.CreatedBy ?? Guid.Empty,
                    ModifiedAt = om.ModifiedAt,
                    ModifiedBy = om.ModifiedBy
                }).ToList() ?? new List<ObjectMetadataDto>(),
           
        }).ToList();

        return new PaginatedResult<ObjectWithMetadataDto>(objectDtos, request.PageNumber, request.PageSize, totalCount);
    }
}
*/
