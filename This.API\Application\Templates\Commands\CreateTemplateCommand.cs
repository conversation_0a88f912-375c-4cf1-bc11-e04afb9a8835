using Application.Templates.DTOs;
using MediatR;
using Shared.Common.Response;
using System.Text.Json;

namespace Application.Templates.Commands;

/// <summary>
/// Command to create a new template
/// </summary>
public class CreateTemplateCommand : IRequest<Result<TemplateDto>>
{
    /// <summary>
    /// Product ID this template belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Template version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Template stage (draft, live, beta, etc.)
    /// </summary>
    public string Stage { get; set; } = string.Empty;

    /// <summary>
    /// Template JSON content
    /// </summary>
    public JsonElement TemplateJson { get; set; }

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
