using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Field Mapping entity - stores field mapping configurations for data transformation
/// </summary>
public class FieldMapping : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Integration ID this field mapping belongs to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// API name for this field mapping
    /// </summary>
    public string? ApiName { get; set; }

    /// <summary>
    /// Name of the field in the incoming JSON (e.g., "soldPrice", "documents")
    /// </summary>
    public string SourceField { get; set; } = string.Empty;

    /// <summary>
    /// Data type in the source (e.g., "string", "decimal", "array", "object", etc.)
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// Object metadata ID this mapping targets (optional)
    /// </summary>
    public Guid? ObjectMetadataId { get; set; }

    /// <summary>
    /// User ID this mapping is associated with (optional)
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// Role ID this mapping is associated with (optional)
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Optional: which object this mapping is for (e.g., "Payments", "Documents")
    /// </summary>
    public string? TargetObjectName { get; set; }

    /// <summary>
    /// Additional notes or mapping logic
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Transformation rules stored as JSON
    /// </summary>
    public string? TransformationRules { get; set; }

    // Navigation properties
    /// <summary>
    /// Integration this field mapping belongs to
    /// </summary>
    public virtual Integration Integration { get; set; } = null!;
    /// <summary>
    /// Object metadata this mapping targets
    /// </summary>
    public virtual ObjectMetadata? ObjectMetadata { get; set; }

    /// <summary>
    /// User this mapping is associated with
    /// </summary>
    public virtual User? User { get; set; }

    /// <summary>
    /// Role this mapping is associated with
    /// </summary>
    public virtual Role? Role { get; set; }
}
