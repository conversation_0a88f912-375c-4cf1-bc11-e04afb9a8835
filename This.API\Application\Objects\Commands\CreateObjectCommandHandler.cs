using Application.Objects.DTOs;
using Application.Objects.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.Objects.Commands;

/// <summary>
/// Create Object command handler
/// </summary>
public class CreateObjectCommandHandler : IRequestHandler<CreateObjectCommand, Result<ObjectDto>>
{
    private readonly IRepository<Domain.Entities.Object> _repository;
    private readonly IRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateObjectCommandHandler(
        IRepository<Domain.Entities.Object> repository,
        IRepository<Product> productRepository)
    {
        _repository = repository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ObjectDto>> Handle(CreateObjectCommand request, CancellationToken cancellationToken)
    {
        // Validate product exists
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null)
        {
            return Result<ObjectDto>.Failure($"Product with ID '{request.ProductId}' not found.");
        }

        // Validate parent object exists if specified
        if (request.ParentObjectId.HasValue)
        {
            var parentObject = await _repository.GetByIdAsync(request.ParentObjectId.Value, cancellationToken);
            if (parentObject == null)
            {
                return Result<ObjectDto>.Failure($"Parent object with ID '{request.ParentObjectId}' not found.");
            }

            // Ensure parent object belongs to the same product
            if (parentObject.ProductId != request.ProductId)
            {
                return Result<ObjectDto>.Failure("Parent object must belong to the same product.");
            }
        }

        // Check if Object with same name already exists in the product
        var existingObject = await _repository.GetBySpecAsync(
            new ObjectByProductAndNameSpec(request.ProductId, request.Name), cancellationToken);
        if (existingObject != null)
        {
            return Result<ObjectDto>.Failure($"Object with name '{request.Name}' already exists in this product.");
        }

        // Create new Object
        var obj = new Domain.Entities.Object
        {
            ProductId = request.ProductId,
            ParentObjectId = request.ParentObjectId,
            Name = request.Name,
            Description = request.Description,
            Icon = request.Icon,
            IsActive = request.IsActive
        };

        var createdObject = await _repository.AddAsync(obj, cancellationToken);

        var dto = new ObjectDto
        {
            Id = createdObject.Id,
            ProductId = createdObject.ProductId,
            ProductName = product.Name,
            ParentObjectId = createdObject.ParentObjectId,
            Name = createdObject.Name,
            Description = createdObject.Description,
            Icon = createdObject.Icon,
            IsActive = createdObject.IsActive,
            ChildObjectsCount = 0,
            MetadataCount = 0,
            CreatedAt = createdObject.CreatedAt,
            CreatedBy = createdObject.CreatedBy ?? Guid.Empty,
            ModifiedAt = createdObject.ModifiedAt,
            ModifiedBy = createdObject.ModifiedBy
        };

        return Result<ObjectDto>.Success(dto);
    }
}
