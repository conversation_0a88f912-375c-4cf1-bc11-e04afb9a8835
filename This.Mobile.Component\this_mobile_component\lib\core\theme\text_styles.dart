import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/app_fonts.dart';

class LexendTextStyles {
  static const TextStyle baseStyle = TextStyle(
    overflow: TextOverflow.visible,
    fontFamily: fontFamilyLexendDecaRegular,
  );
  static TextStyle lexend6 = baseStyle.copyWith(fontSize: 6);
  static TextStyle lexend6Medium = lexend6.copyWith(fontFamily: fontFamilyLexendDecaMedium);

  static TextStyle lexend8 = baseStyle.copyWith(fontSize: 8);
  static TextStyle lexend8Thin = lexend8.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend8SemiBold = lexend8.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend8Regular = lexend8.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend8Medium = lexend8.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend8Light = lexend8.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend8ExtraLight = lexend8.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend8ExtraBold = lexend8.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend8Bold = lexend8.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend8Black = lexend8.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend9 = baseStyle.copyWith(fontSize: 9);
  static TextStyle lexend9Thin = lexend9.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend9SemiBold = lexend9.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend9Regular = lexend9.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend9Medium = lexend9.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend9Light = lexend9.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend9ExtraLight = lexend9.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend9ExtraBold = lexend9.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend9Bold = lexend9.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend9Black = lexend9.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend10 = baseStyle.copyWith(fontSize: 10);
  static TextStyle lexend10Thin = lexend10.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend10SemiBold = lexend10.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend10Regular = lexend10.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend10Medium = lexend10.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend10Light = lexend10.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend10ExtraLight = lexend10.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend10ExtraBold = lexend10.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend10Bold = lexend10.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend10Black = lexend10.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend11 = baseStyle.copyWith(fontSize: 11);
  static TextStyle lexend11Thin = lexend11.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend11SemiBold = lexend11.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend11Regular = lexend11.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend11Medium = lexend11.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend11Light = lexend11.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend11ExtraLight = lexend11.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend11ExtraBold = lexend11.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend11Bold = lexend11.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend11Black = lexend11.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend12 = baseStyle.copyWith(fontSize: 12);
  static TextStyle lexend12Thin = lexend12.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend12SemiBold = lexend12.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend12Regular = lexend12.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend12Medium = lexend12.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend12Light = lexend12.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend12ExtraLight = lexend12.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend12ExtraBold = lexend12.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend12Bold = lexend12.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend12Black = lexend12.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend13 = baseStyle.copyWith(fontSize: 13);
  static TextStyle lexend13Thin = lexend13.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend13SemiBold = lexend13.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend13Regular = lexend13.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend13Medium = lexend13.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend13Light = lexend13.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend13ExtraLight = lexend13.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend13ExtraBold = lexend13.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend13Bold = lexend13.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend13Black = lexend13.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend14 = baseStyle.copyWith(fontSize: 14);
  static TextStyle lexend14Thin = lexend14.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend14SemiBold = lexend14.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend14Regular = lexend14.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend14Medium = lexend14.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend14Light = lexend14.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend14ExtraLight = lexend14.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend14ExtraBold = lexend14.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend14Bold = lexend14.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend14Black = lexend14.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend15 = baseStyle.copyWith(fontSize: 15);
  static TextStyle lexend15Thin = lexend15.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend15SemiBold = lexend15.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend15Regular = lexend15.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend15Medium = lexend15.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend15Light = lexend15.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend15ExtraLight = lexend15.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend15ExtraBold = lexend15.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend15Bold = lexend15.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend15Black = lexend15.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend16 = baseStyle.copyWith(fontSize: 16);
  static TextStyle lexend16Thin = lexend16.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend16SemiBold = lexend16.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend16Regular = lexend16.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend16Medium = lexend16.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend16Light = lexend16.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend16ExtraLight = lexend16.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend16ExtraBold = lexend16.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend16Bold = lexend16.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend16Black = lexend16.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend17 = baseStyle.copyWith(fontSize: 17);
  static TextStyle lexend17Thin = lexend17.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend17SemiBold = lexend17.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend17Regular = lexend17.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend17Medium = lexend17.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend17Light = lexend17.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend17ExtraLight = lexend17.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend17ExtraBold = lexend17.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend17Bold = lexend17.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend17Black = lexend17.copyWith(fontFamily: fontFamilyLexendDecaBlack);

  static TextStyle lexend18 = baseStyle.copyWith(fontSize: 18);
  static TextStyle lexend18Thin = lexend18.copyWith(fontFamily: fontFamilyLexendDecaThin);
  static TextStyle lexend18SemiBold = lexend18.copyWith(fontFamily: fontFamilyLexendDecaSemiBold);
  static TextStyle lexend18Regular = lexend18.copyWith(fontFamily: fontFamilyLexendDecaRegular);
  static TextStyle lexend18Medium = lexend18.copyWith(fontFamily: fontFamilyLexendDecaMedium);
  static TextStyle lexend18Light = lexend18.copyWith(fontFamily: fontFamilyLexendDecaLight);
  static TextStyle lexend18ExtraLight = lexend18.copyWith(fontFamily: fontFamilyLexendDecaExtraLight);
  static TextStyle lexend18ExtraBold = lexend18.copyWith(fontFamily: fontFamilyLexendDecaExtraBold);
  static TextStyle lexend18Bold = lexend18.copyWith(fontFamily: fontFamilyLexendDecaBold);
  static TextStyle lexend18Black = lexend18.copyWith(fontFamily: fontFamilyLexendDecaBlack);
}
