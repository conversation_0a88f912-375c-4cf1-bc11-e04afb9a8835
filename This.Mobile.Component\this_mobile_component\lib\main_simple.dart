import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/app_theme.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/this_text_input.dart';
import 'package:this_mobile_component/core/components/widgets/this_textarea_input.dart';
import 'package:this_mobile_component/core/components/widgets/widget_enums.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Widget Test App - Simple',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.darkThemeMode,
      home: const SimpleTestPage(),
    );
  }
}

class SimpleTestPage extends StatefulWidget {
  const SimpleTestPage({super.key});

  @override
  State<SimpleTestPage> createState() => _SimpleTestPageState();
}

class _SimpleTestPageState extends State<SimpleTestPage> {
  String _textValue = '';
  String _textareaValue = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorPalette.primaryDarkColor,
        title: Text(
          'Simple Widget Test',
          style: LexendTextStyles.lexend16ExtraLight.copyWith(
            color: ColorPalette.white,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Testing Basic Widgets',
              style: LexendTextStyles.lexend18Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 24),

            // Text Input Test
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.gray700),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Text Input Test',
                    style: LexendTextStyles.lexend16Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ThisTextInput(
                    id: 'text_test',
                    label: 'Name',
                    value: _textValue,
                    onChanged: (value) => setState(() => _textValue = value),
                    placeholder: 'Enter your name...',
                    required: true,
                    maxLength: 50,
                    showCharacterCount: true,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),

            // Textarea Input Test
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.gray700),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Textarea Input Test',
                    style: LexendTextStyles.lexend16Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ThisTextareaInput(
                    id: 'textarea_test',
                    label: 'Description',
                    value: _textareaValue,
                    onChanged: (value) => setState(() => _textareaValue = value),
                    placeholder: 'Enter description...',
                    minLines: 3,
                    maxLines: 6,
                    maxLength: 200,
                    showCharacterCount: true,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Status
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.green),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ Basic Widgets Working!',
                    style: LexendTextStyles.lexend14Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Text Input: ${_textValue.isEmpty ? "Empty" : _textValue}\nTextarea: ${_textareaValue.isEmpty ? "Empty" : _textareaValue}',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
