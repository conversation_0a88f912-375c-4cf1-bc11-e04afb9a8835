using Application.ComprehensiveEntityData.DTOs;

namespace Application.ObjectValues.DTOs;

/// <summary>
/// Response DTO for ObjectValues grouped by RefId
/// </summary>
public class ObjectValuesResponseDto
{
    /// <summary>
    /// Reference ID that groups related ObjectValues
    /// </summary>
    public Guid RefId { get; set; }
    
    /// <summary>
    /// Object ID that these values belong to
    /// </summary>
    public Guid ObjectId { get; set; }
    
    /// <summary>
    /// Object name
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;
    
    /// <summary>
    /// Object description
    /// </summary>
    public string? ObjectDescription { get; set; }
    
    /// <summary>
    /// Parent object ID if this is a child object
    /// </summary>
    public Guid? ParentObjectId { get; set; }
    
    /// <summary>
    /// Parent object name if this is a child object
    /// </summary>
    public string? ParentObjectName { get; set; }
    
    /// <summary>
    /// Dictionary of metadata values keyed by Name
    /// </summary>
    public Dictionary<string, ObjectValueDetailDto> Values { get; set; } = new();
    
    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    public string TenantId { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; }
}

/// <summary>
/// Detailed information about a specific ObjectValue
/// </summary>
public class ObjectValueDetailDto
{
    /// <summary>
    /// The actual value stored
    /// </summary>
    public string? Value { get; set; }
    
    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }
    
    /// <summary>
    /// Metadata key for identification
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;
    
    /// <summary>
    /// Display label for UI
    /// </summary>
    public string DisplayLabel { get; set; } = string.Empty;
    
    /// <summary>
    /// Help text for the field
    /// </summary>
    public string? HelpText { get; set; }
    
    /// <summary>
    /// Field order for display
    /// </summary>
    public int? FieldOrder { get; set; }
    
    /// <summary>
    /// Whether the field is visible
    /// </summary>
    public bool IsVisible { get; set; }
    
    /// <summary>
    /// Whether the field is readonly
    /// </summary>
    public bool IsReadonly { get; set; }
    
    /// <summary>
    /// Whether the field is required
    /// </summary>
    public bool IsRequired { get; set; }
    
    /// <summary>
    /// Whether this metadata field is unique for the object
    /// </summary>
    public bool IsUnique { get; set; }
    
    /// <summary>
    /// Data type information for validation and UI rendering
    /// </summary>
    public DataTypeInfoDto DataType { get; set; } = new();
    
    /// <summary>
    /// ObjectValue ID if it exists (null for missing values)
    /// </summary>
    public Guid? ObjectValueId { get; set; }
}

/// <summary>
/// Request DTO for upserting ObjectValues
/// </summary>
public class UpsertObjectValuesRequestDto
{
    /// <summary>
    /// Reference ID that groups related ObjectValues
    /// </summary>
    public Guid RefId { get; set; }
    
    /// <summary>
    /// Object ID that these values belong to
    /// </summary>
    public Guid ObjectId { get; set; }
    
    /// <summary>
    /// Dictionary of values keyed by Name
    /// </summary>
    public Dictionary<string, string> Values { get; set; } = new();
    
    /// <summary>
    /// Whether to create missing metadata automatically
    /// </summary>
    public bool AutoCreateMetadata { get; set; } = true;
    
    /// <summary>
    /// Whether to validate data types strictly
    /// </summary>
    public bool StrictValidation { get; set; } = true;
}

/// <summary>
/// Response DTO for upsert operations
/// </summary>
public class UpsertObjectValuesResponseDto
{
    /// <summary>
    /// The updated/created ObjectValues data
    /// </summary>
    public ObjectValuesResponseDto ObjectValues { get; set; } = new();
    
    /// <summary>
    /// Number of values created
    /// </summary>
    public int CreatedCount { get; set; }
    
    /// <summary>
    /// Number of values updated
    /// </summary>
    public int UpdatedCount { get; set; }
    
    /// <summary>
    /// Number of metadata records auto-created
    /// </summary>
    public int MetadataCreatedCount { get; set; }
    
    /// <summary>
    /// List of validation errors if any
    /// </summary>
    public List<string> ValidationErrors { get; set; } = new();
    
    /// <summary>
    /// List of warnings if any
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Dictionary DTO for ObjectValues with Id and Value
/// </summary>
public class ObjectValueDictionaryDto
{
    /// <summary>
    /// ObjectValue ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// ObjectValue Value
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// Metadata Key for reference
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// Display label for UI
    /// </summary>
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// Whether this field is required
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    /// Data type information
    /// </summary>
    public string? DataType { get; set; }
}

/// <summary>
/// Summary DTO for RefId listings
/// </summary>
public class ObjectValuesRefIdSummaryDto
{
    /// <summary>
    /// Reference ID
    /// </summary>
    public Guid RefId { get; set; }
    
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid ObjectId { get; set; }
    
    /// <summary>
    /// Object name
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;
    
    /// <summary>
    /// Number of metadata values for this RefId
    /// </summary>
    public int ValueCount { get; set; }
    
    /// <summary>
    /// Last modified date
    /// </summary>
    public DateTime LastModified { get; set; }
    
    /// <summary>
    /// Whether all required fields have values
    /// </summary>
    public bool IsComplete { get; set; }
    
    /// <summary>
    /// Sample values for preview (first 3 non-empty values)
    /// </summary>
    public Dictionary<string, string> MetaValues { get; set; } = new();
}

/// <summary>
/// Request DTO for querying ObjectValues with filters
/// </summary>
public class GetObjectValuesQueryDto
{
    /// <summary>
    /// Object ID to filter by
    /// </summary>
    public Guid? ObjectId { get; set; }
    
    /// <summary>
    /// Reference ID to get specific instance
    /// </summary>
    public Guid? RefId { get; set; }
    
    /// <summary>
    /// Search term for values
    /// </summary>
    public string? SearchTerm { get; set; }
    
    /// <summary>
    /// Include only active records
    /// </summary>
    public bool OnlyActive { get; set; } = true;
    
    /// <summary>
    /// Include only visible metadata
    /// </summary>
    public bool OnlyVisible { get; set; } = true;
    
    /// <summary>
    /// Page number for pagination
    /// </summary>
    public int PageNumber { get; set; } = 1;
    
    /// <summary>
    /// Page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 50;
    
    /// <summary>
    /// Order by field
    /// </summary>
    public string? OrderBy { get; set; }
    
    /// <summary>
    /// Order direction (asc/desc)
    /// </summary>
    public string OrderDirection { get; set; } = "asc";
}
