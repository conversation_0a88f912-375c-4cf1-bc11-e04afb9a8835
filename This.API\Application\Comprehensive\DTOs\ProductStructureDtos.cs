using System.ComponentModel.DataAnnotations;

namespace Application.Comprehensive.DTOs;

/// <summary>
/// Root structure for creating comprehensive product structure
/// </summary>
public class ProductStructureRequest
{
    /// <summary>
    /// List of products to create
    /// </summary>
    [Required]
    public List<ProductStructureDto> Products { get; set; } = new();
}

/// <summary>
/// Product structure with metadata and objects
/// </summary>
public class ProductStructureDto
{
    /// <summary>
    /// External product ID (from JSON)
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Product description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Product version
    /// </summary>
    [MaxLength(50)]
    public string? Version { get; set; } = "1.0.0";

    /// <summary>
    /// Whether the product is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Product metadata definitions
    /// </summary>
    public List<MetadataStructureDto>? Metadata { get; set; }

    /// <summary>
    /// Product objects with hierarchical structure
    /// </summary>
    public List<ObjectStructureDto>? Objects { get; set; }
}

/// <summary>
/// Object structure with metadata and child objects
/// </summary>
public class ObjectStructureDto
{
    /// <summary>
    /// External object ID (from JSON)
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Object description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Object metadata definitions
    /// </summary>
    public List<MetadataStructureDto>? Metadata { get; set; }

    /// <summary>
    /// Child objects (for hierarchical structure)
    /// </summary>
    public List<ObjectStructureDto>? Objects { get; set; }
}

/// <summary>
/// Metadata structure definition
/// </summary>
public class MetadataStructureDto
{
    /// <summary>
    /// External metadata ID (from JSON)
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// Metadata name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Data type name
    /// </summary>
    [Required]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Metadata description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the field is required
    /// </summary>
    public bool Required { get; set; } = false;

    /// <summary>
    /// Whether the metadata is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Default value for the metadata
    /// </summary>
    public string? DefaultValue { get; set; }

    /// <summary>
    /// Whether the field is visible
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// Whether the field is readonly
    /// </summary>
    public bool IsReadonly { get; set; } = false;
}

/// <summary>
/// Response for comprehensive product structure creation
/// </summary>
public class ProductStructureCreationResult
{
    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Summary message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Total number of products created
    /// </summary>
    public int TotalProductsCreated { get; set; }

    /// <summary>
    /// Total number of objects created
    /// </summary>
    public int TotalObjectsCreated { get; set; }

    /// <summary>
    /// Total number of metadata entries created
    /// </summary>
    public int TotalMetadataCreated { get; set; }

    /// <summary>
    /// Total number of object-metadata links created
    /// </summary>
    public int TotalObjectMetadataCreated { get; set; }

    /// <summary>
    /// List of created products with their details
    /// </summary>
    public List<CreatedProductInfo> CreatedProducts { get; set; } = new();

    /// <summary>
    /// Any errors that occurred during processing
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Processing warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// Processing metrics
    /// </summary>
    public ProcessingMetrics Metrics { get; set; } = new();
}

/// <summary>
/// Information about a created product
/// </summary>
public class CreatedProductInfo
{
    /// <summary>
    /// Created product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// External ID from JSON
    /// </summary>
    public string? ExternalId { get; set; }

    /// <summary>
    /// Number of metadata fields created for this product
    /// </summary>
    public int MetadataFieldsCount { get; set; }

    /// <summary>
    /// Number of objects created for this product
    /// </summary>
    public int ObjectsCount { get; set; }

    /// <summary>
    /// Root objects created under this product
    /// </summary>
    public List<ProductStructureCreatedObjectInfo> RootObjects { get; set; } = new();
}

/// <summary>
/// Information about a created object in product structure
/// </summary>
public class ProductStructureCreatedObjectInfo
{
    /// <summary>
    /// Created object ID
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// External ID from JSON
    /// </summary>
    public string? ExternalId { get; set; }

    /// <summary>
    /// Parent object ID (if any)
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Hierarchy level (0 = root)
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// Number of metadata fields created for this object
    /// </summary>
    public int MetadataFieldsCount { get; set; }

    /// <summary>
    /// Child objects created under this object
    /// </summary>
    public List<ProductStructureCreatedObjectInfo> Children { get; set; } = new();
}

/// <summary>
/// Processing metrics
/// </summary>
public class ProcessingMetrics
{
    /// <summary>
    /// Total processing time in milliseconds
    /// </summary>
    public long TotalProcessingTimeMs { get; set; }

    /// <summary>
    /// Validation time in milliseconds
    /// </summary>
    public long ValidationTimeMs { get; set; }

    /// <summary>
    /// Database operations time in milliseconds
    /// </summary>
    public long DatabaseTimeMs { get; set; }

    /// <summary>
    /// Number of database queries executed
    /// </summary>
    public int DatabaseQueriesCount { get; set; }

    /// <summary>
    /// Maximum hierarchy depth processed
    /// </summary>
    public int MaxHierarchyDepth { get; set; }
}
