import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

class AppTheme {
  static final darkThemeMode = ThemeData.dark(useMaterial3: true).copyWith(
    scaffoldBackgroundColor: ColorPalette.primaryDarkColor,
    appBarTheme: const AppBarTheme(
      backgroundColor: ColorPalette.primaryDarkColor,
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: ColorPalette.darkToneInk,
      type: BottomNavigationBarType.fixed,
      unselectedLabelStyle: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.placeHolderTextColor),
      selectedLabelStyle: LexendTextStyles.lexend10Bold.copyWith(color: ColorPalette.placeHolderTextColor),
    ),
    splashFactory: NoSplash.splashFactory,
    inputDecorationTheme: InputDecorationTheme(
      contentPadding: const EdgeInsets.all(18),
      enabledBorder: _inputBorder(),
      disabledBorder: _inputBorder(),
      focusedBorder: _inputBorder(),
      errorBorder: _inputBorder(const Color(0xFFC73E1D)),
      focusedErrorBorder: _inputBorder(const Color(0xFFC73E1D)),
    ),
  );

  static final lightThemeMode = ThemeData.light(useMaterial3: true).copyWith(
    scaffoldBackgroundColor: ColorPalette.primaryDarkColor,
    appBarTheme: const AppBarTheme(
      backgroundColor: ColorPalette.primaryDarkColor,
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: ColorPalette.darkToneInk,
      type: BottomNavigationBarType.fixed,
      unselectedLabelStyle: LexendTextStyles.lexend10Light.copyWith(color: ColorPalette.placeHolderTextColor),
      selectedLabelStyle: LexendTextStyles.lexend10Bold.copyWith(color: ColorPalette.placeHolderTextColor),
    ),
    splashFactory: NoSplash.splashFactory,
    inputDecorationTheme: InputDecorationTheme(
      contentPadding: const EdgeInsets.all(14),
      enabledBorder: _inputBorder(const Color(0xFFE0E0E0)),
      disabledBorder: _inputBorder(const Color(0xFFE0E0E0)),
      focusedBorder: _inputBorder(const Color(0xFFE0E0E0)),
      errorBorder: _inputBorder(const Color(0xFFC73E1D)),
      focusedErrorBorder: _inputBorder(const Color(0xFFC73E1D)),
    ),
  );

  static _inputBorder([Color color = ColorPalette.gray300]) => OutlineInputBorder(
        borderSide: BorderSide(color: color, width: 1),
        borderRadius: BorderRadius.circular(6),
      );
}
