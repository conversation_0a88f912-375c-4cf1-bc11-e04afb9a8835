using Ardalis.Specification;
using Domain.Entities;

namespace Application.Objects.Specifications;

/// <summary>
/// Specification to get Object by product and name
/// </summary>
public class ObjectByProductAndNameSpec : Specification<Domain.Entities.Object>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectByProductAndNameSpec(Guid productId, string name)
    {
        Query.Where(obj => obj.ProductId == productId && obj.Name == name && !obj.IsDeleted);
    }
}
