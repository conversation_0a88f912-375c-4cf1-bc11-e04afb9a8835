using Application.Comprehensive.Commands;
using Application.Comprehensive.DTOs;
using Application.ComprehensiveEntityData.DTOs;
using Application.ComprehensiveEntityData.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Comprehensive Entity Controller - Unified API for all comprehensive entity operations
/// Combines hierarchical data retrieval and product structure creation functionality
/// </summary>
[ApiController]
[Route("api/comprehensive-entity")]
[Produces("application/json")]
public class ComprehensiveEntityController : BaseApiController
{
    #region Hierarchical Entity Data Endpoints

    /// <summary>
    /// Get all hierarchical entity data with filtering
    /// Returns proper parent-child nested structure where child objects are nested within their parents
    /// </summary>
    /// <param name="productId">Filter by Product ID (optional)</param>
    /// <param name="featureId">Filter by Feature ID (optional)</param>
    /// <param name="searchTerm">Search term for names and descriptions (optional)</param>
    /// <param name="isActive">Filter by active status (optional)</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata (default: true)</param>
    /// <param name="onlyActiveMetadata">Include only active metadata (default: true)</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size (default: 50)</param>
    /// <returns>Hierarchical entity data with proper parent-child nesting</returns>
    /// <response code="200">Hierarchical entity data retrieved successfully</response>
    /// <response code="400">Invalid request parameters</response>
    /// <response code="500">Internal server error</response>
    /// <remarks>
    /// **HIERARCHICAL STRUCTURE**: Returns data in proper parent-child nested format
    ///
    /// ```json
    /// {
    ///   "succeeded": true,
    ///   "data": {
    ///     "products": [
    ///       {
    ///         "id": "product-guid",
    ///         "name": "Product Name",
    ///         "metadata": [...],
    ///         "features": [
    ///           {
    ///             "id": "feature-guid",
    ///             "name": "Feature Name",
    ///             "metadata": [...],
    ///             "rootObjects": [
    ///               {
    ///                 "id": "organization-guid",
    ///                 "name": "Organization",
    ///                 "parentObjectId": null,
    ///                 "hierarchyLevel": 0,
    ///                 "hierarchyPath": "Organization",
    ///                 "metadata": [...],
    ///                 "childObjects": [
    ///                   {
    ///                     "id": "building-guid",
    ///                     "name": "Building A",
    ///                     "parentObjectId": "organization-guid",
    ///                     "hierarchyLevel": 1,
    ///                     "hierarchyPath": "Organization > Building",
    ///                     "metadata": [...],
    ///                     "childObjects": [...]
    ///                   }
    ///                 ]
    ///               }
    ///             ]
    ///           }
    ///         ]
    ///       }
    ///     ]
    ///   }
    /// }
    /// ```
    /// </remarks>
    [HttpGet]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<HierarchicalEntityDataResponseDto>>> GetHierarchicalEntityData(
        [FromQuery] Guid? productId = null,
        [FromQuery] Guid? featureId = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            var query = new GetHierarchicalEntityDataQuery
            {
                ProductId = productId,
                FeatureId = featureId,
                SearchTerm = searchTerm,
                IsActive = isActive,
                OnlyVisibleMetadata = onlyVisibleMetadata,
                OnlyActiveMetadata = onlyActiveMetadata,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while processing the request",
                error = ex.Message,
                details = ex.StackTrace
            });
        }
    }

    /// <summary>
    /// Get hierarchical entity data for a specific product
    /// Returns proper parent-child nested structure for a single product
    /// </summary>
    /// <param name="productId">Product ID</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata (default: true)</param>
    /// <param name="onlyActiveMetadata">Include only active metadata (default: true)</param>
    /// <param name="pageSize">Page size (default: 100)</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="searchTerm">Search term for filtering (optional)</param>
    /// <param name="isActive">Filter by active status (optional)</param>
    /// <returns>Hierarchical entity data for the specified product with proper parent-child nesting</returns>
    /// <response code="200">Product hierarchical data retrieved successfully</response>
    /// <response code="404">Product not found</response>
    /// <response code="500">Internal server error</response>
    /// <remarks>
    /// **SINGLE PRODUCT HIERARCHICAL VIEW**: Returns complete hierarchical structure for one product
    ///
    /// ```json
    /// {
    ///   "succeeded": true,
    ///   "data": {
    ///     "products": [
    ///       {
    ///         "id": "product-guid",
    ///         "name": "Product Name",
    ///         "metadata": [...],
    ///         "features": [
    ///           {
    ///             "id": "feature-guid",
    ///             "name": "Feature Name",
    ///             "metadata": [...],
    ///             "rootObjects": [
    ///               {
    ///                 "id": "organization-guid",
    ///                 "name": "Organization",
    ///                 "parentObjectId": null,
    ///                 "hierarchyLevel": 0,
    ///                 "hierarchyPath": "Organization",
    ///                 "metadata": [...],
    ///                 "childObjects": [
    ///                   {
    ///                     "id": "building-guid",
    ///                     "name": "Building A",
    ///                     "parentObjectId": "organization-guid",
    ///                     "hierarchyLevel": 1,
    ///                     "hierarchyPath": "Organization > Building A",
    ///                     "metadata": [...],
    ///                     "childObjects": [
    ///                       {
    ///                         "id": "floor-guid",
    ///                         "name": "Floor 1",
    ///                         "parentObjectId": "building-guid",
    ///                         "hierarchyLevel": 2,
    ///                         "hierarchyPath": "Organization > Building A > Floor 1",
    ///                         "metadata": [...],
    ///                         "childObjects": [...]
    ///                       }
    ///                     ]
    ///                   }
    ///                 ]
    ///               }
    ///             ]
    ///           }
    ///         ]
    ///       }
    ///     ]
    ///   }
    /// }
    /// ```
    /// </remarks>
    [HttpGet("{productId:guid}")]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<HierarchicalEntityDataResponseDto>>> GetHierarchicalEntityDataByProduct(
        Guid productId,
        [FromQuery] bool onlyVisibleMetadata = true,
        [FromQuery] bool onlyActiveMetadata = true,
        [FromQuery] int pageSize = 100,
        [FromQuery] int pageNumber = 1,
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = null)
    {
        try
        {
            var query = new GetHierarchicalEntityDataQuery
            {
                ProductId = productId,
                OnlyVisibleMetadata = onlyVisibleMetadata,
                OnlyActiveMetadata = onlyActiveMetadata,
                PageSize = pageSize,
                PageNumber = pageNumber,
                SearchTerm = searchTerm,
                IsActive = isActive
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while processing the request",
                error = ex.Message,
                details = ex.StackTrace
            });
        }
    }

    #endregion

    #region Product Structure Creation Endpoints

    /// <summary>
    /// Create comprehensive product structure from JSON
    /// Creates Products, Objects, Metadata, and ObjectMetadata with proper hierarchical relationships
    /// </summary>
    /// <param name="command">Product structure creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Comprehensive creation response with all created entities</returns>
    /// <response code="200">Product structure created successfully</response>
    /// <response code="400">Invalid request data or validation errors</response>
    /// <response code="500">Internal server error</response>
    /// <example>
    /// POST /api/comprehensive-entity/create-product-structure
    /// {
    ///   "products": [
    ///     {
    ///       "id": "1750056667908",
    ///       "name": "Incentive management",
    ///       "type": "product",
    ///       "description": "Comprehensive incentive management system",
    ///       "version": "1.0.0",
    ///       "isActive": true,
    ///       "metadata": [
    ///         {
    ///           "id": "00017d5789cb-2ff1-026f-3a5a-bc8e41fbe67f",
    ///           "name": "Name",
    ///           "type": "Text",
    ///           "description": "Product name",
    ///           "required": true,
    ///           "isActive": true
    ///         }
    ///       ],
    ///       "features": [
    ///         {
    ///           "id": "1750056667909",
    ///           "name": "Real Estate Management",
    ///           "type": "feature",
    ///           "description": "Feature for managing real estate properties",
    ///           "isActive": true,
    ///           "metadata": [...],
    ///           "objects": [...]
    ///         }
    ///       ]
    ///     }
    ///   ]
    /// }
    /// </example>
    [HttpPost("create-product-structure")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ProductStructureCreationResult>>> CreateProductStructure(
        [FromBody] CreateProductStructureCommand command,
        CancellationToken cancellationToken = default)
    {
        var result = await Mediator.Send(command, cancellationToken);
        
        if (result.Succeeded)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    #endregion
}
