using System.Collections.Frozen;

namespace Application.Common.DataType;

/// <summary>
/// Centralized synonym mapping system for automatic data type detection
/// Maps field name patterns to their corresponding data types for intelligent detection
/// </summary>
public static class DataTypeSynonymMappings
{
    /// <summary>
    /// Comprehensive synonym mappings for all 29 supported data types
    /// Key: Data type name, Value: Array of synonyms/patterns
    /// </summary>
    private static readonly Dictionary<string, string[]> _synonymMappings = new()
    {
        // Number data type synonyms
        ["number"] = [
            "figure", "digit", "amount", "total", "sum", "count", "quantity", "volume",
            "statistic", "measure", "metric", "score", "rate", "index", "reading", "result", "level",
            "entry", "datapoint", "data_point", "number", "num", "qty", "vol", "amt", "cnt",
            "area", "size", "weight", "height", "width", "length", "depth", "distance", "radius",
            "floors", "rooms", "units", "capacity", "limit", "maximum", "minimum", "range",
            "sequence", "order", "position", "rank", "grade", "points", "marks"
        ],

        // Email data type synonyms
        ["email"] = [
            "email", "e-mail", "mail", "emailaddress", "email_address", "e_mail", "contact_email",
            "user_email", "admin_email", "support_email", "info_email", "sales_email",
            "notification_email", "reply_email", "sender_email", "recipient_email"
        ],

        // Phone data type synonyms
        ["phone"] = [
            "phone", "telephone", "tel", "mobile", "cell", "cellular", "contact", "number",
            "phone_number", "telephone_number", "mobile_number", "cell_number", "contact_number",
            "home_phone", "work_phone", "office_phone", "business_phone", "emergency_contact",
            "primary_phone", "secondary_phone", "fax", "fax_number"
        ],

        // URL data type synonyms
        ["url"] = [
            "url", "link", "website", "site", "web", "webpage", "weblink", "hyperlink",
            "web_url", "web_link", "web_site", "web_page", "homepage", "domain", "uri",
            "reference", "external_link", "internal_link", "redirect", "endpoint"
        ],

        // Address data type synonyms
        ["address"] = [
            "address", "location", "street", "place", "residence", "home", "office",
            "street_address", "home_address", "office_address", "billing_address", "shipping_address",
            "mailing_address", "physical_address", "postal_address", "delivery_address",
            "business_address", "residential_address", "geographic_location", "coordinates"
        ],

        // Date data type synonyms
        ["date"] = [
            "date", "day", "birthday", "birthdate", "anniversary", "deadline", "due_date",
            "start_date", "end_date", "expiry_date", "expiration_date", "creation_date",
            "delivery_date", "completion_date", "launch_date", "release_date", "publish_date",
            "effective_date", "valid_from", "valid_to", "schedule_date", "appointment_date"
        ],

        // DateTime data type synonyms
        ["datetime"] = [
            "datetime", "timestamp", "time_stamp", "created_at", "updated_at", "modified_at",
            "created_on", "updated_on", "modified_on", "last_login", "last_access",
            "login_time", "logout_time", "session_start", "session_end", "event_time",
            "occurrence_time", "scheduled_time", "appointment_time"
        ],

        // Time data type synonyms
        ["time"] = [
            "time", "hour", "minute", "duration", "period", "interval", "timespan",
            "start_time", "end_time", "opening_time", "closing_time", "business_hours",
            "working_hours", "office_hours", "schedule_time", "appointment_time",
            "meeting_time", "event_time", "session_time"
        ],

        // Boolean data type synonyms
        ["boolean"] = [
            "is", "has", "can", "should", "will", "active", "enabled", "visible", "available",
            "valid", "confirmed", "verified", "approved", "published", "featured", "premium",
            "public", "private", "locked", "unlocked", "open", "closed", "online", "offline",
            "flag", "status", "state", "condition", "toggle", "switch", "checkbox"
        ],

        // Currency data type synonyms
        ["currency"] = [
            "price", "cost", "amount", "fee", "charge", "rate", "salary", "wage", "income",
            "revenue", "profit", "loss", "budget", "expense", "payment", "refund", "discount",
            "tax", "total", "subtotal", "balance", "credit", "debit", "money", "cash",
            "currency", "dollar", "euro", "pound", "yen", "value", "worth"
        ],

        // Percentage data type synonyms
        ["percentage"] = [
            "percent", "percentage", "rate", "ratio", "proportion", "share", "portion",
            "fraction", "part", "commission", "interest", "discount", "markup", "margin",
            "growth", "decline", "increase", "decrease", "change", "variance", "deviation"
        ],

        // Text data type synonyms
        ["text"] = [
            "text", "string", "content", "message", "note", "comment", "remark", "observation",
            "title", "name", "label", "caption", "heading", "subject", "topic", "keyword",
            "tag", "category", "type", "kind", "sort", "class", "group", "section"
        ],

        // Textarea data type synonyms
        ["textarea"] = [
            "description", "details", "summary", "overview", "abstract", "content", "body",
            "text", "message", "comment", "note", "remark", "observation", "feedback",
            "review", "testimonial", "biography", "profile", "about", "introduction",
            "explanation", "instruction", "specification", "requirement", "terms", "conditions"
        ],

        // GUID data type synonyms
        ["guid"] = [
            "id", "identifier", "uuid", "guid", "key", "reference", "ref", "foreign_key",
            "primary_key", "unique_id", "record_id", "entity_id", "object_id", "item_id",
            "user_id", "customer_id", "product_id", "order_id", "transaction_id"
        ],

        // Rating data type synonyms
        ["rating"] = [
            "rating", "rate", "score", "review", "evaluation", "assessment", "grade", "mark",
            "star", "stars", "star_rating", "user_rating", "customer_rating", "product_rating",
            "service_rating", "quality_rating", "performance_rating", "satisfaction_rating",
            "feedback_score", "review_score", "evaluation_score", "assessment_score",
            "rating_value", "score_value", "grade_value", "mark_value", "points", "ranking"
        ],

        // Day data type synonyms
        ["day"] = [
            "day", "weekday", "day_of_week", "day_name", "week_day", "business_day",
            "working_day", "work_day", "calendar_day", "day_of_month", "day_number",
            "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday",
            "mon", "tue", "wed", "thu", "fri", "sat", "sun", "day_abbreviation"
        ],

        // Color data type synonyms
        ["color"] = [
            "color", "colour", "hex", "hex_color", "hex_code", "color_code", "rgb", "rgba",
            "hsl", "hsla", "color_value", "background_color", "foreground_color", "text_color",
            "border_color", "theme_color", "primary_color", "secondary_color", "accent_color",
            "brand_color", "custom_color", "color_scheme", "color_palette", "shade", "tint"
        ],

        // Multiselect data type synonyms
        ["multiselect"] = [
            "multiselect", "multi_select", "multiple_select", "multiple_choice", "multi_choice",
            "checkbox_list", "checklist", "multiple_options", "multi_options", "selection_list",
            "option_list", "choice_list", "multiple_values", "multi_values", "tags_select",
            "categories_select", "items_select", "features_select", "attributes_select"
        ],

        // Month data type synonyms
        ["month"] = [
            "month", "month_name", "month_number", "calendar_month", "fiscal_month",
            "january", "february", "march", "april", "may", "june", "july", "august",
            "september", "october", "november", "december", "jan", "feb", "mar", "apr",
            "jun", "jul", "aug", "sep", "oct", "nov", "dec", "month_abbreviation"
        ],

        // Select data type synonyms
        ["select"] = [
            "select", "dropdown", "drop_down", "choice", "option", "selection", "picker",
            "single_select", "single_choice", "single_option", "combo", "combobox", "combo_box",
            "list", "option_list", "choice_list", "selection_list", "menu", "dropdown_menu",
            "pick_list", "lookup", "reference", "category", "type", "status", "state"
        ],

        // File data type synonyms
        ["file"] = [
            "file", "upload", "attachment", "document", "doc", "pdf", "image", "picture",
            "photo", "video", "audio", "media", "asset", "resource", "download", "binary",
            "file_upload", "file_attachment", "document_upload", "media_upload", "asset_upload",
            "file_path", "file_name", "filename", "file_url", "document_path", "media_path"
        ],

        // Richtext data type synonyms
        ["richtext"] = [
            "richtext", "rich_text", "html", "html_content", "formatted_text", "wysiwyg",
            "editor_content", "rich_content", "markup", "html_markup", "formatted_content",
            "styled_text", "rich_editor", "text_editor", "content_editor", "html_editor",
            "article_content", "blog_content", "page_content", "post_content", "body_html"
        ],

        // Image data type synonyms
        ["image"] = [
            "image", "img", "picture", "pic", "photo", "photograph", "avatar", "thumbnail",
            "banner", "logo", "icon", "graphic", "illustration", "screenshot", "snapshot",
            "image_url", "image_path", "photo_url", "picture_url", "avatar_url", "thumbnail_url",
            "profile_image", "cover_image", "featured_image", "gallery_image", "product_image"
        ],

        // Year data type synonyms
        ["year"] = [
            "year", "year_value", "calendar_year", "fiscal_year", "academic_year", "birth_year",
            "graduation_year", "start_year", "end_year", "publication_year", "release_year",
            "model_year", "vintage_year", "year_number", "yyyy", "year_of_birth", "year_established",
            "year_founded", "year_created", "year_built", "year_manufactured", "year_published"
        ],

        // Radio data type synonyms
        ["radio"] = [
            "radio", "radio_button", "radio_group", "single_choice", "exclusive_choice",
            "option_group", "choice_group", "selection_group", "radio_list", "radio_options",
            "exclusive_select", "single_select", "one_choice", "either_or", "yes_no",
            "true_false", "gender", "status_choice", "type_choice", "category_choice"
        ],

        // Tag data type synonyms
        ["tag"] = [
            "tag", "tags", "label", "labels", "keyword", "keywords", "category", "categories",
            "topic", "topics", "hashtag", "hashtags", "chip", "chips", "badge", "badges",
            "tag_list", "keyword_list", "label_list", "category_list", "topic_list",
            "classification", "taxonomy", "metadata_tags", "content_tags", "search_tags"
        ],

        // Slider data type synonyms
        ["slider"] = [
            "slider", "range", "range_slider", "scale", "level", "intensity", "volume",
            "brightness", "opacity", "transparency", "zoom", "progress", "completion",
            "slider_value", "range_value", "scale_value", "level_value", "intensity_value",
            "min_max", "from_to", "between", "range_input", "slider_input", "scale_input"
        ],

        // Checkbox data type synonyms
        ["checkbox"] = [
            "checkbox", "check", "checked", "tick", "ticked", "boolean_flag", "yes_no_flag",
            "true_false_flag", "option_flag", "feature_flag", "setting_flag", "config_flag",
            "checkbox_value", "check_value", "boolean_value", "flag_value", "switch_value",
            "toggle_value", "on_off", "enabled_disabled", "active_inactive", "selected_unselected",
            // Fields starting with "is" and "should" patterns
            "is_", "should_", "has_", "can_", "will_", "must_", "allow_", "enable_", "disable_",
            "show_", "hide_", "display_", "visible_", "hidden_", "required_", "optional_",
            "readonly_", "editable_", "locked_", "unlocked_", "public_", "private_"
        ],

        // JSON data type synonyms
        ["json"] = [
            "json", "data", "config", "configuration", "settings", "options", "parameters",
            "metadata", "properties", "attributes", "details", "specification", "schema",
            "structure", "object", "array", "collection", "list", "map", "dictionary"
        ]
    };

    /// <summary>
    /// Frozen dictionary for optimal performance during lookups
    /// </summary>
    private static readonly FrozenDictionary<string, FrozenSet<string>> _frozenSynonymMappings =
        _synonymMappings.ToFrozenDictionary(
            kvp => kvp.Key,
            kvp => kvp.Value.ToFrozenSet(StringComparer.OrdinalIgnoreCase)
        );

    /// <summary>
    /// Get all synonyms for a specific data type
    /// </summary>
    /// <param name="dataType">The data type name</param>
    /// <returns>Set of synonyms for the data type, or empty set if not found</returns>
    public static FrozenSet<string> GetSynonymsForDataType(string dataType)
    {
        return _frozenSynonymMappings.TryGetValue(dataType.ToLowerInvariant(), out var synonyms)
            ? synonyms
            : FrozenSet<string>.Empty;
    }

    /// <summary>
    /// Determine data type based on field name using synonym mappings
    /// </summary>
    /// <param name="fieldName">The field name to analyze</param>
    /// <returns>Detected data type or null if no match found</returns>
    public static string? DetectDataTypeFromFieldName(string fieldName)
    {
        if (string.IsNullOrWhiteSpace(fieldName))
            return null;

        var normalizedFieldName = fieldName.ToLowerInvariant();

        // Priority 1: Check for boolean/checkbox patterns first (is*, should*, has*, can*, etc.)
        if (IsBooleanCheckboxPattern(fieldName, normalizedFieldName))
        {
            return "checkbox";
        }

        // Priority 2: Check each data type's synonyms for matches
        foreach (var (dataType, synonyms) in _frozenSynonymMappings)
        {
            // Skip checkbox since we already handled it above
            if (dataType == "checkbox")
                continue;

            // Check for exact matches or contains matches
            foreach (var synonym in synonyms)
            {
                if (normalizedFieldName.Contains(synonym, StringComparison.OrdinalIgnoreCase))
                {
                    return dataType;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Check if field name matches boolean/checkbox patterns (is*, should*, has*, can*, etc.)
    /// </summary>
    /// <param name="originalFieldName">The original field name (for case checking)</param>
    /// <param name="normalizedFieldName">The normalized field name to check</param>
    /// <returns>True if the field name matches boolean/checkbox patterns</returns>
    private static bool IsBooleanCheckboxPattern(string originalFieldName, string normalizedFieldName)
    {
        // Common boolean prefixes that indicate checkbox/boolean fields
        var booleanPrefixes = new[]
        {
            "is", "should", "has", "can", "will", "must", "allow", "enable", "disable",
            "show", "hide", "display", "visible", "hidden", "required", "optional",
            "readonly", "editable", "locked", "unlocked", "public", "private",
            "active", "inactive", "valid", "invalid", "confirmed", "unconfirmed",
            "approved", "unapproved", "published", "unpublished", "featured", "unfeatured"
        };

        // Check if field starts with any boolean prefix followed by underscore or uppercase letter
        foreach (var prefix in booleanPrefixes)
        {
            if (normalizedFieldName.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
            {
                // Check if it's followed by underscore, uppercase letter, or end of string
                if (normalizedFieldName.Length == prefix.Length ||
                    normalizedFieldName[prefix.Length] == '_' ||
                    (normalizedFieldName.Length > prefix.Length &&
                     char.IsUpper(originalFieldName[prefix.Length]))) // Check original case for camelCase
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Check if a field name matches any synonym for a specific data type
    /// </summary>
    /// <param name="fieldName">The field name to check</param>
    /// <param name="dataType">The data type to check against</param>
    /// <returns>True if the field name matches any synonym for the data type</returns>
    public static bool IsFieldNameMatchForDataType(string fieldName, string dataType)
    {
        if (string.IsNullOrWhiteSpace(fieldName) || string.IsNullOrWhiteSpace(dataType))
            return false;

        var synonyms = GetSynonymsForDataType(dataType);
        var normalizedFieldName = fieldName.ToLowerInvariant();

        return synonyms.Any(synonym => 
            normalizedFieldName.Contains(synonym, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Get all supported data types
    /// </summary>
    /// <returns>Collection of all supported data type names</returns>
    public static IEnumerable<string> GetSupportedDataTypes()
    {
        return _frozenSynonymMappings.Keys;
    }

    /// <summary>
    /// Context-based field patterns that should use Context table relationships
    /// These fields require lookup data from Context/Lookup tables
    /// </summary>
    private static readonly FrozenSet<string> _contextBasedFields = new[]
    {
        "currency", "phone", "mobile", "phonenumber", "mobilenumber", "country",
        "state", "province", "region", "city", "timezone", "language", "locale",
        "nationality", "countrycode", "currencycode", "phonecode", "areacode",
        "dialcode", "isocode", "languagecode", "localecode", "regioncode"
    }.ToFrozenSet(StringComparer.OrdinalIgnoreCase);

    /// <summary>
    /// Check if a field name indicates it should use Context table relationships
    /// </summary>
    /// <param name="fieldName">The field name to check</param>
    /// <returns>True if the field should use Context table relationships</returns>
    public static bool IsContextBasedField(string fieldName)
    {
        if (string.IsNullOrWhiteSpace(fieldName))
            return false;

        var normalizedFieldName = fieldName.ToLowerInvariant();

        // Check for exact matches or contains matches
        return _contextBasedFields.Any(contextField =>
            normalizedFieldName.Contains(contextField, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Get the suggested context name for a context-based field
    /// </summary>
    /// <param name="fieldName">The field name</param>
    /// <returns>Suggested context name or null if not a context-based field</returns>
    public static string? GetSuggestedContextName(string fieldName)
    {
        if (string.IsNullOrWhiteSpace(fieldName))
            return null;

        var normalizedFieldName = fieldName.ToLowerInvariant();

        // Map field patterns to context names
        var contextMappings = new Dictionary<string, string>
        {
            ["currency"] = "Currencies",
            ["phone"] = "PhoneTypes",
            ["mobile"] = "PhoneTypes",
            ["phonenumber"] = "PhoneTypes",
            ["mobilenumber"] = "PhoneTypes",
            ["country"] = "Countries",
            ["state"] = "States",
            ["province"] = "Provinces",
            ["region"] = "Regions",
            ["city"] = "Cities",
            ["timezone"] = "TimeZones",
            ["language"] = "Languages",
            ["locale"] = "Locales",
            ["nationality"] = "Nationalities"
        };

        foreach (var (pattern, contextName) in contextMappings)
        {
            if (normalizedFieldName.Contains(pattern, StringComparison.OrdinalIgnoreCase))
            {
                return contextName;
            }
        }

        return null;
    }

    /// <summary>
    /// Add custom synonyms for a data type (for runtime extension)
    /// Note: This creates a new mapping and should be used sparingly for performance
    /// </summary>
    /// <param name="dataType">The data type name</param>
    /// <param name="additionalSynonyms">Additional synonyms to add</param>
    /// <returns>True if synonyms were added successfully</returns>
    public static bool TryAddSynonyms(string dataType, params string[] additionalSynonyms)
    {
        if (string.IsNullOrWhiteSpace(dataType) || additionalSynonyms == null || additionalSynonyms.Length == 0)
            return false;

        var normalizedDataType = dataType.ToLowerInvariant();

        if (_synonymMappings.TryGetValue(normalizedDataType, out var existingSynonyms))
        {
            var updatedSynonyms = existingSynonyms.Concat(additionalSynonyms).Distinct().ToArray();
            _synonymMappings[normalizedDataType] = updatedSynonyms;
            return true;
        }

        return false;
    }
}
