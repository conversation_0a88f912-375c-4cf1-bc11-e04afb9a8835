import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// A customizable percentage input widget following the 'this_componentName_input' naming convention
/// This widget handles percentage input with validation based on API configuration
class ThisPercentageInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final String value;
  final ValueChanged<String> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  
  // API-based validation parameters
  final String? validationPattern;
  final int? minLength;
  final int? maxLength;
  final double? minValue;
  final double? maxValue;
  final int? decimalPlaces;
  final double? stepValue;
  final String? inputMask;
  final String? requiredErrorMessage;
  final String? patternErrorMessage;
  final String? minLengthErrorMessage;
  final String? maxLengthErrorMessage;
  final String? minValueErrorMessage;
  final String? maxValueErrorMessage;
  
  // Percentage-specific parameters
  final bool showPercentageSymbol;
  final bool allowDecimals;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool autoFocus;
  final String? Function(String)? customValidation;

  const ThisPercentageInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.validationPattern,
    this.minLength,
    this.maxLength,
    this.minValue,
    this.maxValue,
    this.decimalPlaces,
    this.stepValue,
    this.inputMask,
    this.requiredErrorMessage,
    this.patternErrorMessage,
    this.minLengthErrorMessage,
    this.maxLengthErrorMessage,
    this.minValueErrorMessage,
    this.maxValueErrorMessage,
    this.showPercentageSymbol = true,
    this.allowDecimals = true,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisPercentageInput> createState() => _ThisPercentageInputState();
}

class _ThisPercentageInputState extends State<ThisPercentageInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
    _focusNode = FocusNode();
    
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void didUpdateWidget(ThisPercentageInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  double? _parsePercentage(String value) {
    if (value.trim().isEmpty) return null;
    
    // Remove percentage symbol for parsing
    String cleanValue = value.replaceAll('%', '').trim();
    return double.tryParse(cleanValue);
  }

  List<String> _validateValue(String value) {
    final errors = <String>[];

    // 1. Required validation
    if (widget.required && value.trim().isEmpty) {
      errors.add(widget.requiredErrorMessage ?? '${widget.label} is required');
      return errors;
    }

    // Skip other validations if empty and not required
    if (value.trim().isEmpty && !widget.required) {
      return errors;
    }

    // 2. Pattern validation (from API)
    if (widget.validationPattern != null) {
      final regex = RegExp(widget.validationPattern!);
      if (!regex.hasMatch(value)) {
        errors.add(widget.patternErrorMessage ?? 'Please enter a valid percentage');
        return errors;
      }
    }

    // 3. Length validation
    if (widget.minLength != null && value.length < widget.minLength!) {
      errors.add(widget.minLengthErrorMessage ?? '${widget.label} is too short');
      return errors;
    }

    if (widget.maxLength != null && value.length > widget.maxLength!) {
      errors.add(widget.maxLengthErrorMessage ?? '${widget.label} is too long');
      return errors;
    }

    // 4. Numeric value validation
    final percentage = _parsePercentage(value);
    if (percentage == null && value.trim().isNotEmpty) {
      errors.add('Please enter a valid percentage');
      return errors;
    }

    if (percentage != null) {
      // Default percentage range validation (0-100)
      final minVal = widget.minValue ?? 0;
      final maxVal = widget.maxValue ?? 100;

      if (percentage < minVal) {
        errors.add(widget.minValueErrorMessage ?? 'Percentage cannot be less than $minVal%');
        return errors;
      }

      if (percentage > maxVal) {
        errors.add(widget.maxValueErrorMessage ?? 'Percentage cannot be greater than $maxVal%');
        return errors;
      }

      // Decimal places validation
      if (!widget.allowDecimals && percentage != percentage.floor()) {
        errors.add('Decimal values are not allowed');
        return errors;
      }

      if (widget.decimalPlaces != null) {
        final decimalPart = value.split('.').length > 1 ? value.split('.')[1].replaceAll('%', '') : '';
        if (decimalPart.length > widget.decimalPlaces!) {
          errors.add('Maximum ${widget.decimalPlaces} decimal places allowed');
          return errors;
        }
      }

      // Step validation
      if (widget.stepValue != null) {
        final minVal = widget.minValue ?? 0;
        final remainder = (percentage - minVal) % widget.stepValue!;
        if (remainder.abs() >= 0.0001) {
          errors.add('Value must be in increments of ${widget.stepValue}%');
          return errors;
        }
      }
    }

    // 5. Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(value);
      if (customError != null) {
        errors.add(customError);
        return errors;
      }
    }

    return errors;
  }

  String _formatPercentage(String value) {
    final percentage = _parsePercentage(value);
    if (percentage == null) return value;

    String formatted = percentage.toString();
    
    // Add decimal places if specified
    if (widget.decimalPlaces != null) {
      formatted = percentage.toStringAsFixed(widget.decimalPlaces!);
    }

    // Add percentage symbol if enabled
    if (widget.showPercentageSymbol && !formatted.endsWith('%')) {
      formatted += '%';
    }

    return formatted;
  }

  void _handleChange(String value) {
    widget.onChanged(value);
    
    // Real-time validation (only if not validating on blur)
    if (!widget.validateOnBlur) {
      final errors = _validateValue(value);
      setState(() {
        _errors = errors;
        _isValidated = value.trim().isNotEmpty;
      });
      
      widget.onValidation?.call(errors);
    }
  }

  void _handleBlur() {
    // Format the percentage on blur if it's valid
    String formattedValue = widget.value;
    final percentage = _parsePercentage(widget.value);
    
    if (percentage != null && _errors.isEmpty) {
      formattedValue = _formatPercentage(widget.value);
      if (formattedValue != widget.value) {
        widget.onChanged(formattedValue);
      }
    }

    // Validate on blur if enabled
    if (widget.validateOnBlur) {
      final errors = _validateValue(formattedValue);
      setState(() {
        _errors = errors;
        _isValidated = formattedValue.trim().isNotEmpty;
      });
      
      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated || widget.value.trim().isEmpty) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  String _getPlaceholder() {
    if (widget.placeholder != null) return widget.placeholder!;
    
    final minVal = widget.minValue ?? 0;
    final maxVal = widget.maxValue ?? 100;
    return 'Enter percentage ($minVal-$maxVal%)...';
  }

  List<String> _getHelperTexts() {
    final helpers = <String>[];
    
    final minVal = widget.minValue ?? 0;
    final maxVal = widget.maxValue ?? 100;
    helpers.add('Range: $minVal% - $maxVal%');
    
    if (widget.stepValue != null) {
      helpers.add('Step: ${widget.stepValue}%');
    }
    
    if (widget.decimalPlaces != null) {
      helpers.add('Max ${widget.decimalPlaces} decimal places');
    } else if (!widget.allowDecimals) {
      helpers.add('Whole numbers only');
    }
    
    return helpers;
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors && widget.value.trim().isNotEmpty;
    final helperTexts = _getHelperTexts();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled 
                    ? ColorPalette.placeHolderTextColor 
                    : ColorPalette.white,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        
        // Input Field
        TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          enabled: !widget.disabled,
          readOnly: widget.readOnly,
          keyboardType: TextInputType.numberWithOptions(decimal: widget.allowDecimals),
          textInputAction: TextInputAction.next,
          onChanged: _handleChange,
          onFieldSubmitted: (_) => _handleBlur(),
          onTapOutside: (_) => _handleBlur(),
          decoration: InputDecoration(
            hintText: _getPlaceholder(),
            hintStyle: LexendTextStyles.lexend14Regular.copyWith(
              color: ColorPalette.placeHolderTextColor,
            ),
            prefixIcon: widget.showIcon 
                ? Icon(Icons.percent, size: 20, color: ColorPalette.placeHolderTextColor)
                : null,
            suffixIcon: _getValidationIcon(),
            suffixText: widget.showPercentageSymbol ? '%' : null,
            suffixStyle: LexendTextStyles.lexend14Regular.copyWith(
              color: ColorPalette.placeHolderTextColor,
            ),
            errorText: hasErrors ? _errors.first : null,
            errorStyle: LexendTextStyles.lexend12Regular.copyWith(
              color: const Color(0xFFC73E1D),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: hasErrors 
                    ? const Color(0xFFC73E1D)
                    : (isValid ? ColorPalette.green : ColorPalette.gray300),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: hasErrors 
                    ? const Color(0xFFC73E1D)
                    : (isValid ? ColorPalette.green : ColorPalette.gray300),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: hasErrors 
                    ? const Color(0xFFC73E1D)
                    : (isValid ? ColorPalette.green : ColorPalette.white),
                width: 2,
              ),
            ),
          ),
          style: LexendTextStyles.lexend14Regular.copyWith(
            color: widget.disabled 
                ? ColorPalette.placeHolderTextColor 
                : ColorPalette.white,
          ),
          inputFormatters: [
            FilteringTextInputFormatter.allow(
              widget.allowDecimals 
                  ? RegExp(r'[0-9\.\%]') 
                  : RegExp(r'[0-9\%]')
            ),
          ],
        ),
        
        // Helper texts (only show when no errors)
        if (!hasErrors && helperTexts.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: helperTexts.map((text) => 
                Padding(
                  padding: const EdgeInsets.only(bottom: 2),
                  child: Text(
                    text,
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                )
              ).toList(),
            ),
          ),
      ],
    );
  }
}
