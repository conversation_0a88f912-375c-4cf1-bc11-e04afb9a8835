using Abstraction.Database.Repositories;
using Application.Templates.DTOs;
using Application.Templates.Specifications;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Templates.Queries;

/// <summary>
/// Handler for GetTemplateByIdQuery
/// </summary>
public class GetTemplateByIdQueryHandler : IRequestHandler<GetTemplateByIdQuery, Result<TemplateDto>>
{
    private readonly IReadRepository<Template> _templateRepository;
    private readonly ILogger<GetTemplateByIdQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetTemplateByIdQueryHandler(
        IReadRepository<Template> templateRepository,
        ILogger<GetTemplateByIdQueryHandler> logger)
    {
        _templateRepository = templateRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<TemplateDto>> Handle(GetTemplateByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting template by ID: {TemplateId}", request.Id);

            // Use specification to get template with product
            var spec = new TemplateByIdSpec(request.Id);
            var template = await _templateRepository.GetBySpecAsync(spec, cancellationToken);

            if (template == null)
            {
                _logger.LogWarning("Template not found with ID: {TemplateId}", request.Id);
                return Result<TemplateDto>.Failure("Template not found");
            }

            // Map to DTO
            var templateDto = new TemplateDto
            {
                Id = template.Id,
                ProductId = template.ProductId,
                ProductName = "Unknown", // No relationship with Product table
                Version = template.Version,
                Stage = template.Stage,
                TemplateJson = template.TemplateJson,
                CreatedAt = template.CreatedAt,
                CreatedBy = template.CreatedBy,
                PublishedAt = template.PublishedAt,
                IsActive = template.IsActive,
                IsDeleted = template.IsDeleted
            };

            _logger.LogInformation("Successfully retrieved template: {TemplateId}", request.Id);
            return Result<TemplateDto>.Success(templateDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting template by ID: {TemplateId}", request.Id);
            return Result<TemplateDto>.Failure("Failed to retrieve template");
        }
    }
}
