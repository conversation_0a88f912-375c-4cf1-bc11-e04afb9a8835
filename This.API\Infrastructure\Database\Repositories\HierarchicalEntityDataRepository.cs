using Abstraction.Database.Repositories;
using Application.ComprehensiveEntityData.DTOs;
using Domain.MultiTenancy;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Npgsql;
using System.Data;
using Dapper;
using Finbuckle.MultiTenant;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Infrastructure.Database.Repositories;

/// <summary>
/// CLEAN: Repository for hierarchical entity data - ONLY DATA ACCESS
/// NO business logic, NO hardcoded SQL
/// Uses separated query files and proper DTOs based on actual entities
/// </summary>
public class HierarchicalEntityDataRepository : IHierarchicalEntityDataRepository
{
    private readonly string _connectionString;
    private readonly ILogger<HierarchicalEntityDataRepository> _logger;
    private readonly ITenantInfo _tenantInfo;

    public HierarchicalEntityDataRepository(
        IOptions<DatabaseSettings> options,
        ILogger<HierarchicalEntityDataRepository> logger,
        ITenantInfo tenantInfo)
    {
        _connectionString = options.Value.ConnectionString;
        _logger = logger;
        _tenantInfo = tenantInfo;
    }

    /// <summary>
    /// Create a new database connection
    /// </summary>
    private IDbConnection CreateConnection() => new NpgsqlConnection(_connectionString);

    /// <summary>
    /// Get the current tenant ID from the injected tenant info
    /// </summary>
    private string GetCurrentTenantId()
    {
        // Get tenant ID from the injected ITenantInfo
        if (_tenantInfo != null && !string.IsNullOrEmpty(_tenantInfo.Id))
        {
            return _tenantInfo.Id;
        }

        // Fallback to default tenant
        return "default";
    }

    /// <summary>
    /// Get hierarchical entity data with proper parent-child nesting
    /// </summary>
    public async Task<object> GetHierarchicalEntityDataAsync(
        Guid? productId = null,
        Guid? featureId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Fetching hierarchical entity data with optimized queries");

            using var connection = CreateConnection();
            connection.Open();

            // STEP 1: Get filtered products with bulk query
            var products = await GetProductsAsync(connection, productId, isActive, searchTerm, pageNumber, pageSize);

            if (!products.Any())
            {
                return new HierarchicalEntityDataResponseDto();
            }

            var productIds = products.Select(p => p.Id).ToArray();

            // STEP 2: Get ALL objects for these products directly (Features removed)
            var objects = await GetObjectsAsync(connection, productIds, null, isActive, searchTerm);

            // STEP 3: Get metadata for all entities (2 optimized bulk queries) - NO VALUES (Features removed)
            _logger.LogInformation("Fetching metadata for {ProductCount} products", productIds.Length);

            var productMetadata = await GetProductMetadataAsync(connection, productIds, onlyVisibleMetadata, onlyActiveMetadata);
            _logger.LogInformation("Found {ProductMetadataCount} product metadata entries", productMetadata.Sum(x => x.Value.Count));

            var objectIds = objects.Select(o => o.Id).ToArray();
            var objectMetadata = await GetObjectMetadataAsync(connection, objectIds, onlyVisibleMetadata, onlyActiveMetadata);
            _logger.LogInformation("Found {ObjectMetadataCount} object metadata entries for {ObjectCount} objects", objectMetadata.Sum(x => x.Value.Count), objectIds.Length);

            // STEP 5: Build hierarchical structure with metadata
            var response = new HierarchicalEntityDataResponseDto();

            foreach (var product in products)
            {
                var productDto = new ProductHierarchicalDto
                {
                    Id = product.Id,
                    Name = product.Name,
                    Description = product.Description,
                    Version = product.Version,
                    IsActive = product.IsActive,
                    Metadata = productMetadata.ContainsKey(product.Id) ? productMetadata[product.Id] : new List<MetadataWithValuesDto>(),
                };

                // Features removed - add objects directly to product
                productDto.RootObjects = new List<ObjectHierarchicalDto>();

                // Get root objects (ParentObjectId = null) for this product directly
                var rootObjects = objects.Where(o => o.ProductId == product.Id && o.ParentObjectId == null).ToList();

                foreach (var rootObject in rootObjects)
                {
                    var rootObjectDto = BuildObjectHierarchy(rootObject, objects.ToList(), objectMetadata, 0, rootObject.Name);
                    productDto.RootObjects.Add(rootObjectDto);
                }

                response.Products.Add(productDto);
            }

            _logger.LogInformation("Successfully built hierarchical structure for {ProductCount} products", products.Count());
            return (object)response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching hierarchical entity data");
            throw;
        }
    }

    /// <summary>
    /// Build object hierarchy recursively with proper parent-child nesting and metadata
    /// </summary>
    private ObjectHierarchicalDto BuildObjectHierarchy(
        ObjectDto obj,
        List<ObjectDto> allObjects,
        Dictionary<Guid, List<MetadataWithValuesDto>> objectMetadata,
        int hierarchyLevel,
        string hierarchyPath)
    {
        var objectDto = new ObjectHierarchicalDto
        {
            Id = obj.Id,
            Name = obj.Name,
            Description = obj.Description,
            ParentObjectId = obj.ParentObjectId,
            IsActive = obj.IsActive,
            HierarchyLevel = hierarchyLevel,
            HierarchyPath = hierarchyPath,
            Metadata = objectMetadata.ContainsKey(obj.Id) ? objectMetadata[obj.Id] : new List<MetadataWithValuesDto>(),
            ChildObjects = new List<ObjectHierarchicalDto>()
        };

        // Get child objects for this object
        var childObjects = allObjects.Where(o => o.ParentObjectId == obj.Id).ToList();

        foreach (var childObject in childObjects)
        {
            var childDto = BuildObjectHierarchy(
                childObject,
                allObjects,
                objectMetadata,
                hierarchyLevel + 1,
                $"{hierarchyPath} > {childObject.Name}");

            objectDto.ChildObjects.Add(childDto);
        }

        return objectDto;
    }

    /// <summary>
    /// Get product metadata with datatypes only (no values) using separated query and DTOs
    /// </summary>
    private async Task<Dictionary<Guid, List<MetadataWithValuesDto>>> GetProductMetadataAsync(
        IDbConnection connection,
        Guid[] productIds,
        bool onlyVisible,
        bool onlyActive)
    {
        var sql = HierarchicalEntityDataQueries.AddMetadataFilters(
            HierarchicalEntityDataQueries.GetProductMetadataWithDataTypesSql,
            onlyVisible,
            onlyActive,
            "pm");

        var tenantId = GetCurrentTenantId();
        var metadataData = await connection.QueryAsync<ProductMetadataDto>(sql, new { ProductIds = productIds, TenantId = tenantId });
        _logger.LogInformation("Product metadata query returned {Count} rows for {ProductCount} products", metadataData.Count(), productIds.Length);

        var result = new Dictionary<Guid, List<MetadataWithValuesDto>>();

        foreach (var group in metadataData.GroupBy(x => x.ProductId))
        {
            var metadataList = new List<MetadataWithValuesDto>();

            foreach (var item in group)
            {
                try
                {
                    var metadata = new MetadataWithValuesDto
                    {
                        Metadata = new MetadataInfoDto
                        {
                            Id = item.MetadataId,
                            Name = item.Name ?? "",
                            DisplayLabel = item.DisplayLabel ?? "",
                            HelpText = item.HelpText,
                            FieldOrder = item.FieldOrder,
                            IsVisible = item.IsVisible,
                            IsReadonly = item.IsReadonly,
                            ValidationPattern = item.ValidationPattern, // Updated property name
                            MinLength = item.MinLength, // Updated property name
                            MaxLength = item.MaxLength, // Updated property name
                            MinValue = item.MinValue, // Updated property name
                            MaxValue = item.MaxValue, // Updated property name
                            IsRequired = item.IsRequired, // Updated property name
                            Placeholder = item.Placeholder, // Updated property name
                            DefaultOptions = item.DefaultOptions, // Updated property name
                            MaxSelections = item.MaxSelections, // Updated property name
                            AllowedFileTypes = item.AllowedFileTypes, // Updated property name
                            MaxFileSize = item.MaxFileSize, // Updated property name
                            ErrorMessage = item.ErrorMessage, // Updated property name
                            DataType = new DataTypeInfoDto
                            {
                                Id = item.DataTypeId,
                                Name = item.DataTypeName ?? "",
                                DisplayName = item.DataTypeDisplayName ?? "",
                                Category = item.DataTypeCategory,
                                UiComponent = item.DataTypeUiComponent,
                                ValidationPattern = item.DataTypeValidationPattern,
                                MinLength = item.DataTypeMinLength,
                                MaxLength = item.DataTypeMaxLength,
                                MinValue = item.DataTypeMinValue,
                                MaxValue = item.DataTypeMaxValue,
                                DecimalPlaces = item.DataTypeDecimalPlaces,
                                StepValue = item.DataTypeStepValue,
                                IsRequired = item.DataTypeIsRequired,
                                InputType = item.DataTypeInputType,
                                InputMask = item.DataTypeInputMask,
                                Placeholder = item.DataTypePlaceholder,
                                HtmlAttributes = item.DataTypeHtmlAttributes,
                                DefaultOptions = item.DataTypeDefaultOptions,
                                AllowsMultiple = item.DataTypeAllowsMultiple,
                                AllowsCustomOptions = item.DataTypeAllowsCustomOptions,
                                MaxSelections = item.DataTypeMaxSelections,
                                AllowedFileTypes = item.DataTypeAllowedFileTypes,
                                MaxFileSizeBytes = item.DataTypeMaxFileSizeBytes,
                                RequiredErrorMessage = item.DataTypeRequiredErrorMessage,
                                PatternErrorMessage = item.DataTypePatternErrorMessage,
                                MinLengthErrorMessage = item.DataTypeMinLengthErrorMessage,
                                MaxLengthErrorMessage = item.DataTypeMaxLengthErrorMessage,
                                MinValueErrorMessage = item.DataTypeMinValueErrorMessage,
                                MaxValueErrorMessage = item.DataTypeMaxValueErrorMessage,
                                FileTypeErrorMessage = item.DataTypeFileTypeErrorMessage,
                                FileSizeErrorMessage = item.DataTypeFileSizeErrorMessage,
                                IsActive = item.DataTypeIsActive
                            },
                            MetadataLink = new MetadataLinkInfoDto
                            {
                                ObjectMetaDataId = item.ProductMetadataId,
                                IsUnique = item.IsUnique,
                                IsActive = item.MetadataLinkIsActive,
                                ShouldVisibleInList = item.ShouldVisibleInList,
                                ShouldVisibleInEdit = item.ShouldVisibleInEdit,
                                ShouldVisibleInCreate = item.ShouldVisibleInCreate,
                                ShouldVisibleInView = item.ShouldVisibleInView,
                                IsCalculate = item.IsCalculate
                            }
                        },
                        Values = new List<ValueInfoDto>() // Empty values list
                    };

                    metadataList.Add(metadata);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Error processing product metadata item: {Error}", ex.Message);
                    // Skip this item and continue
                }
            }

            result[group.Key] = metadataList;
        }

        return result;
    }

    /// <summary>
    /// Get object metadata with datatypes only (no values) using separated query and DTOs
    /// </summary>
    private async Task<Dictionary<Guid, List<MetadataWithValuesDto>>> GetObjectMetadataAsync(
        IDbConnection connection,
        Guid[] objectIds,
        bool onlyVisible,
        bool onlyActive)
    {
        var sql = HierarchicalEntityDataQueries.AddMetadataFilters(
            HierarchicalEntityDataQueries.GetObjectMetadataWithDataTypesSql,
            onlyVisible,
            onlyActive,
            "om");

        var tenantId = GetCurrentTenantId();
        var metadataData = await connection.QueryAsync<ObjectMetadataDto>(sql, new { ObjectIds = objectIds, TenantId = tenantId });
        _logger.LogInformation("Object metadata query returned {Count} rows for {ObjectCount} objects", metadataData.Count(), objectIds.Length);

        var result = new Dictionary<Guid, List<MetadataWithValuesDto>>();

        foreach (var group in metadataData.GroupBy(x => x.ObjectId))
        {
            var metadataList = new List<MetadataWithValuesDto>();

            foreach (var item in group)
            {
                try
                {
                    var metadata = new MetadataWithValuesDto
                    {
                        Metadata = new MetadataInfoDto
                        {
                            Id = item.MetadataId,
                            Name = item.Name ?? "",
                            DisplayLabel = item.DisplayLabel ?? "",
                            IsVisible = item.IsVisible ?? false,
                            Placeholder = item.Placeholder,
                            DefaultOptions = item.DefaultOptions,
                            IsRequired = item.IsRequired,
                            FieldOrder = item.FieldOrder,
                            ValidationPattern = item.ValidationPattern,
                            MinLength = item.MinLength,
                            MaxLength = item.MaxLength,
                            MinValue = item.MinValue,
                            MaxValue = item.MaxValue,
                            MaxSelections = item.MaxSelections,
                            AllowedFileTypes = item.AllowedFileTypes,
                            MaxFileSize = item.MaxFileSize,
                            ErrorMessage = item.ErrorMessage,
                            IsReadonly = item.IsReadonly,
                            HelpText = item.HelpText,
                            DataType = new DataTypeInfoDto
                            {
                                Id = item.DataTypeId,
                                Name = item.DataTypeName ?? "",
                                DisplayName = item.DataTypeDisplayName ?? "",
                                Category = item.DataTypeCategory,
                                UiComponent = item.DataTypeUiComponent,
                                ValidationPattern = item.DataTypeValidationPattern,
                                MinLength = item.DataTypeMinLength,
                                MaxLength = item.DataTypeMaxLength,
                                MinValue = item.DataTypeMinValue,
                                MaxValue = item.DataTypeMaxValue,
                                IsRequired = item.DataTypeIsRequired,
                                InputType = item.DataTypeInputType,
                                InputMask = item.DataTypeInputMask,
                                Placeholder = item.DataTypePlaceholder,
                                DefaultOptions = item.DataTypeDefaultOptions,
                                AllowsMultiple = item.DataTypeAllowsMultiple
                            },
                            MetadataLink = new MetadataLinkInfoDto
                            {
                                ObjectMetaDataId = item.ObjectMetadataId,
                                IsUnique = item.IsUnique,
                                IsActive = item.MetadataLinkIsActive,
                                ShouldVisibleInList = item.ShouldVisibleInList,
                                ShouldVisibleInEdit = item.ShouldVisibleInEdit,
                                ShouldVisibleInCreate = item.ShouldVisibleInCreate,
                                ShouldVisibleInView = item.ShouldVisibleInView,
                                IsCalculate = item.IsCalculate
                            }
                        },
                        Values = new List<ValueInfoDto>() // Empty values list
                    };

                    metadataList.Add(metadata);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Error processing metadata item: {Error}", ex.Message);
                    // Skip this item and continue
                }
            }

            result[group.Key] = metadataList;
        }

        return result;
    }

    #region Private Data Access Methods

    /// <summary>
    /// Get products with filters using separated query
    /// </summary>
    private async Task<IEnumerable<ProductDto>> GetProductsAsync(
        IDbConnection connection,
        Guid? productId,
        bool? isActive,
        string? searchTerm,
        int pageNumber,
        int pageSize)
    {
        var sql = HierarchicalEntityDataQueries.AddProductFilters(
            hasProductId: productId.HasValue,
            hasIsActive: isActive.HasValue,
            hasSearchTerm: !string.IsNullOrWhiteSpace(searchTerm),
            hasPagination: true);

        var parameters = new DynamicParameters();
        var tenantId = GetCurrentTenantId();
        parameters.Add("TenantId", tenantId);

        if (productId.HasValue)
            parameters.Add("ProductId", productId.Value);

        if (isActive.HasValue)
            parameters.Add("IsActive", isActive.Value);

        if (!string.IsNullOrWhiteSpace(searchTerm))
            parameters.Add("SearchTerm", $"%{searchTerm}%");

        parameters.Add("Offset", (pageNumber - 1) * pageSize);
        parameters.Add("Limit", pageSize);

        return await connection.QueryAsync<ProductDto>(sql, parameters);
    }

    /// <summary>
    /// Get objects for products using separated query (Features removed)
    /// </summary>
    private async Task<IEnumerable<ObjectDto>> GetObjectsAsync(
        IDbConnection connection,
        Guid[] productIds,
        Guid? objectId = null,
        bool? isActive = null,
        string? searchTerm = null)
    {
        var sql = HierarchicalEntityDataQueries.AddObjectFilters(
            hasObjectId: objectId.HasValue,
            hasIsActive: isActive.HasValue,
            hasSearchTerm: !string.IsNullOrWhiteSpace(searchTerm));

        var parameters = new DynamicParameters();
        var tenantId = GetCurrentTenantId();
        parameters.Add("TenantId", tenantId);
        parameters.Add("ProductIds", productIds);

        if (objectId.HasValue)
            parameters.Add("ObjectId", objectId.Value);

        if (isActive.HasValue)
            parameters.Add("IsActive", isActive.Value);

        if (!string.IsNullOrWhiteSpace(searchTerm))
            parameters.Add("SearchTerm", $"%{searchTerm}%");

        return await connection.QueryAsync<ObjectDto>(sql, parameters);
    }

    #endregion
}
