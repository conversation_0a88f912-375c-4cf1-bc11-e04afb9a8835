using Application.MetadataManagement.DTOs;
using Application.MetadataManagement.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Commands;

/// <summary>
/// Update Metadata command handler
/// </summary>
public class UpdateMetadataCommandHandler : IRequestHandler<UpdateMetadataCommand, Result<MetadataDto>>
{
    private readonly IRepository<Domain.Entities.Metadata> _repository;
    private readonly IRepository<DataType> _dataTypeRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateMetadataCommandHandler(
        IRepository<Domain.Entities.Metadata> repository,
        IRepository<DataType> dataTypeRepository)
    {
        _repository = repository;
        _dataTypeRepository = dataTypeRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<MetadataDto>> Handle(UpdateMetadataCommand request, CancellationToken cancellationToken)
    {
        // Get existing metadata
        var metadata = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (metadata == null)
        {
            return Result<MetadataDto>.Failure($"Metadata with ID '{request.Id}' not found.");
        }

        // Validate DataType exists
        var dataType = await _dataTypeRepository.GetByIdAsync(request.DataTypeId, cancellationToken);
        if (dataType == null)
        {
            return Result<MetadataDto>.Failure($"DataType with ID '{request.DataTypeId}' not found.");
        }

        // Check if another Metadata with same key already exists
        var existingMetadata = await _repository.GetBySpecAsync(new MetadataByKeySpec(request.MetadataKey), cancellationToken);
        if (existingMetadata != null && existingMetadata.Id != request.Id)
        {
            return Result<MetadataDto>.Failure($"Metadata with key '{request.MetadataKey}' already exists.");
        }

        // Update metadata properties
        metadata.Name = request.MetadataKey; // Updated: Name maps to Name property
        metadata.DataTypeId = request.DataTypeId;
        metadata.ValidationPattern = request.ValidationPattern; // Updated property name
        metadata.MinLength = request.MinLength; // Updated property name
        metadata.MaxLength = request.MaxLength; // Updated property name
        metadata.MinValue = request.MinValue; // Updated property name
        metadata.MaxValue = request.MaxValue; // Updated property name
        metadata.IsRequired = request.IsRequired; // Updated property name
        metadata.Placeholder = request.Placeholder; // Updated property name
        metadata.DefaultOptions = request.DefaultOptions; // Updated property name
        metadata.MaxSelections = request.MaxSelections; // Updated property name
        metadata.AllowedFileTypes = request.AllowedFileTypes; // Updated property name
        metadata.MaxFileSize = request.MaxFileSize; // Updated property name
        metadata.ErrorMessage = request.ErrorMessage; // Updated property name
        metadata.DisplayLabel = request.DisplayLabel;
        metadata.HelpText = request.HelpText;
        metadata.FieldOrder = request.FieldOrder;
        metadata.IsVisible = request.IsVisible;
        metadata.IsReadonly = request.IsReadonly;

        await _repository.UpdateAsync(metadata, cancellationToken);

        var dto = new MetadataDto
        {
            Id = metadata.Id,
            Name = metadata.Name, // Updated: Name property maps to Name
            DataTypeId = metadata.DataTypeId,
            DataTypeName = dataType.Name,
            ValidationPattern = metadata.ValidationPattern, // Updated property name
            MinLength = metadata.MinLength, // Updated property name
            MaxLength = metadata.MaxLength, // Updated property name
            MinValue = metadata.MinValue, // Updated property name
            MaxValue = metadata.MaxValue, // Updated property name
            IsRequired = metadata.IsRequired, // Updated property name
            Placeholder = metadata.Placeholder, // Updated property name
            DefaultOptions = metadata.DefaultOptions, // Updated property name
            MaxSelections = metadata.MaxSelections, // Updated property name
            AllowedFileTypes = metadata.AllowedFileTypes, // Updated property name
            MaxFileSize = metadata.MaxFileSize, // Updated property name
            ErrorMessage = metadata.ErrorMessage, // Updated property name
            DisplayLabel = metadata.DisplayLabel,
            HelpText = metadata.HelpText,
            FieldOrder = metadata.FieldOrder,
            IsVisible = metadata.IsVisible,
            IsReadonly = metadata.IsReadonly,
            CreatedAt = metadata.CreatedAt,
            CreatedBy = metadata.CreatedBy ?? Guid.Empty,
            ModifiedAt = metadata.ModifiedAt,
            ModifiedBy = metadata.ModifiedBy
        };

        return Result<MetadataDto>.Success(dto);
    }
}
