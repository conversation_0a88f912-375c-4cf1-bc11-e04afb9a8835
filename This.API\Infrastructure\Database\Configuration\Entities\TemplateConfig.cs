using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Template entity
/// </summary>
public class TemplateConfig : IEntityTypeConfiguration<Template>
{
    public void Configure(EntityTypeBuilder<Template> builder)
    {
        builder.ToTable("Templates", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.ProductId)
            .IsRequired();

        builder.Property(e => e.Version)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.Stage)
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(e => e.TemplateJson)
            .HasColumnType("jsonb")
            .IsRequired();

        builder.Property(e => e.PublishedAt);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false);

        // Unique constraint
        builder.HasIndex(e => new { e.ProductId, e.Version, e.Stage })
            .IsUnique()
            .HasDatabaseName("IX_Templates_ProductId_Version_Stage");

        // Indexes
        builder.HasIndex(e => e.ProductId)
            .HasDatabaseName("IX_Templates_ProductId");

        builder.HasIndex(e => e.Stage)
            .HasDatabaseName("IX_Templates_Stage");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Templates_IsActive");

        builder.HasIndex(e => e.IsDeleted)
            .HasDatabaseName("IX_Templates_IsDeleted");

        // Note: No relationship with Product table - ProductId is just a reference field
    }
}
