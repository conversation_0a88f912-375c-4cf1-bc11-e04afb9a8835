using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// ObjectLookupConfiguration entity for configuring dynamic object-based lookups
/// Represents configurations for lookups that pull data from other entities
/// </summary>
public class ObjectLookup : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Name of the lookup configuration
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Source type for the lookup (e.g., "Product", "Object")
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// Optional Object ID if source type is Object-based
    /// </summary>
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Field to use for display text (default: "Name")
    /// </summary>
    public string DisplayField { get; set; } = "Name";

    /// <summary>
    /// Field to use for the value (default: "Id")
    /// </summary>
    public string ValueField { get; set; } = "Id";

    /// <summary>
    /// Metadata field to use for display (for metadata-based lookups)
    /// </summary>
    public string? MetadataFieldForDisplay { get; set; }

    /// <summary>
    /// Metadata field to use for value (for metadata-based lookups)
    /// </summary>
    public string? MetadataFieldForValue { get; set; }

    /// <summary>
    /// Whether this lookup supports tenant filtering
    /// </summary>
    public bool SupportsTenantFiltering { get; set; } = true;

    /// <summary>
    /// Field to sort by (default: "Name")
    /// </summary>
    public string SortBy { get; set; } = "Name";

    /// <summary>
    /// Sort order (default: "ASC")
    /// </summary>
    public string SortOrder { get; set; } = "ASC";

    /// <summary>
    /// Navigation property to Object (if ObjectId is set)
    /// </summary>
    public virtual Object? Object { get; set; }

    /// <summary>
    /// Navigation property for DataTypes that use this configuration
    /// </summary>
    public virtual ICollection<DataType> DataTypes { get; set; } = new List<DataType>();

    /// <summary>
    /// Navigation property for Metadata that override with this configuration
    /// </summary>
    public virtual ICollection<Metadata> MetadataOverrides { get; set; } = new List<Metadata>();
}
