using Abstraction.Database.Repositories;
using Application.Common.DataType;
using Application.MetadataManagement.Specifications;
using Application.Objects.DTOs;
using Application.Products.Specifications;
using Domain.Entities;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace Application.Objects.Services;

/// <summary>
/// Service for creating hierarchical objects with metadata
/// </summary>
public interface IHierarchicalObjectCreationService
{
    Task<HierarchicalObjectCreationResult> CreateHierarchicalObjectsAsync(
        Guid productId,
        List<HierarchicalObjectDto> objects,
        CancellationToken cancellationToken = default);
        
    Task<HierarchicalObjectValidationResult> ValidateHierarchicalObjectsAsync(
        Guid productId,
        List<HierarchicalObjectDto> objects,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of hierarchical object creation service
/// </summary>
public class HierarchicalObjectCreationService : IHierarchicalObjectCreationService
{
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly IRepository<Product> _productRepository;
    private readonly IRepository<Metadata> _metadataRepository;
    private readonly IRepository<DataType> _dataTypeRepository;
    private readonly IRepository<ObjectMetadata> _objectMetadataRepository;
    private readonly IRepository<ObjectValue> _objectValueRepository;
    private readonly ILogger<HierarchicalObjectCreationService> _logger;

    public HierarchicalObjectCreationService(
        IRepository<Domain.Entities.Object> objectRepository,
        IRepository<Product> productRepository,
        IRepository<Metadata> metadataRepository,
        IRepository<DataType> dataTypeRepository,
        IRepository<ObjectMetadata> objectMetadataRepository,
        IRepository<ObjectValue> objectValueRepository,
        ILogger<HierarchicalObjectCreationService> logger)
    {
        _objectRepository = objectRepository;
        _productRepository = productRepository;
        _metadataRepository = metadataRepository;
        _dataTypeRepository = dataTypeRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _objectValueRepository = objectValueRepository;
        _logger = logger;
    }

    public async Task<HierarchicalObjectCreationResult> CreateHierarchicalObjectsAsync(
        Guid productId,
        List<HierarchicalObjectDto> objects,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new HierarchicalObjectCreationResult();

        try
        {
            _logger.LogInformation("Starting hierarchical object creation for ProductId: {ProductId}", productId);

            // Phase 1: Validation
            var validationResult = await ValidateHierarchicalObjectsAsync(productId, objects, cancellationToken);
            if (!validationResult.IsValid)
            {
                result.Success = false;
                result.Errors.AddRange(validationResult.Errors);
                result.Message = "Validation failed";
                return result;
            }

            // Phase 2: Process hierarchical structure
            var context = new ProcessingContext
            {
                ProductId = productId,
                AllMetadata = new Dictionary<string, Metadata>(),
                AllDataTypes = await GetAllDataTypesAsync(cancellationToken),
                CreatedObjects = new List<Domain.Entities.Object>(),
                CreatedMetadata = new List<Metadata>(),
                CreatedObjectMetadata = new List<ObjectMetadata>(),
                CreatedObjectValues = new List<ObjectValue>(),
                DatabaseQueriesCount = 0
            };

            // Process all objects in the hierarchy
            foreach (var objectDto in objects)
            {
                await ProcessHierarchicalObjectAsync(objectDto, null, 0, context, cancellationToken);
            }

            // Phase 3: Batch database operations
            await ExecuteBatchOperationsAsync(context, cancellationToken);

            // Build success response
            BuildSuccessResponse(result, context);

            stopwatch.Stop();
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Hierarchical object creation completed successfully. " +
                "Objects: {ObjectCount}, Metadata: {MetadataCount}, Values: {ValueCount}, Time: {TimeMs}ms",
                result.TotalObjectsCreated, result.TotalMetadataCreated, 
                result.TotalObjectValuesCreated, result.ProcessingTimeMs);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during hierarchical object creation for ProductId: {ProductId}", productId);
            
            result.Success = false;
            result.Errors.Add($"Internal error: {ex.Message}");
            result.Message = "Processing failed due to internal error";
            
            stopwatch.Stop();
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            
            return result;
        }
    }

    public async Task<HierarchicalObjectValidationResult> ValidateHierarchicalObjectsAsync(
        Guid productId,
        List<HierarchicalObjectDto> objects,
        CancellationToken cancellationToken = default)
    {
        var result = new HierarchicalObjectValidationResult();
        var errors = new List<string>();

        try
        {
            // Validate product exists
            var product = await _productRepository.GetByIdAsync(productId, cancellationToken);
            if (product == null)
            {
                errors.Add($"Product with ID '{productId}' not found");
            }

            // Validate objects structure
            if (!objects.Any())
            {
                errors.Add("At least one object must be provided");
            }

            // Validate object names are unique at each level
            ValidateObjectNamesUnique(objects, "", errors);

            // Validate object structure
            foreach (var obj in objects)
            {
                ValidateObjectStructure(obj, 0, errors);
            }

            result.IsValid = !errors.Any();
            result.Errors = errors;
            result.TotalObjects = CountTotalObjects(objects);
            result.MaxDepth = CalculateMaxDepth(objects);
            result.EstimatedMetadataFields = EstimateMetadataFields(objects);
            result.EstimatedValues = EstimateValues(objects);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during validation for ProductId: {ProductId}", productId);
            result.IsValid = false;
            result.Errors.Add($"Validation error: {ex.Message}");
            return result;
        }
    }

    private async Task<Metadata?> GetOrCreateMetadataAsync(
        string propertyName,
        object? propertyValue,
        ProcessingContext context,
        CancellationToken cancellationToken)
    {
        // Check if metadata already exists in context
        if (context.AllMetadata.TryGetValue(propertyName.ToLowerInvariant(), out var existingMetadata))
        {
            return existingMetadata;
        }

        // Check if metadata exists in database
        var metadataSpec = new MetadataByKeySpec(propertyName);
        var dbMetadata = await _metadataRepository.FirstOrDefaultAsync(metadataSpec, cancellationToken);
        context.DatabaseQueriesCount++;

        if (dbMetadata != null)
        {
            context.AllMetadata[propertyName.ToLowerInvariant()] = dbMetadata;
            return dbMetadata;
        }

        // Create new metadata
        var dataTypeName = DetermineDataTypeName(propertyValue, propertyName);
        if (!context.AllDataTypes.TryGetValue(dataTypeName.ToLowerInvariant(), out var dataType))
        {
            _logger.LogWarning("DataType '{DataTypeName}' not found for property '{PropertyName}', using 'text' as fallback", dataTypeName, propertyName);
            dataType = context.AllDataTypes.GetValueOrDefault("text") ?? context.AllDataTypes.Values.First();
        }

        var newMetadata = new Metadata
        {
            Id = Guid.NewGuid(),
            Name = propertyName,
            DataTypeId = dataType.Id,
            DisplayLabel = propertyName,
            IsVisible = true,
            IsReadonly = false,
            CreatedAt = DateTime.UtcNow,
            ModifiedAt = DateTime.UtcNow
        };

        context.CreatedMetadata.Add(newMetadata);
        context.AllMetadata[propertyName.ToLowerInvariant()] = newMetadata;

        return newMetadata;
    }

    private async Task ExecuteBatchOperationsAsync(ProcessingContext context, CancellationToken cancellationToken)
    {
        // Add metadata first (dependencies)
        if (context.CreatedMetadata.Any())
        {
            await _metadataRepository.AddRangeAsync(context.CreatedMetadata, cancellationToken);
            context.DatabaseQueriesCount++;
        }

        // Add objects
        if (context.CreatedObjects.Any())
        {
            await _objectRepository.AddRangeAsync(context.CreatedObjects, cancellationToken);
            context.DatabaseQueriesCount++;
        }

        // Add object-metadata links
        if (context.CreatedObjectMetadata.Any())
        {
            await _objectMetadataRepository.AddRangeAsync(context.CreatedObjectMetadata, cancellationToken);
            context.DatabaseQueriesCount++;
        }

        // Add object values
        if (context.CreatedObjectValues.Any())
        {
            await _objectValueRepository.AddRangeAsync(context.CreatedObjectValues, cancellationToken);
            context.DatabaseQueriesCount++;
        }
    }

    private void BuildSuccessResponse(HierarchicalObjectCreationResult result, ProcessingContext context)
    {
        result.Success = true;
        result.Message = "Hierarchical objects created successfully";

        result.TotalObjectsCreated = context.CreatedObjects.Count;
        result.TotalMetadataCreated = context.CreatedMetadata.Count;
        result.TotalObjectMetadataCreated = context.CreatedObjectMetadata.Count;
        result.TotalObjectValuesCreated = context.CreatedObjectValues.Count;

        // Build hierarchical response structure
        var rootObjects = context.CreatedObjects.Where(o => o.ParentObjectId == null).ToList();
        foreach (var rootObject in rootObjects)
        {
            var createdObjectInfo = BuildCreatedObjectInfo(rootObject, context, 0);
            result.CreatedObjects.Add(createdObjectInfo);
        }
    }

    private CreatedObjectInfo BuildCreatedObjectInfo(Domain.Entities.Object obj, ProcessingContext context, int level)
    {
        var metadataCount = context.CreatedObjectMetadata.Count(om => om.ObjectId == obj.Id);
        var valuesCount = context.CreatedObjectValues.Count(ov =>
            context.CreatedObjectMetadata.Any(om => om.Id == ov.ObjectMetadataId && om.ObjectId == obj.Id));

        var info = new CreatedObjectInfo
        {
            ObjectId = obj.Id,
            Name = obj.Name,
            ParentObjectId = obj.ParentObjectId,
            Level = level,
            MetadataFieldsCount = metadataCount,
            InstanceValuesCount = valuesCount
        };

        // Add child objects
        var children = context.CreatedObjects.Where(o => o.ParentObjectId == obj.Id).ToList();
        foreach (var child in children)
        {
            var childInfo = BuildCreatedObjectInfo(child, context, level + 1);
            info.Children.Add(childInfo);
        }

        return info;
    }

    private static string DetermineDataTypeName(object? value, string fieldName)
    {
        if (value == null)
            return "text";

        var valueStr = value.ToString();
        if (string.IsNullOrEmpty(valueStr))
            return "text";

        var fieldNameLower = fieldName.ToLowerInvariant();

        // Priority 1: Check centralized synonym mappings first (highest priority)
        var synonymDetectedType = DataTypeSynonymMappings.DetectDataTypeFromFieldName(fieldNameLower);
        if (!string.IsNullOrEmpty(synonymDetectedType))
            return synonymDetectedType;

        // Priority 2: Check if the string value looks like JSON
        if (valueStr.StartsWith("{") && valueStr.EndsWith("}") ||
            valueStr.StartsWith("[") && valueStr.EndsWith("]"))
        {
            return "json"; // JSON strings should use json datatype
        }

        // Priority 3: GUID patterns
        if (fieldNameLower.Contains("guid") || fieldNameLower.Contains("uuid") || Guid.TryParse(valueStr, out _))
            return "guid";

        // Priority 4: Default fallback
        return "text";
    }

    private void ValidateObjectNamesUnique(List<HierarchicalObjectDto> objects, string parentPath, List<string> errors)
    {
        var names = new HashSet<string>();

        foreach (var obj in objects)
        {
            var currentPath = string.IsNullOrEmpty(parentPath) ? obj.Name : $"{parentPath}/{obj.Name}";

            if (!names.Add(obj.Name.ToLowerInvariant()))
            {
                errors.Add($"Duplicate object name '{obj.Name}' found at path '{parentPath}'");
            }

            if (obj.ChildObjects?.Any() == true)
            {
                ValidateObjectNamesUnique(obj.ChildObjects, currentPath, errors);
            }
        }
    }

    private void ValidateObjectStructure(HierarchicalObjectDto obj, int depth, List<string> errors)
    {
        if (depth > 10) // Prevent excessive nesting
        {
            errors.Add($"Maximum hierarchy depth (10) exceeded for object '{obj.Name}'");
            return;
        }

        if (string.IsNullOrWhiteSpace(obj.Name))
        {
            errors.Add($"Object name is required at depth {depth}");
        }

        // Validate metaValues have instanceId if provided
        if (obj.MetaValues?.Any() == true)
        {
            foreach (var metaValue in obj.MetaValues)
            {
                if (!metaValue.ContainsKey("instanceId") || metaValue["instanceId"] == null)
                {
                    errors.Add($"Object '{obj.Name}': All metaValues must contain an 'instanceId' field");
                }
            }
        }

        // Recursively validate child objects
        if (obj.ChildObjects?.Any() == true)
        {
            foreach (var child in obj.ChildObjects)
            {
                ValidateObjectStructure(child, depth + 1, errors);
            }
        }
    }

    private int CountTotalObjects(List<HierarchicalObjectDto> objects)
    {
        if (objects == null) return 0;

        int count = objects.Count;
        foreach (var obj in objects)
        {
            count += CountTotalObjects(obj.ChildObjects ?? new List<HierarchicalObjectDto>());
        }
        return count;
    }

    private int CalculateMaxDepth(List<HierarchicalObjectDto> objects, int currentDepth = 0)
    {
        if (objects == null || !objects.Any()) return currentDepth;

        int maxDepth = currentDepth;
        foreach (var obj in objects)
        {
            int childDepth = CalculateMaxDepth(obj.ChildObjects ?? new List<HierarchicalObjectDto>(), currentDepth + 1);
            maxDepth = Math.Max(maxDepth, childDepth);
        }
        return maxDepth;
    }

    private int EstimateMetadataFields(List<HierarchicalObjectDto> objects)
    {
        if (objects == null) return 0;

        var uniqueFields = new HashSet<string>();
        CollectMetadataFields(objects, uniqueFields);
        return uniqueFields.Count;
    }

    private void CollectMetadataFields(List<HierarchicalObjectDto> objects, HashSet<string> fields)
    {
        if (objects == null) return;

        foreach (var obj in objects)
        {
            if (obj.MetaJson != null)
            {
                foreach (var key in obj.MetaJson.Keys)
                {
                    fields.Add(key);
                }
            }

            if (obj.MetaValues != null)
            {
                foreach (var metaValue in obj.MetaValues)
                {
                    foreach (var key in metaValue.Keys)
                    {
                        if (key != "instanceId")
                        {
                            fields.Add(key);
                        }
                    }
                }
            }

            CollectMetadataFields(obj.ChildObjects ?? new List<HierarchicalObjectDto>(), fields);
        }
    }

    private int EstimateValues(List<HierarchicalObjectDto> objects)
    {
        if (objects == null) return 0;

        int count = 0;
        foreach (var obj in objects)
        {
            if (obj.MetaValues != null)
            {
                foreach (var metaValue in obj.MetaValues)
                {
                    count += metaValue.Count - 1; // Subtract 1 for instanceId
                }
            }

            count += EstimateValues(obj.ChildObjects ?? new List<HierarchicalObjectDto>());
        }
        return count;
    }

    private async Task<Dictionary<string, DataType>> GetAllDataTypesAsync(CancellationToken cancellationToken)
    {
        var dataTypes = await _dataTypeRepository.ListAsync(cancellationToken);
        return dataTypes.ToDictionary(dt => dt.Name.ToLowerInvariant(), dt => dt);
    }

    private async Task ProcessHierarchicalObjectAsync(
        HierarchicalObjectDto objectDto,
        Guid? parentObjectId,
        int level,
        ProcessingContext context,
        CancellationToken cancellationToken)
    {
        // Create the object entity
        var objectEntity = new Domain.Entities.Object
        {
            Id = Guid.NewGuid(),
            ProductId = context.ProductId,
            ParentObjectId = parentObjectId,
            Name = objectDto.Name,
            Description = objectDto.Description,
            IsActive = objectDto.IsActive,
            CreatedAt = DateTime.UtcNow,
            ModifiedAt = DateTime.UtcNow
        };

        context.CreatedObjects.Add(objectEntity);

        // Process metadata schema (metaJson)
        if (objectDto.MetaJson?.Any() == true)
        {
            foreach (var metaProperty in objectDto.MetaJson)
            {
                var metadata = await GetOrCreateMetadataAsync(metaProperty.Key, metaProperty.Value, context, cancellationToken);
                if (metadata != null)
                {
                    // Create ObjectMetadata link
                    var objectMetadata = new ObjectMetadata
                    {
                        Id = Guid.NewGuid(),
                        ObjectId = objectEntity.Id,
                        MetadataId = metadata.Id,
                        IsUnique = false,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        ModifiedAt = DateTime.UtcNow
                    };

                    context.CreatedObjectMetadata.Add(objectMetadata);
                }
            }
        }

        // Process instance values (metaValues)
        if (objectDto.MetaValues?.Any() == true)
        {
            foreach (var metaValue in objectDto.MetaValues)
            {
                var refId = Guid.NewGuid(); // Each instance gets its own RefId

                foreach (var valueProperty in metaValue)
                {
                    if (valueProperty.Key == "instanceId") continue; // Skip instanceId

                    // Find or create metadata for this property
                    var metadata = await GetOrCreateMetadataAsync(valueProperty.Key, valueProperty.Value, context, cancellationToken);
                    if (metadata != null)
                    {
                        // Ensure ObjectMetadata link exists
                        if (!context.CreatedObjectMetadata.Any(om => om.ObjectId == objectEntity.Id && om.MetadataId == metadata.Id))
                        {
                            var objectMetadata = new ObjectMetadata
                            {
                                Id = Guid.NewGuid(),
                                ObjectId = objectEntity.Id,
                                MetadataId = metadata.Id,
                                IsUnique = false,
                                IsActive = true,
                                CreatedAt = DateTime.UtcNow,
                                ModifiedAt = DateTime.UtcNow
                            };

                            context.CreatedObjectMetadata.Add(objectMetadata);
                        }

                        // Create ObjectValue
                        var objectValue = new ObjectValue
                        {
                            Id = Guid.NewGuid(),
                            ObjectMetadataId = context.CreatedObjectMetadata.First(om => om.ObjectId == objectEntity.Id && om.MetadataId == metadata.Id).Id,
                            RefId = refId,
                            Value = valueProperty.Value?.ToString(),
                            CreatedAt = DateTime.UtcNow,
                            ModifiedAt = DateTime.UtcNow
                        };

                        context.CreatedObjectValues.Add(objectValue);
                    }
                }
            }
        }

        // Process child objects recursively
        if (objectDto.ChildObjects?.Any() == true)
        {
            foreach (var childObject in objectDto.ChildObjects)
            {
                await ProcessHierarchicalObjectAsync(childObject, objectEntity.Id, level + 1, context, cancellationToken);
            }
        }
    }
}
