# Custom Flutter Input Widgets

This directory contains custom Flutter input widgets following the `this_componentName_input` naming convention for data entry components.

## Naming Convention

All widgets follow the pattern: `this_componentName_input`
- `this` - Fixed prefix
- `componentName` - The component type (Text, Email, Phone, Currency, etc.)
- `input` - All widgets are for data entry

## Available Input Widgets

### Input Widgets (Data Entry)

**Total: 18 Input Widgets** - All components support API-based configuration and validation

#### 1. ThisTextInput
- **File**: `this_text_input.dart`
- **Purpose**: Single-line text input with comprehensive validation
- **Features**: Character count, length limits, pattern validation, clear button
- **Usage**:
```dart
ThisTextInput(
  id: 'user_name',
  label: 'Full Name',
  value: textValue,
  onChanged: (value) => setState(() => textValue = value),
  required: true,
  maxLength: 100,
  showCharacterCount: true,
)
```

#### 2. ThisTextareaInput
- **File**: `this_textarea_input.dart`
- **Purpose**: Multi-line text input with validation
- **Features**: Auto-resize, character count, line limits, word count
- **Usage**:
```dart
ThisTextareaInput(
  id: 'description',
  label: 'Description',
  value: textareaValue,
  onChanged: (value) => setState(() => textareaValue = value),
  minLines: 3,
  maxLines: 6,
  maxLength: 500,
)
```

#### 3. ThisEmailInput
- **File**: `this_email_input.dart`
- **Purpose**: Email input with comprehensive validation
- **Features**: Domain validation, international support, plus addressing, real-time validation
- **Usage**:
```dart
ThisEmailInput(
  id: 'user_email',
  label: 'Email Address',
  value: emailValue,
  onChanged: (value) => setState(() => emailValue = value),
  required: true,
  allowedDomains: ['gmail.com', 'company.com'],
  showValidationIcon: true,
)
```

#### 4. ThisPhoneInput
- **File**: `this_phone_input.dart`
- **Purpose**: Phone number input with country selection
- **Features**: Country flags, formatting, extension support, international validation
- **Usage**:
```dart
ThisPhoneInput(
  id: 'user_phone',
  label: 'Phone Number',
  value: phoneValue,
  onChanged: (value) => setState(() => phoneValue = value),
  required: true,
  defaultCountry: 'US',
  showFlag: true,
  allowExtensions: true,
)
```

#### 5. ThisNumberInput
- **File**: `this_number_input.dart`
- **Purpose**: Numeric input with formatting and validation
- **Features**: Currency support, decimals, thousands separators, range validation
- **Usage**:
```dart
ThisNumberInput(
  id: 'amount',
  label: 'Amount',
  value: numberValue,
  onChanged: (value) => setState(() => numberValue = value),
  required: true,
  min: 0,
  max: 10000,
  decimals: 2,
  thousandsSeparator: true,
)
```

#### 6. ThisCurrencyInput
- **File**: `this_currency_input.dart`
- **Purpose**: Currency input with multi-currency support
- **Features**: Currency selector, formatting, exchange rates, validation
- **API Parameters**: `validationPattern`, `minValue`, `maxValue`, `decimalPlaces`, `stepValue`
- **Usage**:
```dart
ThisCurrencyInput(
  id: 'price',
  label: 'Price',
  value: currencyValue,
  onChanged: (value) => setState(() => currencyValue = value),
  required: true,
  minValue: 0,
  maxValue: 10000,
  decimalPlaces: 2,
  showCurrencySelector: true,
)
```

#### 7. ThisPercentageInput
- **File**: `this_percentage_input.dart`
- **Purpose**: Percentage input with validation
- **Features**: Percentage symbol, decimal support, range validation
- **API Parameters**: `validationPattern`, `minValue`, `maxValue`, `decimalPlaces`, `stepValue`
- **Usage**:
```dart
ThisPercentageInput(
  id: 'completion',
  label: 'Completion Rate',
  value: percentageValue,
  onChanged: (value) => setState(() => percentageValue = value),
  required: true,
  minValue: 0,
  maxValue: 100,
  showPercentageSymbol: true,
)
```

#### 8. ThisDropdownInput
- **File**: `this_dropdown_input.dart`
- **Purpose**: Dropdown selection with single/multiple options
- **Features**: Search, custom options, multi-select, validation
- **API Parameters**: `allowsMultiple`, `allowsCustomOptions`, `maxSelections`
- **Usage**:
```dart
ThisDropdownInput(
  id: 'category',
  label: 'Category',
  options: dropdownOptions,
  value: dropdownValue,
  onChanged: (value) => setState(() => dropdownValue = value),
  required: true,
  allowsMultiple: false,
  searchable: true,
)
```

#### 9. ThisSliderInput
- **File**: `this_slider_input.dart`
- **Purpose**: Range slider input with validation
- **Features**: Step values, min/max labels, ticks, custom formatting
- **API Parameters**: `minValue`, `maxValue`, `stepValue`, `decimalPlaces`
- **Usage**:
```dart
ThisSliderInput(
  id: 'rating',
  label: 'Rating',
  value: sliderValue,
  onChanged: (value) => setState(() => sliderValue = value),
  required: true,
  minValue: 0,
  maxValue: 100,
  stepValue: 5,
  showTicks: true,
)
```

#### 10. ThisFileInput
- **File**: `this_file_input.dart`
- **Purpose**: File upload with validation
- **Features**: Multiple files, type filtering, size limits, progress
- **API Parameters**: `allowsMultiple`, `allowedFileTypes`, `maxFileSizeBytes`, `maxSelections`
- **Usage**:
```dart
ThisFileInput(
  id: 'documents',
  label: 'Upload Documents',
  value: fileValue,
  onChanged: (value) => setState(() => fileValue = value),
  allowsMultiple: true,
  allowedFileTypes: ['pdf', 'doc', 'docx'],
  maxFileSizeBytes: 10485760, // 10MB
)
```

#### 11. ThisImageInput
- **File**: `this_image_input.dart`
- **Purpose**: Image upload with preview and validation
- **Features**: Camera/gallery, preview grid, dimension validation, compression
- **API Parameters**: `allowsMultiple`, `allowedFileTypes`, `maxFileSizeBytes`, `maxWidth`, `maxHeight`
- **Usage**:
```dart
ThisImageInput(
  id: 'photos',
  label: 'Upload Photos',
  value: imageValue,
  onChanged: (value) => setState(() => imageValue = value),
  allowsMultiple: true,
  maxFileSizeBytes: 5242880, // 5MB
  maxWidth: 1920,
  maxHeight: 1080,
  showPreview: true,
)
```

#### 12. ThisVideoInput
- **File**: `this_video_input.dart`
- **Purpose**: Video upload with validation
- **Features**: Camera/gallery, duration limits, format validation, preview
- **API Parameters**: `allowsMultiple`, `allowedFileTypes`, `maxFileSizeBytes`, `maxDuration`, `minDuration`
- **Usage**:
```dart
ThisVideoInput(
  id: 'video',
  label: 'Upload Video',
  value: videoValue,
  onChanged: (value) => setState(() => videoValue = value),
  allowsMultiple: false,
  allowedFileTypes: ['mp4', 'mov'],
  maxDuration: Duration(minutes: 5),
  maxFileSizeBytes: 104857600, // 100MB
)
```

#### 13. ThisCheckboxInput
- **File**: `this_checkbox_input.dart`
- **Purpose**: Multiple checkbox selection with validation
- **Features**: Select all, minimum/maximum selections, custom options
- **Usage**:
```dart
ThisCheckboxInput(
  id: 'preferences',
  label: 'Preferences',
  options: checkboxOptions,
  value: checkboxValues,
  onChanged: (values) => setState(() => checkboxValues = values),
  allowSelectAll: true,
  minSelected: 1,
)
```

#### 14. ThisRadioInput
- **File**: `this_radio_input.dart`
- **Purpose**: Single selection from multiple options
- **Features**: Descriptions, icons, validation
- **Usage**:
```dart
ThisRadioInput(
  id: 'gender',
  label: 'Gender',
  options: radioOptions,
  value: radioValue,
  onChanged: (value) => setState(() => radioValue = value),
  required: true,
)
```

#### 15. ThisYearInput
- **File**: `this_year_input.dart`
- **Purpose**: Year selection with validation
- **Features**: Dropdown/picker, year ranges, relative info
- **Usage**:
```dart
ThisYearInput(
  id: 'birth_year',
  label: 'Birth Year',
  value: yearValue,
  onChanged: (value) => setState(() => yearValue = value),
  minYear: 1950,
  maxYear: 2024,
  showDropdown: true,
)
```

#### 16. ThisMonthInput
- **File**: `this_month_input.dart`
- **Purpose**: Month selection with validation
- **Features**: Full/short/numeric display, month info
- **Usage**:
```dart
ThisMonthInput(
  id: 'birth_month',
  label: 'Birth Month',
  value: monthValue,
  onChanged: (value) => setState(() => monthValue = value),
  displayFormat: MonthDisplayFormat.full,
  required: true,
)
```

#### 17. ThisDayInput
- **File**: `this_day_input.dart`
- **Purpose**: Day selection with validation
- **Features**: Month/year awareness, ordinal format, day ranges
- **Usage**:
```dart
ThisDayInput(
  id: 'birth_day',
  label: 'Birth Day',
  value: dayValue,
  onChanged: (value) => setState(() => dayValue = value),
  month: monthValue,
  year: yearValue,
  showDropdown: true,
)
```

#### 18. ThisTimeInput
- **File**: `this_time_input.dart`
- **Purpose**: Time selection with validation
- **Features**: 12/24 hour format, time picker, relative time
- **Usage**:
```dart
ThisTimeInput(
  id: 'meeting_time',
  label: 'Meeting Time',
  value: timeValue,
  onChanged: (value) => setState(() => timeValue = value),
  use24HourFormat: false,
  showTimePicker: true,
  required: true,
)
```

## API Integration

All widgets support API-based configuration through these common parameters:

### Validation Parameters
- `validationPattern` - Regex pattern for validation
- `minLength` / `maxLength` - String length limits
- `minValue` / `maxValue` - Numeric range limits
- `decimalPlaces` - Decimal precision
- `stepValue` - Increment/decrement step
- `inputMask` - Input formatting mask

### Error Messages
- `requiredErrorMessage` - Custom required field message
- `patternErrorMessage` - Custom pattern validation message
- `minLengthErrorMessage` - Custom min length message
- `maxLengthErrorMessage` - Custom max length message
- `minValueErrorMessage` - Custom min value message
- `maxValueErrorMessage` - Custom max value message
- `fileTypeErrorMessage` - Custom file type message
- `fileSizeErrorMessage` - Custom file size message

### File/Media Parameters
- `allowsMultiple` - Allow multiple file selection
- `allowedFileTypes` - Allowed file extensions
- `maxFileSizeBytes` - Maximum file size in bytes
- `maxSelections` - Maximum number of selections

## Common Features

### All Input Widgets Support:
- Required field validation
- Custom validation functions
- Disabled and read-only states
- Help text with tooltips
- Error message display
- Real-time validation
- Consistent theming
- Parameter-driven configuration
- Accessibility support
- Auto-focus capabilities
- API-based configuration
- Dynamic validation rules
- Custom error messages
- File handling (where applicable)
- Multi-selection support (where applicable)

## Usage Examples

See `main_api_components.dart` for comprehensive examples of all widgets with API-based configuration.

## Dependencies

- `file_picker: ^8.1.2` - For file selection
- `image_picker: ^1.1.2` - For image/video selection
- `intl: ^0.19.0` - For internationalization and formatting
