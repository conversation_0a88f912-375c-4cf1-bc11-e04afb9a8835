import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/app_theme.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widgets.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Widget Debug App',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.darkThemeMode,
      home: const DebugTestPage(),
    );
  }
}

class DebugTestPage extends StatefulWidget {
  const DebugTestPage({super.key});

  @override
  State<DebugTestPage> createState() => _DebugTestPageState();
}

class _DebugTestPageState extends State<DebugTestPage> {
  // Simple state variables for testing
  String _textValue = '';
  String _emailValue = '';
  String _currencyValue = '';
  String? _dropdownValue;

  // Sample dropdown options
  final List<DropdownOption> _dropdownOptions = [
    const DropdownOption(value: 'usa', label: 'United States'),
    const DropdownOption(value: 'canada', label: 'Canada'),
    const DropdownOption(value: 'uk', label: 'United Kingdom'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorPalette.primaryDarkColor,
        title: Text(
          'Debug Test - Basic Widgets',
          style: LexendTextStyles.lexend16ExtraLight.copyWith(
            color: ColorPalette.white,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Testing Basic Widgets',
              style: LexendTextStyles.lexend18Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 24),

            // Text Input Test
            _buildSection(
              'Text Input',
              [
                ThisTextInput(
                  id: 'text_test',
                  label: 'Name',
                  value: _textValue,
                  onChanged: (value) => setState(() => _textValue = value),
                  placeholder: 'Enter your name...',
                  required: true,
                  helpText: 'Enter your full name',
                ),
              ],
            ),

            // Email Input Test
            _buildSection(
              'Email Input',
              [
                ThisEmailInput(
                  id: 'email_test',
                  label: 'Email',
                  value: _emailValue,
                  onChanged: (value) => setState(() => _emailValue = value),
                  placeholder: 'Enter your email...',
                  required: true,
                  helpText: 'Enter a valid email address',
                ),
              ],
            ),

            // Currency Input Test
            _buildSection(
              'Currency Input',
              [
                ThisCurrencyInput(
                  id: 'currency_test',
                  label: 'Amount',
                  value: _currencyValue,
                  onChanged: (value) => setState(() => _currencyValue = value),
                  placeholder: 'Enter amount...',
                  required: true,
                  minValue: 0,
                  maxValue: 1000,
                  decimalPlaces: 2,
                  helpText: 'Enter a monetary amount',
                ),
              ],
            ),

            // Dropdown Input Test
            _buildSection(
              'Dropdown Input',
              [
                ThisDropdownInput(
                  id: 'dropdown_test',
                  label: 'Country',
                  options: _dropdownOptions,
                  value: _dropdownValue,
                  onChanged: (value) => setState(() => _dropdownValue = value),
                  placeholder: 'Select country...',
                  required: true,
                  helpText: 'Select your country',
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Test Summary
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.green),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ Basic Widgets Test',
                    style: LexendTextStyles.lexend14Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Testing: Text, Email, Currency, Dropdown inputs\nIf this works, we can add more widgets.',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: LexendTextStyles.lexend16Bold.copyWith(
              color: ColorPalette.green,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: ColorPalette.gray700),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
