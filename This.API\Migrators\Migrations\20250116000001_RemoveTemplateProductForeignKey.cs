using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Migrators.Migrations
{
    /// <inheritdoc />
    public partial class RemoveTemplateProductForeignKey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop the foreign key constraint
            migrationBuilder.DropForeignKey(
                name: "FK_Templates_Products_ProductId",
                schema: "Genp",
                table: "Templates");

            // Keep the ProductId column but remove the foreign key relationship
            // The ProductId will remain as a regular Guid column for reference purposes
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Re-add the foreign key constraint if rolling back
            migrationBuilder.AddForeignKey(
                name: "FK_Templates_Products_ProductId",
                schema: "Genp",
                table: "Templates",
                column: "ProductId",
                principalSchema: "Genp",
                principalTable: "Products",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
