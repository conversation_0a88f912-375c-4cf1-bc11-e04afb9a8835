using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Context entity
/// </summary>
public class ContextConfig : IEntityTypeConfiguration<Context>
{
    public void Configure(EntityTypeBuilder<Context> builder)
    {
        builder.ToTable("Contexts", "Genp");

        // Properties
        builder.Property(e => e.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnType("TEXT");

        builder.Property(e => e.Category)
            .HasMaxLength(50);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(e => e.Name)
            .HasDatabaseName("IX_Contexts_Name");

        builder.HasIndex(e => e.Category)
            .HasDatabaseName("IX_Contexts_Category");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Contexts_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Relationships
        builder.HasMany(e => e.Lookups)
            .WithOne(e => e.Context)
            .HasForeignKey(e => e.ContextId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.DataTypes)
            .WithOne(e => e.MasterContext)
            .HasForeignKey(e => e.MasterContextId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(e => e.MetadataOverrides)
            .WithOne(e => e.OverrideMasterContext)
            .HasForeignKey(e => e.OverrideMasterContextId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
