namespace Application.Objects.DTOs;

/// <summary>
/// Object DTO
/// </summary>
public class ObjectDto
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID this object belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string? ProductName { get; set; }

    /// <summary>
    /// Parent object ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Parent object name
    /// </summary>
    public string? ParentObjectName { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Object icon
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Number of child objects
    /// </summary>
    public int ChildObjectsCount { get; set; }

    /// <summary>
    /// Number of metadata definitions
    /// </summary>
    public int MetadataCount { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
