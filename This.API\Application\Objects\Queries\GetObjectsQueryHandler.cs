// COMMENTED OUT - Contains Feature references that have been removed
// TODO: Update to use Product references or remove if not needed

/*
using Application.Objects.DTOs;
using Application.Objects.Specifications;
using Application.ObjectMetadataManagement.DTOs;
using Abstraction.Database.Repositories;
using MediatR;
using Shared.Common.Response;
using ObjectEntity = Domain.Entities.Object;

namespace Application.Objects.Queries;

/// <summary>
/// Get Objects query handler
/// </summary>
public class GetObjectsQueryHandler : IRequestHandler<GetObjectsQuery, PaginatedResult<ObjectDto>>
{
    private readonly IReadRepository<ObjectEntity> _objectRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectsQueryHandler(IReadRepository<ObjectEntity> objectRepository)
    {
        _objectRepository = objectRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<ObjectDto>> Handle(GetObjectsQuery request, CancellationToken cancellationToken)
    {
        var skip = (request.PageNumber - 1) * request.PageSize;

        // Create specifications
        var dataSpec = new ObjectWithMetadataSpec(
            searchTerm: request.SearchTerm,
            featureId: request.FeatureId,
            isActive: request.IsActive,
            orderBy: request.OrderBy,
            skip: skip,
            take: request.PageSize);

        var countSpec = new ObjectCountSpec(
            searchTerm: request.SearchTerm,
            featureId: request.FeatureId,
            isActive: request.IsActive);

        // Get data and count using specifications (tenant isolation handled by Finbuckle.MultiTenant)
        var objects = await _objectRepository.ListAsync(dataSpec, cancellationToken);
        var totalCount = await _objectRepository.CountAsync(countSpec, cancellationToken);

        var objectDtos = objects.Select(obj => new ObjectDto
        {
            Id = obj.Id,
            FeatureId = obj.FeatureId,
            FeatureName = obj.Feature?.Name,
            ParentObjectId = obj.ParentObjectId,
            ParentObjectName = obj.ParentObject?.Name,
            Name = obj.Name,
            Description = obj.Description,
            IsActive = obj.IsActive,
            ChildObjectsCount = obj.ChildObjects?.Count(co => co.IsActive && !co.IsDeleted) ?? 0,
            MetadataCount = obj.ObjectMetadata?.Count(om => om.IsActive && !om.IsDeleted) ?? 0
        }).ToList();

        return new PaginatedResult<ObjectDto>(objectDtos, request.PageNumber, request.PageSize, totalCount);
    }
}
*/
