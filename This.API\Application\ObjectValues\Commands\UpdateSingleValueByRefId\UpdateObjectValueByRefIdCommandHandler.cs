using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Application.ObjectValues.Specifications;

namespace Application.ObjectValues.Commands.UpdateSingleValueByRefId;

/// <summary>
/// Handler for UpdateObjectValueByRefIdCommand
/// </summary>
public class UpdateObjectValueByRefIdCommandHandler : IRequestHandler<UpdateObjectValueByRefIdCommand, Result<UpdateObjectValueByRefIdResponse>>
{
    private readonly IRepository<ObjectValue> _objectValueRepository;
    private readonly ILogger<UpdateObjectValueByRefIdCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateObjectValueByRefIdCommandHandler(
        IRepository<ObjectValue> objectValueRepository,
        ILogger<UpdateObjectValueByRefIdCommandHandler> logger)
    {
        _objectValueRepository = objectValueRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<UpdateObjectValueByRefIdResponse>> Handle(UpdateObjectValueByRefIdCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating ObjectValue with RefId: {RefId}, MetadataKey: {MetadataKey} to Value: {Value}",
                request.RefId, request.MetadataKey, request.Value);

            // Use specification to find the ObjectValue by RefId and Name
            var specification = new ObjectValueByRefIdAndMetadataKeySpecification(request.RefId, request.MetadataKey, request.TenantId);
            var existingObjectValue = await _objectValueRepository.GetBySpecAsync(specification, cancellationToken);

            if (existingObjectValue == null)
            {
                _logger.LogWarning("ObjectValue with RefId {RefId} and MetadataKey {MetadataKey} not found",
                    request.RefId, request.MetadataKey);

                var notFoundResponse = new UpdateObjectValueByRefIdResponse
                {
                    RefId = request.RefId,
                    MetadataKey = request.MetadataKey,
                    Value = request.Value,
                    Found = false,
                    Message = $"ObjectValue with RefId '{request.RefId}' and MetadataKey '{request.MetadataKey}' not found"
                };

                return Result<UpdateObjectValueByRefIdResponse>.Success(notFoundResponse);
            }

            // Update only the Value field
            existingObjectValue.Value = request.Value;
            existingObjectValue.ModifiedAt = DateTime.UtcNow;

            // Save changes using repository
            await _objectValueRepository.UpdateAsync(existingObjectValue, cancellationToken);

            _logger.LogInformation("Successfully updated ObjectValue with RefId: {RefId}, MetadataKey: {MetadataKey}",
                request.RefId, request.MetadataKey);

            var response = new UpdateObjectValueByRefIdResponse
            {
                Id = existingObjectValue.Id,
                RefId = request.RefId,
                MetadataKey = request.MetadataKey,
                Value = existingObjectValue.Value,
                ModifiedAt = existingObjectValue.ModifiedAt,
                Found = true,
                Message = $"ObjectValue updated successfully for RefId '{request.RefId}' and MetadataKey '{request.MetadataKey}'"
            };

            return Result<UpdateObjectValueByRefIdResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ObjectValue with RefId: {RefId}, MetadataKey: {MetadataKey}",
                request.RefId, request.MetadataKey);
            return Result<UpdateObjectValueByRefIdResponse>.Failure($"Error updating ObjectValue: {ex.Message}");
        }
    }
}
