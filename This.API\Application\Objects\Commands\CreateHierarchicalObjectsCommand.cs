using Application.Objects.DTOs;
using MediatR;
using Shared.Common.Response;
using System.ComponentModel.DataAnnotations;

namespace Application.Objects.Commands;

/// <summary>
/// Command for creating hierarchical objects with metadata
/// </summary>
public class CreateHierarchicalObjectsCommand : IRequest<Result<HierarchicalObjectCreationResult>>
{
    /// <summary>
    /// Product ID to associate objects with
    /// </summary>
    [Required]
    public Guid ProductId { get; set; }

    /// <summary>
    /// List of objects to create with their hierarchical structure
    /// </summary>
    [Required]
    public List<HierarchicalObjectDto> Objects { get; set; } = new();
}

/// <summary>
/// Query for validating hierarchical objects structure
/// </summary>
public class ValidateHierarchicalObjectsQuery : IRequest<Result<HierarchicalObjectValidationResult>>
{
    /// <summary>
    /// Product ID to associate objects with
    /// </summary>
    [Required]
    public Guid ProductId { get; set; }

    /// <summary>
    /// List of objects to validate
    /// </summary>
    [Required]
    public List<HierarchicalObjectDto> Objects { get; set; } = new();
}
