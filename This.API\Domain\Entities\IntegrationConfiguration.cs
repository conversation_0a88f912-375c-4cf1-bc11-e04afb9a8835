using System.ComponentModel.DataAnnotations;
using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Integration Configuration entity - links integrations to APIs and objects with direction settings
/// </summary>
public class IntegrationConfiguration : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Integration ID this configuration belongs to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Integration API ID this configuration uses
    /// </summary>
    public Guid IntegrationApiId { get; set; }

    /// <summary>
    /// Object ID this configuration applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Data flow direction (e.g., "In", "Out", "Both")
    /// </summary>
    [MaxLength(10)]
    public string? Direction { get; set; }

    // Navigation properties
    /// <summary>
    /// Integration this configuration belongs to
    /// </summary>
    public virtual Integration Integration { get; set; } = null!;

    /// <summary>
    /// Integration API this configuration uses
    /// </summary>
    public virtual IntegrationApi IntegrationApi { get; set; } = null!;

    /// <summary>
    /// Object this configuration applies to
    /// </summary>
    public virtual Object Object { get; set; } = null!;
}
