using Application.FieldMappings.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Commands;

/// <summary>
/// Create multiple field mappings command handler
/// </summary>
public class CreateFieldMappingsCommandHandler : IRequestHandler<CreateFieldMappingsCommand, Result<List<ViewFieldMappingDto>>>
{
    private readonly IRepository<FieldMapping> _fieldMappingRepository;
    private readonly IReadRepository<Domain.Entities.ObjectMetadata> _objectMetadataRepository;
    private readonly IReadRepository<User> _userRepository;
    private readonly IReadRepository<Role> _roleRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateFieldMappingsCommandHandler(
        IRepository<FieldMapping> fieldMappingRepository,
        IReadRepository<Domain.Entities.ObjectMetadata> objectMetadataRepository,
        IReadRepository<User> userRepository,
        IReadRepository<Role> roleRepository)
    {
        _fieldMappingRepository = fieldMappingRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _userRepository = userRepository;
        _roleRepository = roleRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<List<ViewFieldMappingDto>>> Handle(CreateFieldMappingsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (request.FieldMappings == null || !request.FieldMappings.Any())
            {
                return Result<List<ViewFieldMappingDto>>.Failure("No field mappings provided.");
            }

            var createdFieldMappings = new List<FieldMapping>();
            var viewDtos = new List<ViewFieldMappingDto>();
            var errors = new List<string>();

            // Process each field mapping request
            for (int i = 0; i < request.FieldMappings.Count; i++)
            {
                var fieldMappingRequest = request.FieldMappings[i];
                
                try
                {
                    // Validate required fields
                    if (string.IsNullOrWhiteSpace(fieldMappingRequest.SourceField))
                    {
                        errors.Add($"Field mapping {i + 1}: SourceField is required.");
                        continue;
                    }

                    if (string.IsNullOrWhiteSpace(fieldMappingRequest.SourceType))
                    {
                        errors.Add($"Field mapping {i + 1}: SourceType is required.");
                        continue;
                    }

                    // Validate optional foreign keys
                    Domain.Entities.ObjectMetadata? objectMetadata = null;
                    if (fieldMappingRequest.ObjectMetadataId.HasValue)
                    {
                        objectMetadata = await _objectMetadataRepository.GetByIdAsync(fieldMappingRequest.ObjectMetadataId.Value, cancellationToken);
                        if (objectMetadata == null)
                        {
                            errors.Add($"Field mapping {i + 1}: Object metadata not found.");
                            continue;
                        }
                    }

                    User? user = null;
                    if (fieldMappingRequest.UserId.HasValue)
                    {
                        user = await _userRepository.GetByIdAsync(fieldMappingRequest.UserId.Value, cancellationToken);
                        if (user == null)
                        {
                            errors.Add($"Field mapping {i + 1}: User not found.");
                            continue;
                        }
                    }

                    Role? role = null;
                    if (fieldMappingRequest.RoleId.HasValue)
                    {
                        role = await _roleRepository.GetByIdAsync(fieldMappingRequest.RoleId.Value, cancellationToken);
                        if (role == null)
                        {
                            errors.Add($"Field mapping {i + 1}: Role not found.");
                            continue;
                        }
                    }

                    // Create new field mapping
                    var fieldMapping = new FieldMapping
                    {
                        IntegrationId = fieldMappingRequest.IntegrationId,
                        ApiName = fieldMappingRequest.ApiName,
                        SourceField = fieldMappingRequest.SourceField,
                        SourceType = fieldMappingRequest.SourceType,
                        ObjectMetadataId = fieldMappingRequest.ObjectMetadataId,
                        UserId = fieldMappingRequest.UserId,
                        RoleId = fieldMappingRequest.RoleId,
                        TargetObjectName = fieldMappingRequest.TargetObjectName,
                        Notes = fieldMappingRequest.Notes
                    };

                    createdFieldMappings.Add(fieldMapping);

                    // Create view DTO
                    var viewDto = new ViewFieldMappingDto
                    {
                        Id = fieldMapping.Id,
                        IntegrationId = fieldMapping.IntegrationId,
                        ApiName = fieldMapping.ApiName,
                        SourceField = fieldMapping.SourceField,
                        SourceType = fieldMapping.SourceType,
                        ObjectMetadataId = fieldMapping.ObjectMetadataId,
                        ObjectMetadataKey = objectMetadata?.Metadata?.Name, // Updated: Name is now Name property
                        UserId = fieldMapping.UserId,
                        UserName = user?.UserName,
                        RoleId = fieldMapping.RoleId,
                        RoleName = role?.Name,
                        TargetObjectName = fieldMapping.TargetObjectName,
                        Notes = fieldMapping.Notes,
                        CreatedAt = fieldMapping.CreatedAt,
                        CreatedBy = fieldMapping.CreatedBy,
                        ModifiedAt = fieldMapping.ModifiedAt,
                        ModifiedBy = fieldMapping.ModifiedBy
                    };

                    viewDtos.Add(viewDto);
                }
                catch (Exception ex)
                {
                    errors.Add($"Field mapping {i + 1}: {ex.Message}");
                }
            }

            // If we have valid field mappings to create, save them in bulk
            if (createdFieldMappings.Any())
            {
                var savedFieldMappings = await _fieldMappingRepository.AddRangeAsync(createdFieldMappings, cancellationToken);
                var savedFieldMappingsList = savedFieldMappings.ToList();

                // Update the view DTOs with the actual saved data (including generated IDs and timestamps)
                for (int i = 0; i < savedFieldMappingsList.Count; i++)
                {
                    var savedMapping = savedFieldMappingsList[i];
                    var viewDto = viewDtos[i];

                    viewDto.Id = savedMapping.Id;
                    viewDto.CreatedAt = savedMapping.CreatedAt;
                    viewDto.CreatedBy = savedMapping.CreatedBy;
                    viewDto.ModifiedAt = savedMapping.ModifiedAt;
                    viewDto.ModifiedBy = savedMapping.ModifiedBy;
                }
            }

            // Prepare result
            var result = Result<List<ViewFieldMappingDto>>.Success(viewDtos);
            
            if (errors.Any())
            {
                if (viewDtos.Any())
                {
                    // Partial success
                    var warningMessage = $"Partial success: {viewDtos.Count} field mappings created successfully. Errors: {string.Join("; ", errors)}";
                    result.Message = warningMessage;
                }
                else
                {
                    // Complete failure
                    return Result<List<ViewFieldMappingDto>>.Failure($"Failed to create field mappings: {string.Join("; ", errors)}");
                }
            }
            else
            {
                result.Message = $"Successfully created {viewDtos.Count} field mappings.";
            }

            return result;
        }
        catch (Exception ex)
        {
            return Result<List<ViewFieldMappingDto>>.Failure($"Failed to create field mappings: {ex.Message}");
        }
    }
}
