// COMMENTED OUT - Contains IsRefId and IsUserId properties that have been removed
// TODO: Update to remove these properties or remove if not needed

/*
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using Application.ObjectValues.Specifications;

namespace Application.ObjectValues.Commands.UpsertSingle;

/// <summary>
/// Handler for UpsertSingleObjectValueCommand
/// </summary>
public class UpsertSingleObjectValueCommandHandler : IRequestHandler<UpsertSingleObjectValueCommand, Result<UpsertSingleObjectValueResponse>>
{
    private readonly IRepository<ObjectValue> _objectValueRepository;
    private readonly ILogger<UpsertSingleObjectValueCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertSingleObjectValueCommandHandler(
        IRepository<ObjectValue> objectValueRepository,
        ILogger<UpsertSingleObjectValueCommandHandler> logger)
    {
        _objectValueRepository = objectValueRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// RefId Logic: If RefId doesn't exist, it will be created automatically when the first ObjectValue is inserted
    /// ObjectValue Id Logic: If Id is provided but doesn't exist, create new ObjectValue with that specific Id
    /// </summary>
    public async Task<Result<UpsertSingleObjectValueResponse>> Handle(UpsertSingleObjectValueCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Upserting ObjectValue instance with RefId: {RefId}, Count: {Count}",
                request.RefId, request.ObjectValues.Count);

            var response = new UpsertSingleObjectValueResponse
            {
                RefId = request.RefId,
                TotalProcessed = request.ObjectValues.Count
            };

            foreach (var objectValueData in request.ObjectValues)
            {
                try
                {
                    ObjectValue objectValue;
                    bool wasInserted;

                    if (objectValueData.Id.HasValue)
                    {
                        // Try to find existing record by ID
                        var specification = new ObjectValueByIdSpecification(objectValueData.Id.Value);
                        var existingObjectValue = await _objectValueRepository.GetBySpecAsync(specification, cancellationToken);

                        if (existingObjectValue != null)
                        {
                            // Update the existing entity
                            existingObjectValue.ObjectMetadataId = objectValueData.ObjectMetadataId;
                            existingObjectValue.RefId = request.RefId;
                            existingObjectValue.ParentObjectValueId = objectValueData.ParentObjectValueId;
                            existingObjectValue.Value = objectValueData.Value;
                            existingObjectValue.IsRefId = objectValueData.IsRefId;
                            existingObjectValue.IsUserId = objectValueData.IsUserId;
                            existingObjectValue.ModifiedAt = DateTime.UtcNow;

                            await _objectValueRepository.UpdateAsync(existingObjectValue, cancellationToken);
                            objectValue = existingObjectValue;
                            wasInserted = false;
                            response.UpdatedCount++;

                            _logger.LogInformation("Updated ObjectValue with Id: {Id}", objectValueData.Id);
                        }
                        else
                        {
                            // Create new record with the specified ID
                            objectValue = new ObjectValue
                            {
                                Id = objectValueData.Id.Value, // Use the provided ID
                                ObjectMetadataId = objectValueData.ObjectMetadataId,
                                RefId = request.RefId,
                                ParentObjectValueId = objectValueData.ParentObjectValueId,
                                Value = objectValueData.Value,
                                IsRefId = objectValueData.IsRefId,
                                IsUserId = objectValueData.IsUserId,
                                CreatedAt = DateTime.UtcNow,
                                ModifiedAt = DateTime.UtcNow
                            };

                            await _objectValueRepository.AddAsync(objectValue, cancellationToken);
                            wasInserted = true;
                            response.InsertedCount++;

                            _logger.LogInformation("Created new ObjectValue with specified Id: {Id} for MetadataId: {MetadataId}, RefId: {RefId}",
                                objectValue.Id, objectValueData.ObjectMetadataId, request.RefId);
                        }
                    }
                    else
                    {
                        // No Id provided - check if a record already exists for this MetadataId and RefId combination
                        var existingSpec = new ObjectValueByMetadataAndRefIdSpecification(objectValueData.ObjectMetadataId, request.RefId);
                        var existingRecord = await _objectValueRepository.GetBySpecAsync(existingSpec, cancellationToken);

                        if (existingRecord != null)
                        {
                            // Update existing record for this MetadataId and RefId combination
                            existingRecord.ParentObjectValueId = objectValueData.ParentObjectValueId;
                            existingRecord.Value = objectValueData.Value;
                            existingRecord.IsRefId = objectValueData.IsRefId;
                            existingRecord.IsUserId = objectValueData.IsUserId;
                            existingRecord.ModifiedAt = DateTime.UtcNow;

                            await _objectValueRepository.UpdateAsync(existingRecord, cancellationToken);
                            objectValue = existingRecord;
                            wasInserted = false;
                            response.UpdatedCount++;

                            _logger.LogInformation("Updated existing ObjectValue for MetadataId: {MetadataId}, RefId: {RefId}",
                                objectValueData.ObjectMetadataId, request.RefId);
                        }
                        else
                        {
                            // Create new record - RefId will be created automatically if it doesn't exist
                            objectValue = new ObjectValue
                            {
                                Id = Guid.NewGuid(),
                                ObjectMetadataId = objectValueData.ObjectMetadataId,
                                RefId = request.RefId, // This RefId will be created if it doesn't exist
                                ParentObjectValueId = objectValueData.ParentObjectValueId,
                                Value = objectValueData.Value,
                                IsRefId = objectValueData.IsRefId,
                                IsUserId = objectValueData.IsUserId,
                                CreatedAt = DateTime.UtcNow,
                                ModifiedAt = DateTime.UtcNow
                            };

                            await _objectValueRepository.AddAsync(objectValue, cancellationToken);
                            wasInserted = true;
                            response.InsertedCount++;

                            _logger.LogInformation("Created new ObjectValue with Id: {Id} for MetadataId: {MetadataId}, RefId: {RefId} (RefId created if new)",
                                objectValue.Id, objectValueData.ObjectMetadataId, request.RefId);
                        }
                    }

                    // Add to response
                    response.ObjectValues.Add(new ObjectValueResponseData
                    {
                        Id = objectValue.Id,
                        ObjectMetadataId = objectValue.ObjectMetadataId,
                        RefId = objectValue.RefId ?? Guid.Empty,
                        ParentObjectValueId = objectValue.ParentObjectValueId,
                        Value = objectValue.Value,
                        IsRefId = objectValue.IsRefId,
                        IsUserId = objectValue.IsUserId,
                        CreatedAt = objectValue.CreatedAt,
                        ModifiedAt = objectValue.ModifiedAt,
                        WasInserted = wasInserted
                    });
                }
                catch (Exception ex)
                {
                    response.FailedCount++;
                    var errorMessage = $"MetadataId {objectValueData.ObjectMetadataId}: {ex.Message}";
                    response.Errors.Add(errorMessage);
                    _logger.LogError(ex, "Exception occurred while upserting ObjectValue for MetadataId: {MetadataId}",
                        objectValueData.ObjectMetadataId);
                }
            }

            response.Message = $"Instance upsert completed. Inserted: {response.InsertedCount}, Updated: {response.UpdatedCount}, Failed: {response.FailedCount}";

            _logger.LogInformation("Completed upsert of ObjectValue instance. RefId: {RefId}, Inserted: {Inserted}, Updated: {Updated}, Failed: {Failed}",
                request.RefId, response.InsertedCount, response.UpdatedCount, response.FailedCount);

            return Result<UpsertSingleObjectValueResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting ObjectValue instance with RefId: {RefId}", request.RefId);
            return Result<UpsertSingleObjectValueResponse>.Failure($"Error upserting ObjectValue instance: {ex.Message}");
        }
    }
}
*/
