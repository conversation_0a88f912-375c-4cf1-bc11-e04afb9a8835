using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Metadata entity
/// </summary>
public class MetadataConfig : IEntityTypeConfiguration<Metadata>
{
    public void Configure(EntityTypeBuilder<Metadata> builder)
    {
        builder.ToTable("Metadata", "Genp");

        // Properties
        builder.Property(e => e.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.DataTypeId)
            .IsRequired();

        builder.Property(e => e.DisplayLabel)
            .HasMaxLength(255);

        builder.Property(e => e.HelpText)
            .HasColumnType("TEXT");

        builder.Property(e => e.IsVisible)
            .HasDefaultValue(true);

        builder.Property(e => e.IsReadonly)
            .HasDefaultValue(false);

        // Lookup override properties
        builder.Property(e => e.OverrideLookupType)
            .HasMaxLength(50);

        builder.HasIndex(e => e.DataTypeId)
            .HasDatabaseName("IX_Metadata_DataTypeId");

        builder.HasIndex(e => e.IsVisible)
            .HasDatabaseName("IX_Metadata_IsVisible")
            .HasFilter("\"IsVisible\" = true AND \"IsDeleted\" = false");

        // Lookup override indexes
        builder.HasIndex(e => e.OverrideLookupType)
            .HasDatabaseName("IX_Metadata_OverrideLookupType");

        builder.HasIndex(e => e.OverrideMasterContextId)
            .HasDatabaseName("IX_Metadata_OverrideMasterContextId");

        builder.HasIndex(e => e.OverrideTenantContextId)
            .HasDatabaseName("IX_Metadata_OverrideTenantContextId");

        builder.HasIndex(e => e.OverrideObjectLookupId)
            .HasDatabaseName("IX_Metadata_OverrideObjectLookupId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.DataType)
            .WithMany(e => e.Metadata)
            .HasForeignKey(e => e.DataTypeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(e => e.TenantInfoMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ProductMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.RoleMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.UserMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ObjectMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.SubscriptionMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        // Lookup override relationships
        builder.HasOne(e => e.OverrideMasterContext)
            .WithMany(e => e.MetadataOverrides)
            .HasForeignKey(e => e.OverrideMasterContextId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.OverrideTenantContext)
            .WithMany(e => e.MetadataOverrides)
            .HasForeignKey(e => e.OverrideTenantContextId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.OverrideObjectLookup)
            .WithMany(e => e.MetadataOverrides)
            .HasForeignKey(e => e.OverrideObjectLookupId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
