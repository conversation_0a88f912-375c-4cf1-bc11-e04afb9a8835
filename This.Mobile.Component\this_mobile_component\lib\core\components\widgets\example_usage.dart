import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/components/widgets/widgets.dart';

/// Example usage of all the custom input widgets following the 'this_componentName_input' naming convention
/// This demonstrates how to use input widgets with various configurations
class WidgetExamplePage extends StatefulWidget {
  const WidgetExamplePage({super.key});

  @override
  State<WidgetExamplePage> createState() => _WidgetExamplePageState();
}

class _WidgetExamplePageState extends State<WidgetExamplePage> {
  // Form state variables
  String _textValue = '';
  String _textareaValue = '';
  String _emailValue = '';
  String _phoneValue = '';
  String _numberValue = '';
  List<String> _checkboxValues = [];
  String? _radioValue;
  int? _yearValue;
  int? _monthValue;
  int? _dayValue;
  TimeOfDay? _timeValue;

  // Sample data for options
  final List<CheckboxOption> _checkboxOptions = [
    const CheckboxOption(value: 'option1', label: 'Option 1'),
    const CheckboxOption(value: 'option2', label: 'Option 2'),
    const CheckboxOption(value: 'option3', label: 'Option 3'),
  ];

  final List<RadioOption> _radioOptions = [
    const RadioOption(value: 'choice1', label: 'Choice 1', description: 'First choice'),
    const RadioOption(value: 'choice2', label: 'Choice 2', description: 'Second choice'),
    const RadioOption(value: 'choice3', label: 'Choice 3', description: 'Third choice'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Widget Examples'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Text Input Examples
            _buildSection(
              'Text Input',
              [
                ThisTextInput(
                  id: 'text_input_example',
                  label: 'Text Input',
                  value: _textValue,
                  onChanged: (value) => setState(() => _textValue = value),
                  placeholder: 'Enter some text...',
                  required: true,
                  showCharacterCount: true,
                  maxLength: 100,
                  allowClear: true,
                ),
              ],
            ),

            // Textarea Input Examples
            _buildSection(
              'Textarea Input',
              [
                ThisTextareaInput(
                  id: 'textarea_input_example',
                  label: 'Textarea Input',
                  value: _textareaValue,
                  onChanged: (value) => setState(() => _textareaValue = value),
                  placeholder: 'Enter multiple lines of text...',
                  minLines: 3,
                  maxLines: 6,
                  showCharacterCount: true,
                  maxLength: 500,
                ),
              ],
            ),

            // Email Input Examples
            _buildSection(
              'Email Input',
              [
                ThisEmailInput(
                  id: 'email_input_example',
                  label: 'Email Input',
                  value: _emailValue,
                  onChanged: (value) => setState(() => _emailValue = value),
                  placeholder: 'Enter your email...',
                  required: true,
                  allowedDomains: ['gmail.com', 'yahoo.com'],
                  showValidationIcon: true,
                ),
              ],
            ),

            // Phone Input Examples
            _buildSection(
              'Phone Input',
              [
                ThisPhoneInput(
                  id: 'phone_input_example',
                  label: 'Phone Input',
                  value: _phoneValue,
                  onChanged: (value) => setState(() => _phoneValue = value),
                  placeholder: 'Enter your phone...',
                  required: true,
                  defaultCountry: 'US',
                  showFlag: true,
                ),
              ],
            ),

            // Number Input Examples
            _buildSection(
              'Number Input',
              [
                ThisNumberInput(
                  id: 'number_input_example',
                  label: 'Number Input',
                  value: _numberValue,
                  onChanged: (value) => setState(() => _numberValue = value),
                  placeholder: 'Enter amount...',
                  required: true,
                  currency: '\$',
                  decimals: 2,
                  thousandsSeparator: true,
                ),
              ],
            ),

            // Checkbox Input Examples
            _buildSection(
              'Checkbox Input',
              [
                ThisCheckboxInput(
                  id: 'checkbox_input_example',
                  label: 'Checkbox Input',
                  options: _checkboxOptions,
                  value: _checkboxValues,
                  onChanged: (values) => setState(() => _checkboxValues = values),
                  allowSelectAll: true,
                  required: true,
                  minSelected: 1,
                ),
              ],
            ),

            // Radio Input Examples
            _buildSection(
              'Radio Input',
              [
                ThisRadioInput(
                  id: 'radio_input_example',
                  label: 'Radio Input',
                  options: _radioOptions,
                  value: _radioValue,
                  onChanged: (value) => setState(() => _radioValue = value),
                  required: true,
                ),
              ],
            ),

            // Year Input Examples
            _buildSection(
              'Year Input',
              [
                ThisYearInput(
                  id: 'year_input_example',
                  label: 'Year Input',
                  value: _yearValue,
                  onChanged: (value) => setState(() => _yearValue = value),
                  showDropdown: true,
                  minYear: 2000,
                  maxYear: 2030,
                  required: true,
                ),
              ],
            ),

            // Month Input Examples
            _buildSection(
              'Month Input',
              [
                ThisMonthInput(
                  id: 'month_input_example',
                  label: 'Month Input',
                  value: _monthValue,
                  onChanged: (value) => setState(() => _monthValue = value),
                  displayFormat: MonthDisplayFormat.full,
                  required: true,
                ),
              ],
            ),

            // Day Input Examples
            _buildSection(
              'Day Input',
              [
                ThisDayInput(
                  id: 'day_input_example',
                  label: 'Day Input',
                  value: _dayValue,
                  onChanged: (value) => setState(() => _dayValue = value),
                  month: _monthValue,
                  year: _yearValue,
                  showDropdown: true,
                  required: true,
                ),
              ],
            ),

            // Time Input Examples
            _buildSection(
              'Time Input',
              [
                ThisTimeInput(
                  id: 'time_input_example',
                  label: 'Time Input',
                  value: _timeValue,
                  onChanged: (value) => setState(() => _timeValue = value),
                  use24HourFormat: false,
                  showTimePicker: true,
                  required: true,
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[700]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
