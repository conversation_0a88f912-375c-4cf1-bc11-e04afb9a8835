namespace Infrastructure.Database.Repositories;

/// <summary>
/// DTOs for mapping raw SQL query results to strongly-typed objects
/// Used by HierarchicalEntityDataRepository for Dapper mapping
/// </summary>

/// <summary>
/// DTO for mapping product data from SQL queries
/// </summary>
public class ProductDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? Version { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// DTO for mapping feature data from SQL queries
/// </summary>
public class FeatureDto
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// DTO for mapping object data from SQL queries
/// </summary>
public class ObjectDto
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; } // Changed from FeatureId to ProductId
    public Guid? ParentObjectId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// DTO for mapping product metadata - BASED ON ACTUAL ENTITIES (ProductMetadata + Metadata + DataType)
/// </summary>
public class ProductMetadataDto
{
    // ProductMetadata Link Properties (from ProductMetadata entity)
    public Guid ProductId { get; set; }
    public Guid ProductMetadataId { get; set; }
    public bool IsUnique { get; set; }
    public bool MetadataLinkIsActive { get; set; }
    public bool ShouldVisibleInList { get; set; }
    public bool ShouldVisibleInEdit { get; set; }
    public bool ShouldVisibleInCreate { get; set; }
    public bool ShouldVisibleInView { get; set; }
    public bool IsCalculate { get; set; }

    // Metadata Properties (UPDATED from Metadata entity)
    public Guid MetadataId { get; set; }
    public string? Name { get; set; }
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool IsVisible { get; set; }
    public bool IsReadonly { get; set; }
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool? IsRequired { get; set; }
    public string? Placeholder { get; set; }
    public string? DefaultOptions { get; set; }
    public int? MaxSelections { get; set; }
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSize { get; set; }
    public string? ErrorMessage { get; set; }

    // Additional Error Messages from Metadata entity
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }
    public string? FileSizeErrorMessage { get; set; }

    // Additional UI Properties from Metadata entity
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }

    // DataType Properties (EXACTLY from DataType entity)
    public Guid DataTypeId { get; set; }
    public string? DataTypeName { get; set; }
    public string? DataTypeDisplayName { get; set; }
    public string? DataTypeCategory { get; set; }
    public string? DataTypeUiComponent { get; set; }

    // Validation Rules
    public string? DataTypeValidationPattern { get; set; }
    public int? DataTypeMinLength { get; set; }
    public int? DataTypeMaxLength { get; set; }
    public decimal? DataTypeMinValue { get; set; }
    public decimal? DataTypeMaxValue { get; set; }
    public int? DataTypeDecimalPlaces { get; set; }
    public decimal? DataTypeStepValue { get; set; }
    public bool DataTypeIsRequired { get; set; }

    // UI Properties
    public string? DataTypeInputType { get; set; }
    public string? DataTypeInputMask { get; set; }
    public string? DataTypePlaceholder { get; set; }
    public string? DataTypeHtmlAttributes { get; set; }

    // Choice Options
    public string? DataTypeDefaultOptions { get; set; }
    public bool DataTypeAllowsMultiple { get; set; }
    public bool DataTypeAllowsCustomOptions { get; set; }
    public int? DataTypeMaxSelections { get; set; }

    // File/Media Properties
    public string? DataTypeAllowedFileTypes { get; set; }
    public long? DataTypeMaxFileSizeBytes { get; set; }

    // Error Messages
    public string? DataTypeRequiredErrorMessage { get; set; }
    public string? DataTypePatternErrorMessage { get; set; }
    public string? DataTypeMinLengthErrorMessage { get; set; }
    public string? DataTypeMaxLengthErrorMessage { get; set; }
    public string? DataTypeMinValueErrorMessage { get; set; }
    public string? DataTypeMaxValueErrorMessage { get; set; }
    public string? DataTypeFileTypeErrorMessage { get; set; }
    public string? DataTypeFileSizeErrorMessage { get; set; }
    public string? DataTypeErrorMessage { get; set; }
    public string? DataTypeDisplayLabel { get; set; }
    public string? DataTypeHelpText { get; set; }
    public string? DataTypeFieldOrder { get; set; }
    public bool? DataTypeIsVisible { get; set; }
    public bool? DataTypeIsReadonly { get; set; }

    // Lookup Configuration Properties
    public string? DataTypeLookupType { get; set; }
    public string? DataTypeOverrideLookupType { get; set; }
    public Guid? DataTypeOverrideMasterContextId { get; set; }
    public Guid? DataTypeOverrideObjectLookupId { get; set; }
    public Guid? DataTypeOverrideTenantContextId { get; set; }
    public Guid? DataTypeMasterContextId { get; set; }
    public Guid? DataTypeTenantContextId { get; set; }
    public Guid? DataTypeObjectLookupId { get; set; }

    // Status
    public bool DataTypeIsActive { get; set; }
}

/// <summary>
/// DTO for mapping object metadata with COMPLETE datatype information from SQL queries - ALL PROPERTIES
/// </summary>
public class ObjectMetadataDto
{
    // Object Metadata Link Properties
    public Guid ObjectId { get; set; }
    public Guid ObjectMetadataId { get; set; }
    public bool IsUnique { get; set; }
    public bool MetadataLinkIsActive { get; set; }
    public bool? ShouldVisibleInList { get; set; }
    public bool? ShouldVisibleInEdit { get; set; }
    public bool? ShouldVisibleInCreate { get; set; }
    public bool? ShouldVisibleInView { get; set; }
    public bool? IsCalculate { get; set; }

    // Metadata Properties - ALL FIELDS (UPDATED)
    public Guid MetadataId { get; set; }
    public string? Name { get; set; }
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool? IsRequired { get; set; }
    public string? Placeholder { get; set; }
    public string? DefaultOptions { get; set; }
    public int? MaxSelections { get; set; }
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSize { get; set; }
    public string? ErrorMessage { get; set; }

    // Additional Error Messages from Metadata entity
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }
    public string? FileSizeErrorMessage { get; set; }

    // Additional UI Properties from Metadata entity
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }

    // DataType Properties - ALL FIELDS
    public Guid DataTypeId { get; set; }
    public string? DataTypeName { get; set; }
    public string? DataTypeDisplayName { get; set; }
    public string? DataTypeCategory { get; set; }
    public string? DataTypeUiComponent { get; set; }
    public string? DataTypeValidationPattern { get; set; }
    public int? DataTypeMinLength { get; set; }
    public int? DataTypeMaxLength { get; set; }
    public decimal? DataTypeMinValue { get; set; }
    public decimal? DataTypeMaxValue { get; set; }
    public int? DataTypeDecimalPlaces { get; set; }
    public decimal? DataTypeStepValue { get; set; }
    public bool? DataTypeIsRequired { get; set; }
    public string? DataTypeInputType { get; set; }
    public string? DataTypeInputMask { get; set; }
    public string? DataTypePlaceholder { get; set; }
    public string? DataTypeHtmlAttributes { get; set; }
    public string? DataTypeDefaultOptions { get; set; }
    public bool? DataTypeAllowsMultiple { get; set; }
    public bool? DataTypeAllowsCustomOptions { get; set; }
    public int? DataTypeMaxSelections { get; set; }
    public string? DataTypeAllowedFileTypes { get; set; }
    public long? DataTypeMaxFileSizeBytes { get; set; }
    public string? DataTypeRequiredErrorMessage { get; set; }
    public string? DataTypePatternErrorMessage { get; set; }
    public string? DataTypeMinLengthErrorMessage { get; set; }
    public string? DataTypeMaxLengthErrorMessage { get; set; }
    public string? DataTypeMinValueErrorMessage { get; set; }
    public string? DataTypeMaxValueErrorMessage { get; set; }
    public string? DataTypeFileTypeErrorMessage { get; set; }
    public string? DataTypeFileSizeErrorMessage { get; set; }
    public string? DataTypeErrorMessage { get; set; }
    public string? DataTypeDisplayLabel { get; set; }
    public string? DataTypeHelpText { get; set; }
    public string? DataTypeFieldOrder { get; set; }
    public bool? DataTypeIsVisible { get; set; }
    public bool? DataTypeIsReadonly { get; set; }
    public string? DataTypeLookupType { get; set; }
    public string? DataTypeOverrideLookupType { get; set; }
    public Guid? DataTypeOverrideMasterContextId { get; set; }
    public Guid? DataTypeOverrideObjectLookupId { get; set; }
    public Guid? DataTypeOverrideTenantContextId { get; set; }
    public Guid? DataTypeMasterContextId { get; set; }
    public Guid? DataTypeTenantContextId { get; set; }
    public Guid? DataTypeObjectLookupId { get; set; }
    public bool? DataTypeIsActive { get; set; }
}
