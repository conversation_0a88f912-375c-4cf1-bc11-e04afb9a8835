using Application.MetadataManagement.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Queries;

/// <summary>
/// Get Metadata by ID query handler
/// </summary>
public class GetMetadataByIdQueryHandler : IRequestHandler<GetMetadataByIdQuery, Result<MetadataDto>>
{
    private readonly IRepository<Domain.Entities.Metadata> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetMetadataByIdQueryHandler(IRepository<Domain.Entities.Metadata> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<MetadataDto>> Handle(GetMetadataByIdQuery request, CancellationToken cancellationToken)
    {
        var metadata = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (metadata == null)
        {
            return Result<MetadataDto>.Failure($"Metadata with ID '{request.Id}' not found.");
        }

        var dto = new MetadataDto
        {
            Id = metadata.Id,
            Name = metadata.Name, // Updated: Name property maps to Name
            DataTypeId = metadata.DataTypeId,
            DataTypeName = metadata.DataType?.Name,
            ValidationPattern = metadata.ValidationPattern, // Updated property name
            MinLength = metadata.MinLength, // Updated property name
            MaxLength = metadata.MaxLength, // Updated property name
            MinValue = metadata.MinValue, // Updated property name
            MaxValue = metadata.MaxValue, // Updated property name
            IsRequired = metadata.IsRequired, // Updated property name
            Placeholder = metadata.Placeholder, // Updated property name
            DefaultOptions = metadata.DefaultOptions, // Updated property name
            MaxSelections = metadata.MaxSelections, // Updated property name
            AllowedFileTypes = metadata.AllowedFileTypes, // Updated property name
            MaxFileSize = metadata.MaxFileSize, // Updated property name
            ErrorMessage = metadata.ErrorMessage, // Updated property name
            DisplayLabel = metadata.DisplayLabel,
            HelpText = metadata.HelpText,
            FieldOrder = metadata.FieldOrder,
            IsVisible = metadata.IsVisible,
            IsReadonly = metadata.IsReadonly,
            CreatedAt = metadata.CreatedAt,
            CreatedBy = metadata.CreatedBy ?? Guid.Empty,
            ModifiedAt = metadata.ModifiedAt,
            ModifiedBy = metadata.ModifiedBy
        };

        return Result<MetadataDto>.Success(dto);
    }
}
