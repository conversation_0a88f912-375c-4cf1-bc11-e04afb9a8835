// COMMENTED OUT - Contains IsRefId and IsUserId properties that have been removed
// TODO: Update to remove these properties or remove if not needed


using Abstraction.Common;
using Application.ObjectValues.Specifications;
using Domain.Entities;
using Abstraction.Database.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Shared.Common.Response;

namespace Application.Objects.Commands.UpsertObjectWithMetadata;

/// <summary>
/// Handler for UpsertObjectWithMetadataCommand
/// </summary>
public class UpsertObjectWithMetadataCommandHandler : IRequestHandler<UpsertObjectWithMetadataCommand, Result<UpsertObjectWithMetadataResponse>>
{
    private readonly IRepository<ObjectValue> _objectValueRepository;
    private readonly IRepository<ObjectMetadata> _objectMetadataRepository;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly ILogger<UpsertObjectWithMetadataCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertObjectWithMetadataCommandHandler(
        IRepository<ObjectValue> objectValueRepository,
        IRepository<ObjectMetadata> objectMetadataRepository,
        IRepository<Domain.Entities.Object> objectRepository,
        ILogger<UpsertObjectWithMetadataCommandHandler> logger)
    {
        _objectValueRepository = objectValueRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _objectRepository = objectRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<UpsertObjectWithMetadataResponse>> Handle(UpsertObjectWithMetadataCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Extract required properties
            if (!request.MetadataProperties.TryGetValue("ObjectId", out var objectIdValue) || objectIdValue == null)
            {
                return Result<UpsertObjectWithMetadataResponse>.Failure("ObjectId is required");
            }

            if (!request.MetadataProperties.TryGetValue("RefId", out var refIdValue) || refIdValue == null)
            {
                return Result<UpsertObjectWithMetadataResponse>.Failure("RefId is required");
            }

            // Parse ObjectId and RefId
            if (!TryParseGuid(objectIdValue, out var objectId))
            {
                return Result<UpsertObjectWithMetadataResponse>.Failure("Invalid ObjectId format");
            }

            if (!TryParseGuid(refIdValue, out var refId))
            {
                return Result<UpsertObjectWithMetadataResponse>.Failure("Invalid RefId format");
            }

            // Parse ParentObjectValueId (optional)
            Guid? parentObjectValueId = null;
            if (request.MetadataProperties.TryGetValue("ParentObjectValueId", out var parentIdValue) && parentIdValue != null)
            {
                if (TryParseGuid(parentIdValue, out var parsedParentId))
                {
                    parentObjectValueId = parsedParentId;
                }
            }

            _logger.LogInformation("Processing object upsert with metadata for ObjectId: {ObjectId}, RefId: {RefId}", objectId, refId);

            // Verify object exists
            var objectEntity = await _objectRepository.GetByIdAsync(objectId, cancellationToken);
            if (objectEntity == null)
            {
                return Result<UpsertObjectWithMetadataResponse>.Failure($"Object with ID {objectId} not found");
            }

            // Debug: Log available metadata for this object
            var availableMetadataSpec = new Application.ObjectMetadataManagement.Specifications.ObjectMetadataByObjectIdSpec(objectId);
            var availableMetadata = await _objectMetadataRepository.ListAsync(availableMetadataSpec, cancellationToken);
            _logger.LogInformation("Found {Count} metadata entries for ObjectId {ObjectId}. MetadataKeys: {MetadataKeys}",
                availableMetadata.Count, objectId,
                string.Join(", ", availableMetadata.Select(om => $"'{om.Metadata?.Name}'")));

            var response = new UpsertObjectWithMetadataResponse
            {
                ObjectId = objectId,
                RefId = refId,
                ParentObjectValueId = parentObjectValueId
            };

            // Process metadata properties (exclude system properties)
            var systemProperties = new HashSet<string> { "ObjectId", "RefId", "ParentObjectValueId" };
            var metadataProperties = request.MetadataProperties
                .Where(kvp => !systemProperties.Contains(kvp.Key))
                .ToList();

            response.TotalProcessed = metadataProperties.Count;

            foreach (var property in metadataProperties)
            {
                await ProcessMetadataProperty(objectId, refId, parentObjectValueId, property.Key, property.Value, response, cancellationToken);
            }

            _logger.LogInformation("Completed object upsert with metadata. Processed: {Total}, Inserted: {Inserted}, Updated: {Updated}, Skipped: {Skipped}",
                response.TotalProcessed, response.InsertedCount, response.UpdatedCount, response.SkippedCount);

            return Result<UpsertObjectWithMetadataResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing object upsert with metadata");
            return Result<UpsertObjectWithMetadataResponse>.Failure($"An error occurred: {ex.Message}");
        }
    }

    /// <summary>
    /// Process a single metadata property
    /// </summary>
    private async Task ProcessMetadataProperty(
        Guid objectId,
        Guid refId,
        Guid? parentObjectValueId,
        string metadataKey,
        object? value,
        UpsertObjectWithMetadataResponse response,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing metadata property: {MetadataKey} for ObjectId: {ObjectId}", metadataKey, objectId);

            // Find ObjectMetadata by ObjectId and Name (prioritizes Name over DisplayLabel)
            var spec = new ObjectMetadataByObjectIdAndMetadataKeySpec(objectId, metadataKey);
            var objectMetadata = await _objectMetadataRepository.FirstOrDefaultAsync(spec, cancellationToken);

            // If not found by the primary specification, try additional fallback matching
            if (objectMetadata == null)
            {
                _logger.LogInformation("Primary search failed, trying additional matching strategies for: {MetadataKey}", metadataKey);
                var allObjectMetadata = await _objectMetadataRepository.ListAsync(
                    new Application.ObjectMetadataManagement.Specifications.ObjectMetadataByObjectIdSpec(objectId),
                    cancellationToken);

                // Try various matching strategies with additional normalization
                var normalizedSearchTerm = metadataKey.Replace("_", " ").ToLower();
                var searchTermWithoutUnderscore = metadataKey.Replace("_", "").ToLower();

                objectMetadata = allObjectMetadata.FirstOrDefault(om =>
                    // Additional exact matches
                    om.Metadata?.Name?.Equals(metadataKey, StringComparison.OrdinalIgnoreCase) == true ||
                    om.Metadata?.DisplayLabel?.Equals(metadataKey, StringComparison.OrdinalIgnoreCase) == true ||
                    // Normalized matches (replace _ with space)
                    om.Metadata?.Name?.Equals(normalizedSearchTerm, StringComparison.OrdinalIgnoreCase) == true || // Updated: Name is now Name property
                    om.Metadata?.DisplayLabel?.Equals(normalizedSearchTerm, StringComparison.OrdinalIgnoreCase) == true ||
                    // Without underscore matches
                    om.Metadata?.Name?.Replace(" ", "").Equals(searchTermWithoutUnderscore, StringComparison.OrdinalIgnoreCase) == true || // Updated: Name is now Name property
                    om.Metadata?.DisplayLabel?.Replace(" ", "").Equals(searchTermWithoutUnderscore, StringComparison.OrdinalIgnoreCase) == true ||
                    // Contains matches (as last resort)
                    om.Metadata?.Name?.Contains(metadataKey, StringComparison.OrdinalIgnoreCase) == true || // Updated: Name is now Name property
                    om.Metadata?.DisplayLabel?.Contains(metadataKey, StringComparison.OrdinalIgnoreCase) == true);
            }

            if (objectMetadata == null)
            {
                response.SkippedCount++;
                response.SkippedProperties.Add(new SkippedPropertyInfo
                {
                    PropertyName = metadataKey,
                    Reason = "ObjectMetadata not found for this MetadataKey"
                });
                _logger.LogWarning("ObjectMetadata not found for ObjectId: {ObjectId}, MetadataKey: '{MetadataKey}'. Searched for exact match, case-insensitive match, and contains match.", objectId, metadataKey);
                return;
            }

            _logger.LogInformation("Found ObjectMetadata: {ObjectMetadataId} with MetadataKey: '{ActualMetadataKey}' for search term: '{SearchTerm}'",
                objectMetadata.Id, objectMetadata.Metadata?.Name, metadataKey); // Updated: Name is now Name property

            // Check if ObjectValue already exists for this RefId and ObjectMetadata
            var existingValueSpec = new ObjectValueByRefIdAndObjectMetadataIdSpec(refId, objectMetadata.Id);
            var existingValue = await _objectValueRepository.FirstOrDefaultAsync(existingValueSpec, cancellationToken);

            var stringValue = value?.ToString();
            bool wasInserted = false;

            if (existingValue != null)
            {
                // Update existing value
                existingValue.Value = stringValue;
                existingValue.ParentObjectValueId = parentObjectValueId;
                existingValue.ModifiedAt = DateTime.UtcNow;

                await _objectValueRepository.UpdateAsync(existingValue, cancellationToken);
                response.UpdatedCount++;
                _logger.LogInformation("Updated ObjectValue for RefId: {RefId}, MetadataKey: {MetadataKey}", refId, metadataKey);
            }
            else
            {
                // Create new ObjectValue
                var newObjectValue = new ObjectValue
                {
                    Id = Guid.NewGuid(),
                    ObjectMetadataId = objectMetadata.Id,
                    RefId = refId,
                    ParentObjectValueId = parentObjectValueId,
                    Value = stringValue,
                    CreatedAt = DateTime.UtcNow,
                    ModifiedAt = DateTime.UtcNow
                };

                await _objectValueRepository.AddAsync(newObjectValue, cancellationToken);
                existingValue = newObjectValue;
                wasInserted = true;
                response.InsertedCount++;
                _logger.LogInformation("Created new ObjectValue for RefId: {RefId}, MetadataKey: {MetadataKey}", refId, metadataKey);
            }

            // Add to response
            response.ObjectValues.Add(new ObjectValueResponseData
            {
                Id = existingValue.Id,
                ObjectMetadataId = existingValue.ObjectMetadataId,
                RefId = existingValue.RefId ?? Guid.Empty,
                ParentObjectValueId = existingValue.ParentObjectValueId,
                Value = existingValue.Value,
                CreatedAt = existingValue.CreatedAt,
                ModifiedAt = existingValue.ModifiedAt,
                WasInserted = wasInserted,
                MetadataKey = metadataKey
            });
        }
        catch (Exception ex)
        {
            response.SkippedCount++;
            response.SkippedProperties.Add(new SkippedPropertyInfo
            {
                PropertyName = metadataKey,
                Reason = $"Error processing: {ex.Message}"
            });
            _logger.LogError(ex, "Error processing metadata property {MetadataKey} for ObjectId: {ObjectId}", metadataKey, objectId);
        }
    }

    /// <summary>
    /// Try to parse GUID from various formats
    /// </summary>
    private static bool TryParseGuid(object? value, out Guid guid)
    {
        guid = Guid.Empty;

        if (value == null) return false;

        // Handle JsonElement
        if (value is JsonElement jsonElement)
        {
            if (jsonElement.ValueKind == JsonValueKind.String)
            {
                return Guid.TryParse(jsonElement.GetString(), out guid);
            }
            return false;
        }

        // Handle string
        if (value is string stringValue)
        {
            return Guid.TryParse(stringValue, out guid);
        }

        // Handle Guid
        if (value is Guid guidValue)
        {
            guid = guidValue;
            return true;
        }

        return false;
    }
}

