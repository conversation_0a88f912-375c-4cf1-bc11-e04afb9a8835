using System.ComponentModel.DataAnnotations;
using System.Text.Json;

namespace Application.Templates.DTOs;

/// <summary>
/// Template DTO for responses
/// </summary>
public class TemplateDto
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID this template belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Template version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Template stage
    /// </summary>
    public string Stage { get; set; } = string.Empty;

    /// <summary>
    /// Template JSON content
    /// </summary>
    public JsonElement TemplateJson { get; set; }

    /// <summary>
    /// When the template was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Who created the template
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// When the template was published
    /// </summary>
    public DateTime? PublishedAt { get; set; }

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the template is deleted
    /// </summary>
    public bool IsDeleted { get; set; }
}

/// <summary>
/// Create template DTO
/// </summary>
public class CreateTemplateDto
{
    /// <summary>
    /// Product ID this template belongs to
    /// </summary>
    [Required]
    public Guid ProductId { get; set; }

    /// <summary>
    /// Template version
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Template stage (draft, live, beta, etc.)
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Stage { get; set; } = string.Empty;

    /// <summary>
    /// Template JSON content
    /// </summary>
    [Required]
    public JsonElement TemplateJson { get; set; }

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Update template DTO
/// </summary>
public class UpdateTemplateDto
{
    /// <summary>
    /// Template version
    /// </summary>
    [MaxLength(50)]
    public string? Version { get; set; }

    /// <summary>
    /// Template stage (draft, live, beta, etc.)
    /// </summary>
    [MaxLength(20)]
    public string? Stage { get; set; }

    /// <summary>
    /// Template JSON content
    /// </summary>
    public JsonElement? TemplateJson { get; set; }

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool? IsActive { get; set; }
}

/// <summary>
/// Template summary DTO for list views
/// </summary>
public class TemplateSummaryDto
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Template version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Template stage
    /// </summary>
    public string Stage { get; set; } = string.Empty;

    /// <summary>
    /// When the template was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the template was published
    /// </summary>
    public DateTime? PublishedAt { get; set; }

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool IsActive { get; set; }
}


