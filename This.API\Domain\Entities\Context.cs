using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Context entity for global lookup categories
/// Represents master lookup contexts that can be used across all tenants
/// </summary>
public class Context : AuditableEntity, IAggregateRoot
{
    
    /// <summary>
    /// Name of the context (e.g., "Countries", "Status", "Priority")
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the context
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Category for grouping contexts (e.g., "Geographic", "System", "Business")
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Navigation property for related lookups
    /// </summary>
    public virtual ICollection<Lookup> Lookups { get; set; } = new List<Lookup>();

    /// <summary>
    /// Navigation property for DataTypes that use this context
    /// </summary>
    public virtual ICollection<DataType> DataTypes { get; set; } = new List<DataType>();

    /// <summary>
    /// Navigation property for Metadata that override with this context
    /// </summary>
    public virtual ICollection<Metadata> MetadataOverrides { get; set; } = new List<Metadata>();
}
