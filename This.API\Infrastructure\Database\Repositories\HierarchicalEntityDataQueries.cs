namespace Infrastructure.Database.Repositories;

/// <summary>
/// Static SQL queries for hierarchical entity data operations
/// Centralized location for all complex SQL queries used by HierarchicalEntityDataRepository
/// </summary>
public static class HierarchicalEntityDataQueries
{
    /// <summary>
    /// Base query to get products with filters
    /// </summary>
    public static string GetProductsWithFiltersSql => @"
        SELECT ""Id"", ""Name"", ""Description"", ""Version"", ""IsActive""
        FROM ""Genp"".""Products""
        WHERE ""IsDeleted"" = false AND ""TenantId"" = @TenantId";

    /// <summary>
    /// Query to get objects for specific products (Features removed - direct Product to Object relationship)
    /// </summary>
    public static string GetObjectsForProductsSql => @"
        SELECT ""Id"", ""ProductId"", ""ParentObjectId"", ""Name"", ""Description"",
               COALESCE(""IsActive"", true) as ""IsActive""
        FROM ""Genp"".""Objects""
        WHERE ""IsDeleted"" = false AND ""TenantId"" = @TenantId AND ""ProductId"" = ANY(@ProductIds)";
    /// <summary>
    /// Query to get product metadata with ACTUAL entity properties - BASED ON REAL ENTITIES
    /// </summary>
    public static string GetProductMetadataWithDataTypesSql => @"
        SELECT
            -- ProductMetadata Link Properties (ACTUAL from ProductMetadata entity)
            pm.""ProductId"",
            pm.""Id"" as ProductMetadataId,
            pm.""IsUnique"",
            pm.""IsActive"" as MetadataLinkIsActive,
            pm.""IsVisibleInList"",
            pm.""IsVisibleInEdit"",
            pm.""IsVisibleInCreate"",
            pm.""IsVisibleInView"",
            pm.""IsCalculated"",

            -- Metadata Properties (UPDATED from Metadata entity)
            m.""Id"" as MetadataId,
            m.""Name"" as Name,
            m.""DisplayLabel"",
            m.""HelpText"",
            m.""FieldOrder"",
            m.""IsVisible"",
            m.""IsReadonly"",
            m.""ValidationPattern"",
            m.""MinLength"",
            m.""MaxLength"",
            m.""MinValue"",
            m.""MaxValue"",
            m.""IsRequired"",
            m.""Placeholder"",
            m.""DefaultOptions"",
            m.""MaxSelections"",
            m.""AllowedFileTypes"",
            m.""MaxFileSize"",
            m.""ErrorMessage"",
            m.""RequiredErrorMessage"",
            m.""PatternErrorMessage"",
            m.""MinLengthErrorMessage"",
            m.""MaxLengthErrorMessage"",
            m.""MinValueErrorMessage"",
            m.""MaxValueErrorMessage"",
            m.""FileTypeErrorMessage"",
            m.""InputType"",
            m.""InputMask"",
            m.""AllowsMultiple"",
            m.""AllowsCustomOptions"",

            -- DataType Properties (ACTUAL from DataType entity)
            dt.""Id"" as DataTypeId,
            dt.""Name"" as DataTypeName,
            dt.""DisplayName"" as DataTypeDisplayName,
            dt.""Category"" as DataTypeCategory,
            dt.""UiComponent"" as DataTypeUiComponent,
            dt.""ValidationPattern"" as DataTypeValidationPattern,
            dt.""MinLength"" as DataTypeMinLength,
            dt.""MaxLength"" as DataTypeMaxLength,
            dt.""MinValue"" as DataTypeMinValue,
            dt.""MaxValue"" as DataTypeMaxValue,
            dt.""DecimalPlaces"" as DataTypeDecimalPlaces,
            dt.""StepValue"" as DataTypeStepValue,
            dt.""IsRequired"" as DataTypeIsRequired,
            dt.""InputType"" as DataTypeInputType,
            dt.""InputMask"" as DataTypeInputMask,
            dt.""Placeholder"" as DataTypePlaceholder,
            dt.""HtmlAttributes"" as DataTypeHtmlAttributes,
            dt.""DefaultOptions"" as DataTypeDefaultOptions,
            dt.""AllowsMultiple"" as DataTypeAllowsMultiple,
            dt.""AllowsCustomOptions"" as DataTypeAllowsCustomOptions,
            dt.""MaxSelections"" as DataTypeMaxSelections,
            dt.""AllowedFileTypes"" as DataTypeAllowedFileTypes,
            dt.""MaxFileSizeBytes"" as DataTypeMaxFileSizeBytes,
            dt.""RequiredErrorMessage"" as DataTypeRequiredErrorMessage,
            dt.""PatternErrorMessage"" as DataTypePatternErrorMessage,
            dt.""MinLengthErrorMessage"" as DataTypeMinLengthErrorMessage,
            dt.""MaxLengthErrorMessage"" as DataTypeMaxLengthErrorMessage,
            dt.""MinValueErrorMessage"" as DataTypeMinValueErrorMessage,
            dt.""MaxValueErrorMessage"" as DataTypeMaxValueErrorMessage,
            dt.""FileTypeErrorMessage"" as DataTypeFileTypeErrorMessage,
            dt.""FileSizeErrorMessage"" as DataTypeFileSizeErrorMessage,
            dt.""ErrorMessage"" as DataTypeErrorMessage,
            dt.""DisplayLabel"" as DataTypeDisplayLabel,
            dt.""HelpText"" as DataTypeHelpText,
            dt.""FieldOrder"" as DataTypeFieldOrder,
            dt.""IsVisible"" as DataTypeIsVisible,
            dt.""IsReadonly"" as DataTypeIsReadonly,
            dt.""LookupType"" as DataTypeLookupType,
            dt.""OverrideLookupType"" as DataTypeOverrideLookupType,
            dt.""OverrideMasterContextId"" as DataTypeOverrideMasterContextId,
            dt.""OverrideObjectLookupId"" as DataTypeOverrideObjectLookupId,
            dt.""OverrideTenantContextId"" as DataTypeOverrideTenantContextId,
            dt.""MasterContextId"" as DataTypeMasterContextId,
            dt.""TenantContextId"" as DataTypeTenantContextId,
            dt.""ObjectLookupId"" as DataTypeObjectLookupId,
            dt.""IsActive"" as DataTypeIsActive
        FROM ""Genp"".""ProductMetadata"" pm
        INNER JOIN ""Genp"".""Metadata"" m ON pm.""MetadataId"" = m.""Id""
        INNER JOIN ""Genp"".""DataTypes"" dt ON m.""DataTypeId"" = dt.""Id""
        WHERE pm.""TenantId"" = @TenantId AND pm.""ProductId"" = ANY(@ProductIds) AND pm.""IsDeleted"" = false";


    /// <summary>
    /// Query to get object metadata with COMPLETE datatype information - ALL PROPERTIES
    /// </summary>
    public static string GetObjectMetadataWithDataTypesSql => @"
        SELECT
            -- ObjectMetadata Link Properties
            om.""ObjectId"",
            om.""Id"" as ObjectMetadataId,
            om.""IsUnique"",
            om.""IsActive"" as MetadataLinkIsActive,
            om.""IsVisibleInList"",
            om.""IsVisibleInEdit"",
            om.""IsVisibleInCreate"",
            om.""IsVisibleInView"",
            om.""IsCalculated"",

            -- Metadata Properties - ALL FIELDS (UPDATED)
            m.""Id"" as MetadataId,
            m.""Name"" as Name,
            m.""DisplayLabel"",
            m.""HelpText"",
            m.""FieldOrder"",
            m.""IsVisible"",
            m.""IsReadonly"",
            m.""ValidationPattern"",
            m.""MinLength"",
            m.""MaxLength"",
            m.""MinValue"",
            m.""MaxValue"",
            m.""IsRequired"",
            m.""Placeholder"",
            m.""DefaultOptions"",
            m.""MaxSelections"",
            m.""AllowedFileTypes"",
            m.""MaxFileSize"",
            m.""ErrorMessage"",
            m.""RequiredErrorMessage"",
            m.""PatternErrorMessage"",
            m.""MinLengthErrorMessage"",
            m.""MaxLengthErrorMessage"",
            m.""MinValueErrorMessage"",
            m.""MaxValueErrorMessage"",
            m.""FileTypeErrorMessage"",
            m.""InputType"",
            m.""InputMask"",
            m.""AllowsMultiple"",
            m.""AllowsCustomOptions"",

            -- DataType Properties - ALL FIELDS
            dt.""Id"" as DataTypeId,
            dt.""Name"" as DataTypeName,
            dt.""DisplayName"" as DataTypeDisplayName,
            dt.""Category"" as DataTypeCategory,
            dt.""UiComponent"" as DataTypeUiComponent,
            dt.""ValidationPattern"" as DataTypeValidationPattern,
            dt.""MinLength"" as DataTypeMinLength,
            dt.""MaxLength"" as DataTypeMaxLength,
            dt.""MinValue"" as DataTypeMinValue,
            dt.""MaxValue"" as DataTypeMaxValue,
            dt.""DecimalPlaces"" as DataTypeDecimalPlaces,
            dt.""StepValue"" as DataTypeStepValue,
            dt.""IsRequired"" as DataTypeIsRequired,
            dt.""InputType"" as DataTypeInputType,
            dt.""InputMask"" as DataTypeInputMask,
            dt.""Placeholder"" as DataTypePlaceholder,
            dt.""HtmlAttributes"" as DataTypeHtmlAttributes,
            dt.""DefaultOptions"" as DataTypeDefaultOptions,
            dt.""AllowsMultiple"" as DataTypeAllowsMultiple,
            dt.""AllowsCustomOptions"" as DataTypeAllowsCustomOptions,
            dt.""MaxSelections"" as DataTypeMaxSelections,
            dt.""AllowedFileTypes"" as DataTypeAllowedFileTypes,
            dt.""MaxFileSizeBytes"" as DataTypeMaxFileSizeBytes,
            dt.""RequiredErrorMessage"" as DataTypeRequiredErrorMessage,
            dt.""PatternErrorMessage"" as DataTypePatternErrorMessage,
            dt.""MinLengthErrorMessage"" as DataTypeMinLengthErrorMessage,
            dt.""MaxLengthErrorMessage"" as DataTypeMaxLengthErrorMessage,
            dt.""MinValueErrorMessage"" as DataTypeMinValueErrorMessage,
            dt.""MaxValueErrorMessage"" as DataTypeMaxValueErrorMessage,
            dt.""FileTypeErrorMessage"" as DataTypeFileTypeErrorMessage,
            dt.""FileSizeErrorMessage"" as DataTypeFileSizeErrorMessage,
            dt.""ErrorMessage"" as DataTypeErrorMessage,
            dt.""DisplayLabel"" as DataTypeDisplayLabel,
            dt.""HelpText"" as DataTypeHelpText,
            dt.""FieldOrder"" as DataTypeFieldOrder,
            dt.""IsVisible"" as DataTypeIsVisible,
            dt.""IsReadonly"" as DataTypeIsReadonly,
            dt.""LookupType"" as DataTypeLookupType,
            dt.""OverrideLookupType"" as DataTypeOverrideLookupType,
            dt.""OverrideMasterContextId"" as DataTypeOverrideMasterContextId,
            dt.""OverrideObjectLookupId"" as DataTypeOverrideObjectLookupId,
            dt.""OverrideTenantContextId"" as DataTypeOverrideTenantContextId,
            dt.""MasterContextId"" as DataTypeMasterContextId,
            dt.""TenantContextId"" as DataTypeTenantContextId,
            dt.""ObjectLookupId"" as DataTypeObjectLookupId,
            dt.""IsActive"" as DataTypeIsActive
        FROM ""Genp"".""ObjectMetadata"" om
        INNER JOIN ""Genp"".""Metadata"" m ON om.""MetadataId"" = m.""Id""
        INNER JOIN ""Genp"".""DataTypes"" dt ON m.""DataTypeId"" = dt.""Id""
        WHERE om.""TenantId"" = @TenantId AND om.""ObjectId"" = ANY(@ObjectIds) AND om.""IsDeleted"" = false";

    /// <summary>
    /// Helper method to add product filters to the base query
    /// </summary>
    public static string AddProductFilters(bool hasProductId, bool hasIsActive, bool hasSearchTerm, bool hasPagination)
    {
        var sql = GetProductsWithFiltersSql;

        if (hasProductId)
            sql += @" AND ""Id"" = @ProductId";

        if (hasIsActive)
            sql += @" AND ""IsActive"" = @IsActive";

        if (hasSearchTerm)
            sql += @" AND (""Name"" ILIKE @SearchTerm OR ""Description"" ILIKE @SearchTerm)";

        if (hasPagination)
            sql += @" ORDER BY ""Name"" OFFSET @Offset LIMIT @Limit";

        return sql;
    }

    /// <summary>
    /// Helper method to add object filters to the base query (replaces feature filters)
    /// </summary>
    public static string AddObjectFilters(bool hasObjectId, bool hasIsActive, bool hasSearchTerm)
    {
        var sql = GetObjectsForProductsSql;

        if (hasObjectId)
            sql += @" AND ""Id"" = @ObjectId";

        if (hasIsActive)
            // Handle potential type mismatch - use COALESCE for null values
            sql += @" AND COALESCE(""IsActive"", true) = @IsActive";

        if (hasSearchTerm)
            sql += @" AND (""Name"" ILIKE @SearchTerm OR ""Description"" ILIKE @SearchTerm)";

        // Add ORDER BY clause here instead of in the main query
        sql += @" ORDER BY CASE WHEN ""ParentObjectId"" IS NULL THEN 0 ELSE 1 END, ""Name""";

        return sql;
    }

    /// <summary>
    /// Helper method to add metadata visibility and active filters
    /// </summary>
    public static string AddMetadataFilters(string baseQuery, bool onlyVisible, bool onlyActive, string tableAlias)
    {
        var sql = baseQuery;

        if (onlyVisible)
            // Handle potential type mismatch - use COALESCE for null values
            sql += $@" AND COALESCE({tableAlias}.""IsVisibleInList"", true) = true";

        if (onlyActive)
            // Handle potential type mismatch - use COALESCE for null values
            sql += $@" AND COALESCE({tableAlias}.""IsActive"", true) = true";

        return sql;
    }
}
