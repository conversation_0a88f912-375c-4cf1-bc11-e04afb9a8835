using Application.MetadataManagement.DTOs;
using Application.MetadataManagement.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.MetadataManagement.Queries;

/// <summary>
/// Get Metadata query handler
/// </summary>
public class GetMetadataQueryHandler : IRequestHandler<GetMetadataQuery, PaginatedResult<MetadataDto>>
{
    private readonly IRepository<Domain.Entities.Metadata> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetMetadataQueryHandler(IRepository<Domain.Entities.Metadata> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<MetadataDto>> Handle(GetMetadataQuery request, CancellationToken cancellationToken)
    {
        var skip = (request.PageNumber - 1) * request.PageSize;
        var spec = new MetadataWithFiltersSpec(request.SearchTerm, request.DataTypeId, request.IsVisible, request.OrderBy, skip, request.PageSize);
        var countSpec = new MetadataCountSpec(request.SearchTerm, request.DataTypeId, request.IsVisible);

        var metadata = await _repository.ListAsync(spec, cancellationToken);
        var totalCount = await _repository.CountAsync(countSpec, cancellationToken);

        var metadataDtos = metadata.Select(m => new MetadataDto
        {
            Id = m.Id,
            Name = m.Name, // Updated: Name property maps to Name
            DataTypeId = m.DataTypeId,
            DataTypeName = m.DataType?.Name,
            ValidationPattern = m.ValidationPattern, // Updated property name
            MinLength = m.MinLength, // Updated property name
            MaxLength = m.MaxLength, // Updated property name
            MinValue = m.MinValue, // Updated property name
            MaxValue = m.MaxValue, // Updated property name
            IsRequired = m.IsRequired, // Updated property name
            Placeholder = m.Placeholder, // Updated property name
            DefaultOptions = m.DefaultOptions, // Updated property name
            MaxSelections = m.MaxSelections, // Updated property name
            AllowedFileTypes = m.AllowedFileTypes, // Updated property name
            MaxFileSize = m.MaxFileSize, // Updated property name
            ErrorMessage = m.ErrorMessage, // Updated property name
            DisplayLabel = m.DisplayLabel,
            HelpText = m.HelpText,
            FieldOrder = m.FieldOrder,
            IsVisible = m.IsVisible,
            IsReadonly = m.IsReadonly,
            CreatedAt = m.CreatedAt,
            CreatedBy = m.CreatedBy ?? Guid.Empty,
            ModifiedAt = m.ModifiedAt,
            ModifiedBy = m.ModifiedBy
        }).ToList();

        return new PaginatedResult<MetadataDto>(metadataDtos, request.PageNumber, request.PageSize, totalCount);
    }
}
