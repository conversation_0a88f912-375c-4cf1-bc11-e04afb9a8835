using Application.Templates.Commands;
using Application.Templates.DTOs;
using Application.Templates.Queries;
using Infrastructure.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Templates controller
/// </summary>
[Route("api/[controller]")]
public class TemplatesController : BaseApiController
{
    /// <summary>
    /// Get all templates with pagination and filtering
    /// </summary>
    [HttpGet]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<TemplateSummaryDto>>> GetTemplates([FromQuery] GetTemplatesQuery query)
    {
        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get template by ID
    /// </summary>
    [HttpGet("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<TemplateDto>>> GetTemplateById(Guid id)
    {
        return Ok(await Mediator.Send(new GetTemplateByIdQuery(id)));
    }

    /// <summary>
    /// Create a new template
    /// </summary>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<TemplateDto>>> CreateTemplate([FromBody] CreateTemplateDto dto)
    {
        var command = new CreateTemplateCommand
        {
            ProductId = dto.ProductId,
            Version = dto.Version,
            Stage = dto.Stage,
            TemplateJson = dto.TemplateJson,
            IsActive = dto.IsActive
        };

        var result = await Mediator.Send(command);

        if (result.Succeeded)
        {
            return CreatedAtAction(nameof(GetTemplateById), new { id = result.Data!.Id }, result);
        }

        return BadRequest(result);
    }

    /// <summary>
    /// Update an existing template
    /// </summary>
    [HttpPut("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<TemplateDto>>> UpdateTemplate(Guid id, [FromBody] UpdateTemplateDto dto)
    {
        var command = new UpdateTemplateCommand
        {
            Id = id,
            Version = dto.Version,
            Stage = dto.Stage,
            TemplateJson = dto.TemplateJson,
            IsActive = dto.IsActive
        };

        return Ok(await Mediator.Send(command));
    }

    /// <summary>
    /// Delete a template (soft delete)
    /// </summary>
    [HttpDelete("{id}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteTemplate(Guid id)
    {
        return Ok(await Mediator.Send(new DeleteTemplateCommand(id)));
    }

    /// <summary>
    /// Publish a template
    /// </summary>
    [HttpPost("{id}/publish")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<TemplateDto>>> PublishTemplate(Guid id)
    {
        return Ok(await Mediator.Send(new PublishTemplateCommand(id)));
    }

    /// <summary>
    /// Get templates by product ID
    /// </summary>
    [HttpGet("by-product/{productId}")]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<TemplateSummaryDto>>> GetTemplatesByProduct(
        Guid productId, 
        [FromQuery] int pageNumber = 1, 
        [FromQuery] int pageSize = 10,
        [FromQuery] string? stage = null,
        [FromQuery] bool? isActive = null)
    {
        var query = new GetTemplatesQuery
        {
            ProductId = productId,
            PageNumber = pageNumber,
            PageSize = pageSize,
            Stage = stage,
            IsActive = isActive
        };

        return Ok(await Mediator.Send(query));
    }

    /// <summary>
    /// Get templates by stage
    /// </summary>
    [HttpGet("by-stage/{stage}")]
    [TenantIdHeader]
    public async Task<ActionResult<PaginatedResult<TemplateSummaryDto>>> GetTemplatesByStage(
        string stage, 
        [FromQuery] int pageNumber = 1, 
        [FromQuery] int pageSize = 10,
        [FromQuery] Guid? productId = null,
        [FromQuery] bool? isActive = null)
    {
        var query = new GetTemplatesQuery
        {
            Stage = stage,
            PageNumber = pageNumber,
            PageSize = pageSize,
            ProductId = productId,
            IsActive = isActive
        };

        return Ok(await Mediator.Send(query));
    }
}
