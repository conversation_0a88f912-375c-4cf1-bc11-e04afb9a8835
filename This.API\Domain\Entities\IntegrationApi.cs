using System.ComponentModel.DataAnnotations;
using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Integration API entity - stores API endpoint definitions for integrations
/// </summary>
public class IntegrationApi : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Product ID this integration API belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// API name
    /// </summary>
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API endpoint URL
    /// </summary>
    [MaxLength(255)]
    public string EndpointUrl { get; set; } = string.Empty;

    /// <summary>
    /// API schema definition stored as JSON
    /// </summary>
    public string? Schema { get; set; }

    // Navigation properties
    /// <summary>
    /// Product this integration API belongs to
    /// </summary>
    public virtual Product Product { get; set; } = null!;

    /// <summary>
    /// Integration configurations using this API
    /// </summary>
    public virtual ICollection<IntegrationConfiguration> IntegrationConfigurations { get; set; } = new List<IntegrationConfiguration>();
}
