using Application.Templates.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Templates.Queries;

/// <summary>
/// Query to get templates with pagination and filtering
/// </summary>
public class GetTemplatesQuery : IRequest<PaginatedResult<TemplateSummaryDto>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Search term for template version or stage
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by product ID
    /// </summary>
    public Guid? ProductId { get; set; }

    /// <summary>
    /// Filter by stage
    /// </summary>
    public string? Stage { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Include deleted templates
    /// </summary>
    public bool IncludeDeleted { get; set; } = false;
}
