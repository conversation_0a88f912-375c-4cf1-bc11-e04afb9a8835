using Abstraction.Database.Repositories;
using Application.ComprehensiveEntityData.DTOs;
using Application.ObjectValues.DTOs;
using Application.ObjectValues.Interfaces;
using Application.ObjectValues.Specifications;
using Ardalis.Specification;
using Domain.Entities;
using Infrastructure.Database;
using Infrastructure.Database.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Infrastructure.Repositories;

/// <summary>
/// Repository implementation for ObjectValues operations
/// </summary>
public class ObjectValuesRepository : RepositoryBase<ObjectValue>, IObjectValuesRepository
{
    private readonly ILogger<ObjectValuesRepository> _logger;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly IRepository<ObjectMetadata> _objectMetadataRepository;
    private readonly IRepository<Metadata> _metadataRepository;
    private readonly IRepository<DataType> _dataTypeRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="context">Database context</param>
    /// <param name="logger">Logger</param>
    /// <param name="objectRepository">Object repository</param>
    /// <param name="objectMetadataRepository">ObjectMetadata repository</param>
    /// <param name="metadataRepository">Metadata repository</param>
    /// <param name="dataTypeRepository">DataType repository</param>
    public ObjectValuesRepository(
        ApplicationDbContext context,
        ILogger<ObjectValuesRepository> logger,
        IRepository<Domain.Entities.Object> objectRepository,
        IRepository<ObjectMetadata> objectMetadataRepository,
        IRepository<Metadata> metadataRepository,
        IRepository<DataType> dataTypeRepository)
        : base(context)
    {
        _logger = logger;
        _objectRepository = objectRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _metadataRepository = metadataRepository;
        _dataTypeRepository = dataTypeRepository;
    }

    /// <summary>
    /// Get ObjectValues by RefId with complete metadata information
    /// </summary>
    public async Task<ObjectValuesResponseDto?> GetByRefIdAsync(
        Guid refId, 
        string tenantId, 
        bool includeInactive = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting ObjectValues for RefId {RefId} in tenant {TenantId}", refId, tenantId);

            var spec = new ObjectValuesByRefIdSpec(refId, tenantId, includeInactive);
            var objectValues = await ListAsync(spec, cancellationToken);

            if (objectValues.Count == 0)
            {
                _logger.LogDebug("No ObjectValues found for RefId {RefId}", refId);
                return null;
            }

            var firstValue = objectValues.First();
            var objectEntity = firstValue.ObjectMetadata.Object;

            // Get all metadata for this object to include missing values
            var allMetadata = await GetObjectMetadataInternalAsync(
                objectEntity.Id,
                tenantId,
                !includeInactive,
                true,
                cancellationToken);

            // Create response DTO
            var response = new ObjectValuesResponseDto
            {
                RefId = refId,
                ObjectId = objectEntity.Id,
                ObjectName = objectEntity.Name ?? string.Empty,
                ObjectDescription = objectEntity.Description,
                ParentObjectId = objectEntity.ParentObjectId,
                ParentObjectName = objectEntity.ParentObject?.Name,
                TenantId = tenantId,
                IsActive = objectEntity.IsActive,
                Values = new Dictionary<string, ObjectValueDetailDto>()
            };

            // Map existing values
            var existingValues = objectValues.ToDictionary(
                ov => ov.ObjectMetadata.Metadata.Name,
                ov => new ObjectValueDetailDto
                {
                    Value = ov.Value,
                    MetadataId = ov.ObjectMetadata.MetadataId,
                    MetadataKey = ov.ObjectMetadata.Metadata.Name,
                    DisplayLabel = ov.ObjectMetadata.Metadata.DisplayLabel ?? string.Empty,
                    HelpText = ov.ObjectMetadata.Metadata.HelpText,
                    FieldOrder = ov.ObjectMetadata.Metadata.FieldOrder,
                    IsVisible = ov.ObjectMetadata.Metadata.IsVisible,
                    IsReadonly = ov.ObjectMetadata.Metadata.IsReadonly,
                    IsRequired = ov.ObjectMetadata.Metadata.DataType?.IsRequired ?? false,
                    IsUnique = ov.ObjectMetadata.IsUnique,
                    DataType = MapDataType(ov.ObjectMetadata.Metadata.DataType),
                    ObjectValueId = ov.Id
                });

            // Add all metadata (including missing values)
            foreach (var metadata in allMetadata)
            {
                if (existingValues.ContainsKey(metadata.MetadataKey))
                {
                    // Update with ObjectMetadata properties
                    existingValues[metadata.MetadataKey].IsUnique = metadata.IsUnique;
                    response.Values[metadata.MetadataKey] = existingValues[metadata.MetadataKey];
                }
                else
                {
                    // Add missing value
                    response.Values[metadata.MetadataKey] = metadata;
                }
            }

            _logger.LogDebug("Successfully retrieved ObjectValues for RefId {RefId} with {ValueCount} metadata fields", 
                refId, response.Values.Count);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ObjectValues for RefId {RefId}", refId);
            throw;
        }
    }

    /// <summary>
    /// Get optimized RefId list with high-performance LINQ operations
    /// </summary>
    public async Task<PaginatedResult<ObjectValuesRefIdSummaryDto>> GetOptimizedRefIdListAsync(
        Guid objectId,
        string tenantId,
        Guid? refId = null,
        string? searchTerm = null,
        bool onlyActive = true,
        int pageNumber = 1,
        int pageSize = 50,
        string? orderBy = null,
        string orderDirection = "desc",
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting optimized RefIds for ObjectId {ObjectId} in tenant {TenantId}, RefId filter: {RefId}",
                objectId, tenantId, refId);

            // Use optimized specification with proper indexing
            var spec = new OptimizedRefIdListSpec(objectId, tenantId, refId, searchTerm, onlyActive);
            var objectValues = await ListAsync(spec, cancellationToken);

            _logger.LogDebug("Found {Count} ObjectValue records for ObjectId {ObjectId}", objectValues.Count, objectId);

            // Optimized LINQ operations - single pass grouping and aggregation
            var groupedData = objectValues
                .Where(ov => ov.RefId.HasValue)
                .GroupBy(ov => ov.RefId!.Value)
                .Select(refIdGroup => new ObjectValuesRefIdSummaryDto
                {
                    RefId = refIdGroup.Key,
                    ObjectId = objectId,
                    ObjectName = refIdGroup.First().ObjectMetadata.Object.Name ?? string.Empty,
                    ValueCount = refIdGroup.Count(),
                    LastModified = refIdGroup.Max(ov => ov.CreatedAt),
                    IsComplete = true, // Calculated based on metadata count
                    MetaValues = refIdGroup.ToDictionary(
                        ov => ov.ObjectMetadata.Metadata.Name,
                        ov => ov.Value ?? string.Empty)
                })
                .ToList();

            _logger.LogDebug("Created {Count} RefId summaries using optimized LINQ", groupedData.Count);

            // Optimized ordering with LINQ expressions
            var orderedData = ApplyOptimizedOrdering(groupedData, orderBy, orderDirection);

            // Optimized pagination with single enumeration
            var totalCount = orderedData.Count();
            var items = orderedData
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            _logger.LogDebug("Successfully retrieved {Count} RefIds for ObjectId {ObjectId} using optimized operations",
                items.Count, objectId);

            return new PaginatedResult<ObjectValuesRefIdSummaryDto>(
                items,
                pageNumber,
                pageSize,
                totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting optimized RefIds for ObjectId {ObjectId}", objectId);
            throw;
        }
    }

    /// <summary>
    /// Apply optimized ordering using LINQ expressions
    /// </summary>
    private static IEnumerable<ObjectValuesRefIdSummaryDto> ApplyOptimizedOrdering(
        IEnumerable<ObjectValuesRefIdSummaryDto> data,
        string? orderBy,
        string orderDirection)
    {
        var isDescending = orderDirection.Equals("desc", StringComparison.OrdinalIgnoreCase);

        return orderBy?.ToLower() switch
        {
            "refid" => isDescending
                ? data.OrderByDescending(x => x.RefId)
                : data.OrderBy(x => x.RefId),
            "valuecount" => isDescending
                ? data.OrderByDescending(x => x.ValueCount)
                : data.OrderBy(x => x.ValueCount),
            "objectname" => isDescending
                ? data.OrderByDescending(x => x.ObjectName)
                : data.OrderBy(x => x.ObjectName),
            _ => isDescending // Default to LastModified
                ? data.OrderByDescending(x => x.LastModified)
                : data.OrderBy(x => x.LastModified)
        };
    }

    /// <summary>
    /// Get ObjectValues in dictionary format with optimized LINQ operations
    /// </summary>
    public async Task<Dictionary<string, ObjectValueDictionaryDto>?> GetObjectValuesDictionaryAsync(
        Guid objectId,
        Guid refId,
        string tenantId,
        bool includeInactive = false,
        bool onlyVisible = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting ObjectValues dictionary for ObjectId {ObjectId}, RefId {RefId} in tenant {TenantId}",
                objectId, refId, tenantId);

            // Use optimized specification for dictionary retrieval
            var spec = new ObjectValuesByRefIdSpec(refId, tenantId, includeInactive);
            var objectValues = await ListAsync(spec, cancellationToken);

            if (objectValues.Count == 0)
            {
                _logger.LogDebug("No ObjectValues found for RefId {RefId}", refId);
                return null;
            }

            // Validate that RefId belongs to the specified ObjectId
            var firstValue = objectValues.First();
            if (firstValue.ObjectMetadata.ObjectId != objectId)
            {
                _logger.LogWarning("RefId {RefId} does not belong to ObjectId {ObjectId}", refId, objectId);
                return null;
            }

            // Filter by visibility if requested
            var filteredValues = onlyVisible
                ? objectValues.Where(ov => ov.ObjectMetadata.Metadata.IsVisible).ToList()
                : objectValues;

            // Optimized LINQ operations - single pass dictionary creation
            var dictionary = filteredValues
                .Where(ov => !string.IsNullOrEmpty(ov.ObjectMetadata.Metadata.Name))
                .ToDictionary(
                    ov => ov.ObjectMetadata.Metadata.Name,
                    ov => new ObjectValueDictionaryDto
                    {
                        Id = ov.Id,
                        Value = ov.Value,
                        MetadataKey = ov.ObjectMetadata.Metadata.Name,
                        DisplayLabel = ov.ObjectMetadata.Metadata.DisplayLabel,
                        IsRequired = ov.ObjectMetadata.Metadata.DataType?.IsRequired ?? false,
                        DataType = ov.ObjectMetadata.Metadata.DataType?.Name
                    });

            _logger.LogDebug("Successfully created dictionary with {Count} ObjectValues for RefId {RefId}",
                dictionary.Count, refId);

            return dictionary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ObjectValues dictionary for ObjectId {ObjectId}, RefId {RefId}",
                objectId, refId);
            throw;
        }
    }

    /// <summary>
    /// Upsert ObjectValues for a specific RefId
    /// </summary>
    public async Task<UpsertObjectValuesResponseDto> UpsertAsync(
        UpsertObjectValuesRequestDto request,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);
        
        try
        {
            _logger.LogDebug("Upserting ObjectValues for RefId {RefId} and ObjectId {ObjectId}", 
                request.RefId, request.ObjectId);

            var response = new UpsertObjectValuesResponseDto();

            // Validate object exists
            var objectEntity = await GetObjectByIdAsync(request.ObjectId, tenantId, cancellationToken);
            if (objectEntity == null)
            {
                response.ValidationErrors.Add($"Object {request.ObjectId} not found");
                return response;
            }

            // Auto-create missing metadata if requested
            if (request.AutoCreateMetadata)
            {
                var metadataKeys = request.Values.Keys.ToList();
                response.MetadataCreatedCount = await EnsureMetadataExistsAsync(
                    request.ObjectId, 
                    metadataKeys, 
                    tenantId, 
                    userId, 
                    cancellationToken);
            }

            // Validate values if requested
            if (request.StrictValidation)
            {
                var validationErrors = await ValidateValuesAsync(
                    request.ObjectId, 
                    request.Values, 
                    tenantId, 
                    request.StrictValidation, 
                    cancellationToken);
                response.ValidationErrors.AddRange(validationErrors);
                
                if (response.ValidationErrors.Count > 0)
                {
                    await transaction.RollbackAsync(cancellationToken);
                    return response;
                }
            }

            // Get existing ObjectValues for this RefId
            var existingValuesSpec = new ObjectValuesByRefIdAndObjectIdSpec(request.RefId, request.ObjectId, tenantId);
            var existingValues = await ListAsync(existingValuesSpec, cancellationToken);

            // Get metadata for the object
            var metadataSpec = new ObjectMetadataByObjectIdSpec(request.ObjectId, true);
            var objectMetadataList = await _objectMetadataRepository.ListAsync(metadataSpec, cancellationToken);
            var metadata = objectMetadataList.ToDictionary(om => om.Metadata.Name, om => om.Metadata);

            // Process each value
            foreach (var kvp in request.Values)
            {
                if (!metadata.ContainsKey(kvp.Key))
                {
                    response.Warnings.Add($"Metadata '{kvp.Key}' not found for object");
                    continue;
                }

                var metadataEntity = metadata[kvp.Key];
                var existingValue = existingValues.FirstOrDefault(ev => ev.ObjectMetadata.MetadataId == metadataEntity.Id);

                if (existingValue != null)
                {
                    // Update existing value
                    existingValue.Value = kvp.Value;
                    existingValue.ModifiedAt = DateTime.UtcNow;
                    existingValue.ModifiedBy = Guid.Parse(userId);
                    response.UpdatedCount++;
                }
                else
                {
                    // Get the ObjectMetadata ID for this metadata
                    var objectMetadataSpec = new ObjectMetadataByObjectAndMetadataIdSpec(request.ObjectId, metadataEntity.Id);
                    var objectMetadata = await _objectMetadataRepository.FirstOrDefaultAsync(objectMetadataSpec, cancellationToken);

                    if (objectMetadata == null)
                    {
                        response.Warnings.Add($"ObjectMetadata not found for '{kvp.Key}'");
                        continue;
                    }

                    // Create new value
                    var newValue = new ObjectValue
                    {
                        Id = Guid.NewGuid(),
                        RefId = request.RefId,
                        ObjectMetadataId = objectMetadata.Id,
                        Value = kvp.Value,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Parse(userId)
                    };

                    await AddAsync(newValue, cancellationToken);
                    response.CreatedCount++;
                }
            }

            await SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            // Get updated ObjectValues
            var updatedObjectValues = await GetByRefIdAsync(
                request.RefId, 
                tenantId, 
                false, 
                cancellationToken);
                
            if (updatedObjectValues != null)
            {
                response.ObjectValues = updatedObjectValues;
            }

            _logger.LogDebug("Successfully upserted ObjectValues for RefId {RefId}: Created={Created}, Updated={Updated}", 
                request.RefId, response.CreatedCount, response.UpdatedCount);

            return response;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            _logger.LogError(ex, "Error upserting ObjectValues for RefId {RefId}", request.RefId);
            throw;
        }
    }

    /// <summary>
    /// Check if RefId exists for an ObjectId
    /// </summary>
    public async Task<bool> RefIdExistsAsync(
        Guid refId,
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default)
    {
        var spec = new RefIdExistsSpec(refId, objectId, tenantId);
        var result = await FirstOrDefaultAsync(spec, cancellationToken);
        return result != null;
    }

    /// <summary>
    /// Get Object information by ObjectId
    /// </summary>
    public async Task<Domain.Entities.Object?> GetObjectByIdAsync(
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default)
    {
        var spec = new ObjectByIdSpec(objectId);
        return await _objectRepository.FirstOrDefaultAsync(spec, cancellationToken);
    }

    /// <summary>
    /// Get all metadata for an ObjectId with data type information
    /// </summary>
    public async Task<List<ObjectValueDetailDto>> GetObjectMetadataAsync(
        Guid objectId,
        string tenantId,
        bool onlyActive = true,
        bool onlyVisible = true,
        CancellationToken cancellationToken = default)
    {
        return await GetObjectMetadataInternalAsync(objectId, tenantId, onlyActive, onlyVisible, cancellationToken);
    }

    /// <summary>
    /// Internal method to get all metadata for an ObjectId with data type information
    /// </summary>
    private async Task<List<ObjectValueDetailDto>> GetObjectMetadataInternalAsync(
        Guid objectId,
        string tenantId,
        bool onlyActive = true,
        bool onlyVisible = true,
        CancellationToken cancellationToken = default)
    {
        var spec = new ObjectMetadataByObjectIdSpec(objectId, onlyActive);
        var objectMetadata = await _objectMetadataRepository.ListAsync(spec, cancellationToken);

        // Filter by visibility if needed
        if (onlyVisible)
        {
            objectMetadata = objectMetadata.Where(om => om.Metadata.IsVisible).ToList();
        }

        return objectMetadata.Select(om => new ObjectValueDetailDto
        {
            Value = null, // No value yet
            MetadataId = om.MetadataId,
            MetadataKey = om.Metadata.Name,
            DisplayLabel = om.Metadata.DisplayLabel ?? string.Empty,
            HelpText = om.Metadata.HelpText,
            FieldOrder = om.Metadata.FieldOrder,
            IsVisible = om.Metadata.IsVisible,
            IsReadonly = om.Metadata.IsReadonly,
            IsRequired = om.Metadata.DataType?.IsRequired ?? false,
            IsUnique = om.IsUnique,
            DataType = MapDataType(om.Metadata.DataType),
            ObjectValueId = null
        }).ToList();
    }

    /// <summary>
    /// Validate values against metadata constraints
    /// </summary>
    public async Task<List<string>> ValidateValuesAsync(
        Guid objectId,
        Dictionary<string, string> values,
        string tenantId,
        bool strictValidation = true,
        CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();

        try
        {
            // Get metadata for validation
            var metadata = await GetObjectMetadataInternalAsync(objectId, tenantId, true, false, cancellationToken);
            var metadataDict = metadata.ToDictionary(m => m.MetadataKey, m => m);

            foreach (var kvp in values)
            {
                if (!metadataDict.ContainsKey(kvp.Key))
                {
                    if (strictValidation)
                    {
                        errors.Add($"Metadata '{kvp.Key}' not found for object");
                    }
                    continue;
                }

                var meta = metadataDict[kvp.Key];
                var value = kvp.Value;

                // Required field validation
                if (meta.IsRequired && string.IsNullOrEmpty(value))
                {
                    errors.Add($"Field '{meta.DisplayLabel}' is required");
                    continue;
                }

                // Skip further validation if value is empty and not required
                if (string.IsNullOrEmpty(value))
                    continue;

                // Data type validation
                if (strictValidation && meta.DataType != null)
                {
                    var dataTypeErrors = ValidateDataType(value, meta.DataType, meta.DisplayLabel);
                    errors.AddRange(dataTypeErrors);
                }

                // Unique constraint validation
                if (meta.IsUnique)
                {
                    var duplicateSpec = new ObjectValueDuplicateCheckSpec(objectId, meta.MetadataId, value);
                    var isDuplicate = await FirstOrDefaultAsync(duplicateSpec, cancellationToken) != null;

                    if (isDuplicate)
                    {
                        errors.Add($"Value '{value}' for field '{meta.DisplayLabel}' must be unique");
                    }
                }
            }

            return errors;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating values for ObjectId {ObjectId}", objectId);
            errors.Add("Validation error occurred");
            return errors;
        }
    }

    /// <summary>
    /// Auto-create missing metadata for new keys
    /// </summary>
    public async Task<int> EnsureMetadataExistsAsync(
        Guid objectId,
        List<string> metadataKeys,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default)
    {
        var createdCount = 0;

        try
        {
            // Get existing metadata
            var existingMetadataSpec = new ObjectMetadataByObjectIdSpec(objectId, false);
            var existingObjectMetadata = await _objectMetadataRepository.ListAsync(existingMetadataSpec, cancellationToken);
            var existingMetadata = existingObjectMetadata.Select(om => om.Metadata.Name).ToList();

            var missingKeys = metadataKeys.Except(existingMetadata).ToList();

            foreach (var key in missingKeys)
            {
                // Check if global metadata exists
                var metadataSpec = new MetadataByKeySpec(key);
                var globalMetadata = await _metadataRepository.FirstOrDefaultAsync(metadataSpec, cancellationToken);

                if (globalMetadata == null)
                {
                    // Create new metadata with default text data type
                    var dataTypeSpec = new DataTypeByNameSpec("text");
                    var defaultDataType = await _dataTypeRepository.FirstOrDefaultAsync(dataTypeSpec, cancellationToken);

                    if (defaultDataType == null)
                    {
                        _logger.LogWarning("Default text data type not found, skipping metadata creation for key {Key}", key);
                        continue;
                    }

                    globalMetadata = new Metadata
                    {
                        Id = Guid.NewGuid(),
                        Name = key,
                        DisplayLabel = key,
                        DataTypeId = defaultDataType.Id,
                        IsVisible = true,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.Parse(userId)
                    };

                    await _metadataRepository.AddAsync(globalMetadata, cancellationToken);
                }

                // Create ObjectMetadata link
                var objectMetadata = new ObjectMetadata
                {
                    Id = Guid.NewGuid(),
                    ObjectId = objectId,
                    MetadataId = globalMetadata.Id,
                    IsUnique = false,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Parse(userId)
                };

                await _objectMetadataRepository.AddAsync(objectMetadata, cancellationToken);
                createdCount++;
            }

            if (createdCount > 0)
            {
                await SaveChangesAsync(cancellationToken);
            }

            return createdCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring metadata exists for ObjectId {ObjectId}", objectId);
            throw;
        }
    }

    /// <summary>
    /// Delete ObjectValues by RefId
    /// </summary>
    public async Task<int> DeleteByRefIdAsync(
        Guid refId,
        string tenantId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var spec = new ObjectValuesByRefIdSpec(refId, tenantId, true);
            var objectValues = await ListAsync(spec, cancellationToken);

            if (objectValues.Count == 0)
                return 0;

            await DeleteRangeAsync(objectValues, cancellationToken);
            await SaveChangesAsync(cancellationToken);

            return objectValues.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting ObjectValues for RefId {RefId}", refId);
            throw;
        }
    }

    /// <summary>
    /// Get ObjectValues statistics for an ObjectId
    /// </summary>
    public async Task<ObjectValuesStatisticsDto> GetStatisticsAsync(
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var spec = new OptimizedRefIdListSpec(objectId, tenantId);
            var objectValues = await ListAsync(spec, cancellationToken);

            var metadataCountSpec = new ObjectMetadataCountSpec(objectId, true);
            var metadataCount = await _objectMetadataRepository.CountAsync(metadataCountSpec, cancellationToken);

            var refIdGroups = objectValues.GroupBy(ov => ov.RefId).ToList();
            var completeRefIds = refIdGroups.Count(g => g.Count() == metadataCount);

            return new ObjectValuesStatisticsDto
            {
                ObjectId = objectId,
                TotalRefIds = refIdGroups.Count,
                TotalValues = objectValues.Count,
                MetadataFieldCount = metadataCount,
                CompleteRefIds = completeRefIds,
                AverageCompletionPercentage = metadataCount > 0
                    ? (decimal)refIdGroups.Average(g => (double)g.Count() / metadataCount * 100)
                    : 0,
                LastModified = objectValues.Count > 0
                    ? objectValues.Max(ov => ov.CreatedAt)
                    : null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting statistics for ObjectId {ObjectId}", objectId);
            throw;
        }
    }



    /// <summary>
    /// Map DataType entity to DTO
    /// </summary>
    private static DataTypeInfoDto MapDataType(DataType? dataType)
    {
        if (dataType == null)
        {
            return new DataTypeInfoDto
            {
                Id = Guid.Empty,
                Name = "text",
                DisplayName = "Text",
                Category = "string",
                UiComponent = "TextInput",
                InputType = "text"
            };
        }

        return new DataTypeInfoDto
        {
            Id = dataType.Id,
            Name = dataType.Name,
            DisplayName = dataType.DisplayName,
            Category = dataType.Category,
            UiComponent = dataType.UiComponent,
            InputType = dataType.InputType,
            InputMask = dataType.InputMask,
            Placeholder = dataType.Placeholder,
            DefaultOptions = dataType.DefaultOptions,
            ValidationPattern = dataType.ValidationPattern,
            MinLength = dataType.MinLength,
            MaxLength = dataType.MaxLength,
            MinValue = dataType.MinValue,
            MaxValue = dataType.MaxValue,
            DecimalPlaces = dataType.DecimalPlaces,
            StepValue = dataType.StepValue,
            IsRequired = dataType.IsRequired,
            AllowsMultiple = dataType.AllowsMultiple,
            AllowsCustomOptions = dataType.AllowsCustomOptions,
            HtmlAttributes = dataType.HtmlAttributes
        };
    }

    /// <summary>
    /// Validate value against data type constraints
    /// </summary>
    private static List<string> ValidateDataType(string value, DataTypeInfoDto dataType, string fieldLabel)
    {
        var errors = new List<string>();

        if (string.IsNullOrEmpty(value))
            return errors;

        // Length validation
        if (dataType.MinLength.HasValue && value.Length < dataType.MinLength.Value)
        {
            errors.Add($"Field '{fieldLabel}' must be at least {dataType.MinLength.Value} characters long");
        }

        if (dataType.MaxLength.HasValue && value.Length > dataType.MaxLength.Value)
        {
            errors.Add($"Field '{fieldLabel}' cannot exceed {dataType.MaxLength.Value} characters");
        }

        // Pattern validation
        if (!string.IsNullOrEmpty(dataType.ValidationPattern))
        {
            try
            {
                if (!System.Text.RegularExpressions.Regex.IsMatch(value, dataType.ValidationPattern))
                {
                    errors.Add($"Field '{fieldLabel}' format is invalid");
                }
            }
            catch
            {
                // Invalid regex pattern, skip validation
            }
        }

        // Type-specific validation
        switch (dataType.Name?.ToLower())
        {
            case "number":
            case "integer":
                if (!int.TryParse(value, out var intValue))
                {
                    errors.Add($"Field '{fieldLabel}' must be a valid number");
                }
                else
                {
                    if (dataType.MinValue.HasValue && intValue < dataType.MinValue.Value)
                    {
                        errors.Add($"Field '{fieldLabel}' must be at least {dataType.MinValue.Value}");
                    }
                    if (dataType.MaxValue.HasValue && intValue > dataType.MaxValue.Value)
                    {
                        errors.Add($"Field '{fieldLabel}' cannot exceed {dataType.MaxValue.Value}");
                    }
                }
                break;

            case "decimal":
                if (!decimal.TryParse(value, out var decimalValue))
                {
                    errors.Add($"Field '{fieldLabel}' must be a valid decimal number");
                }
                else
                {
                    if (dataType.MinValue.HasValue && decimalValue < dataType.MinValue.Value)
                    {
                        errors.Add($"Field '{fieldLabel}' must be at least {dataType.MinValue.Value}");
                    }
                    if (dataType.MaxValue.HasValue && decimalValue > dataType.MaxValue.Value)
                    {
                        errors.Add($"Field '{fieldLabel}' cannot exceed {dataType.MaxValue.Value}");
                    }
                }
                break;

            case "email":
                if (!IsValidEmail(value))
                {
                    errors.Add($"Field '{fieldLabel}' must be a valid email address");
                }
                break;

            case "boolean":
                if (!bool.TryParse(value, out _) && value != "0" && value != "1")
                {
                    errors.Add($"Field '{fieldLabel}' must be a valid boolean value (true/false)");
                }
                break;

            case "date":
            case "datetime":
                if (!DateTime.TryParse(value, out _))
                {
                    errors.Add($"Field '{fieldLabel}' must be a valid date");
                }
                break;
        }

        return errors;
    }

    /// <summary>
    /// Validate email format
    /// </summary>
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
