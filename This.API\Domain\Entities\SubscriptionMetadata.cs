using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links Subscription types to their metadata with IsUnique support
/// </summary>
public class SubscriptionMetadata : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Subscription ID
    /// </summary>
    public Guid SubscriptionId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the subscription
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInList { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInEdit { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInCreate { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInView { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsCalculated { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Subscription
    /// </summary>
    public virtual Subscription Subscription { get; set; } = null!;

    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// Subscription values
    /// </summary>
    public virtual ICollection<SubscriptionValue> SubscriptionValues { get; set; } = new List<SubscriptionValue>();
}
