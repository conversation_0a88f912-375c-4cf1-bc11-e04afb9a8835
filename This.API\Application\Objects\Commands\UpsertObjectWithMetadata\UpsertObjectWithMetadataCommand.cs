using Abstraction.Common;
using MediatR;
using Shared.Common.Response;

namespace Application.Objects.Commands.UpsertObjectWithMetadata;

/// <summary>
/// Command to upsert object with metadata in flat structure
/// </summary>
public class UpsertObjectWithMetadataCommand : IRequest<Result<UpsertObjectWithMetadataResponse>>
{
    /// <summary>
    /// All properties including ObjectId, RefId, ParentObjectValueId and metadata properties
    /// ObjectId: Required - identifies the target object
    /// RefId: Required - groups all metadata values for this object instance  
    /// ParentObjectValueId: Optional - for hierarchical relationships
    /// Other properties: Metadata where key is Name and value is the metadata value
    /// </summary>
    public Dictionary<string, object?> MetadataProperties { get; set; } = new();
}

/// <summary>
/// Response for upsert object with metadata command
/// </summary>
public class UpsertObjectWithMetadataResponse
{
    /// <summary>
    /// Object ID that was processed
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// RefId for the object instance
    /// </summary>
    public Guid RefId { get; set; }

    /// <summary>
    /// Parent object value ID
    /// </summary>
    public Guid? ParentObjectValueId { get; set; }

    /// <summary>
    /// Total number of metadata properties processed
    /// </summary>
    public int TotalProcessed { get; set; }

    /// <summary>
    /// Number of ObjectValues inserted
    /// </summary>
    public int InsertedCount { get; set; }

    /// <summary>
    /// Number of ObjectValues updated
    /// </summary>
    public int UpdatedCount { get; set; }

    /// <summary>
    /// Number of metadata properties that were skipped (metadata not found)
    /// </summary>
    public int SkippedCount { get; set; }

    /// <summary>
    /// List of processed object values
    /// </summary>
    public List<ObjectValueResponseData> ObjectValues { get; set; } = new();

    /// <summary>
    /// List of skipped properties with reasons
    /// </summary>
    public List<SkippedPropertyInfo> SkippedProperties { get; set; } = new();
}

/// <summary>
/// Information about skipped properties
/// </summary>
public class SkippedPropertyInfo
{
    /// <summary>
    /// Property name (Name)
    /// </summary>
    public string PropertyName { get; set; } = string.Empty;

    /// <summary>
    /// Reason for skipping
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// Object value response data
/// </summary>
public class ObjectValueResponseData
{
    public Guid Id { get; set; }
    public Guid ObjectMetadataId { get; set; }
    public Guid RefId { get; set; }
    public Guid? ParentObjectValueId { get; set; }
    public string? Value { get; set; }
    public bool? IsRefId { get; set; }
    public bool? IsUserId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime ModifiedAt { get; set; }
    public bool WasInserted { get; set; }
    public string MetadataKey { get; set; } = string.Empty;
}
