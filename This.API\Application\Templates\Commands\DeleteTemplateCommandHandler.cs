using Abstraction.Database.Repositories;
using Application.Templates.Specifications;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Templates.Commands;

/// <summary>
/// Handler for DeleteTemplateCommand
/// </summary>
public class DeleteTemplateCommandHandler : IRequestHandler<DeleteTemplateCommand, Result<bool>>
{
    private readonly IRepository<Template> _templateRepository;
    private readonly ILogger<DeleteTemplateCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteTemplateCommandHandler(
        IRepository<Template> templateRepository,
        ILogger<DeleteTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteTemplateCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Deleting template {TemplateId}", request.Id);

            // Get template using specification
            var templateSpec = new TemplateByIdSpec(request.Id);
            var template = await _templateRepository.GetBySpecAsync(templateSpec, cancellationToken);

            if (template == null)
            {
                _logger.LogWarning("Template not found: {TemplateId}", request.Id);
                return Result<bool>.Failure("Template not found");
            }

            // Soft delete
            template.IsDeleted = true;
            template.IsActive = false;
            template.ModifiedAt = DateTime.UtcNow;

            await _templateRepository.UpdateAsync(template, cancellationToken);

            _logger.LogInformation("Successfully deleted template {TemplateId}", request.Id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting template {TemplateId}", request.Id);
            return Result<bool>.Failure("Failed to delete template");
        }
    }
}
