using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Commands.UpdateSingleValueByRefId;

/// <summary>
/// Command to update a single ObjectValue by RefId and Name
/// </summary>
public class UpdateObjectValueByRefIdCommand : IRequest<Result<UpdateObjectValueByRefIdResponse>>
{
    /// <summary>
    /// RefId to identify the instance
    /// </summary>
    public Guid RefId { get; set; }

    /// <summary>
    /// Name to identify the metadata field
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// New value
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Tenant ID for filtering
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateObjectValueByRefIdCommand(Guid refId, string metadataKey, string value, string tenantId)
    {
        RefId = refId;
        MetadataKey = metadataKey;
        Value = value;
        TenantId = tenantId;
    }
}

/// <summary>
/// Response for UpdateObjectValueByRefIdCommand
/// </summary>
public class UpdateObjectValueByRefIdResponse
{
    /// <summary>
    /// ObjectValue ID that was updated
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// RefId of the instance
    /// </summary>
    public Guid RefId { get; set; }

    /// <summary>
    /// Name of the metadata field
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// Updated value
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Modified timestamp
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Whether the record was found and updated
    /// </summary>
    public bool Found { get; set; }

    /// <summary>
    /// Success message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}
