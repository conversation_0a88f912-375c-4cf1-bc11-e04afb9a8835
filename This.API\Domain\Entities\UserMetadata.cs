using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links User types to their metadata with IsUnique support
/// </summary>
public class UserMetadata : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// User ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the user
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInList { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInEdit { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInCreate { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInView { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsCalculated { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// User
    /// </summary>
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// User values
    /// </summary>
    public virtual ICollection<UserValue> UserValues { get; set; } = new List<UserValue>();
}
