using Application.Templates.DTOs;
using MediatR;
using Shared.Common.Response;
using System.Text.Json;

namespace Application.Templates.Commands;

/// <summary>
/// Command to update an existing template
/// </summary>
public class UpdateTemplateCommand : IRequest<Result<TemplateDto>>
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Template version
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// Template stage (draft, live, beta, etc.)
    /// </summary>
    public string? Stage { get; set; }

    /// <summary>
    /// Template JSON content
    /// </summary>
    public JsonElement? TemplateJson { get; set; }

    /// <summary>
    /// Whether the template is active
    /// </summary>
    public bool? IsActive { get; set; }
}
