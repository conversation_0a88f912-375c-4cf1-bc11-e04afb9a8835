import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// A customizable day input widget following the 'this_componentName_relatedTo' naming convention
/// This widget handles day input with validation and month-aware day limits
class ThisDayInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final int? value; // 1-31
  final ValueChanged<int?> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final int? month; // 1-12, used for day validation
  final int? year; // Used for leap year calculation
  final bool showDropdown;
  final List<int>? allowedDays;
  final String? Function(int?)? customValidation;

  const ThisDayInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.month,
    this.year,
    this.showDropdown = false,
    this.allowedDays,
    this.customValidation,
  });

  @override
  State<ThisDayInput> createState() => _ThisDayInputState();
}

class _ThisDayInputState extends State<ThisDayInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value?.toString() ?? '');
    _focusNode = FocusNode();
    _validateValue(widget.value);
  }

  @override
  void didUpdateWidget(ThisDayInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value?.toString() ?? '';
    }
    // Re-validate if month or year changed
    if (oldWidget.month != widget.month || oldWidget.year != widget.year) {
      _validateValue(widget.value);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  int _getDaysInMonth(int month, int year) {
    switch (month) {
      case 1:
      case 3:
      case 5:
      case 7:
      case 8:
      case 10:
      case 12:
        return 31;
      case 4:
      case 6:
      case 9:
      case 11:
        return 30;
      case 2:
        return _isLeapYear(year) ? 29 : 28;
      default:
        return 31;
    }
  }

  bool _isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }

  int get _maxDayForMonth {
    if (widget.month != null && widget.year != null) {
      return _getDaysInMonth(widget.month!, widget.year!);
    } else if (widget.month != null) {
      return _getDaysInMonth(widget.month!, DateTime.now().year);
    }
    return 31; // Default max
  }

  List<int> _getAvailableDays() {
    final maxDay = _maxDayForMonth;
    final days = <int>[];

    for (int day = 1; day <= maxDay; day++) {
      if (widget.allowedDays == null || widget.allowedDays!.contains(day)) {
        days.add(day);
      }
    }

    return days;
  }

  List<String> _validateValue(int? value) {
    final errors = <String>[];

    // Required validation
    if (widget.required && value == null) {
      errors.add('${widget.label} is required');
      return errors;
    }

    if (value != null) {
      // Basic range validation
      if (value < 1 || value > 31) {
        errors.add('Day must be between 1 and 31');
      } else {
        // Month-specific validation
        final maxDay = _maxDayForMonth;
        if (value > maxDay) {
          if (widget.month != null) {
            final monthNames = ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
            errors.add('${monthNames[widget.month!]} has only $maxDay days');
          } else {
            errors.add('Day must be $maxDay or less');
          }
        }
      }

      // Allowed days validation
      if (widget.allowedDays != null && !widget.allowedDays!.contains(value)) {
        errors.add('This day is not allowed');
      }

      // Custom validation
      if (widget.customValidation != null) {
        final customError = widget.customValidation!(value);
        if (customError != null) {
          errors.add(customError);
        }
      }
    }

    return errors;
  }

  void _handleChange(String text) {
    int? newValue;
    if (text.isNotEmpty) {
      newValue = int.tryParse(text);
    }

    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  void _handleDropdownChange(int? newValue) {
    _controller.text = newValue?.toString() ?? '';
    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.white,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Input Field or Dropdown
        if (widget.showDropdown)
          DropdownButtonFormField<int>(
            value: widget.value,
            onChanged: widget.disabled || widget.readOnly ? null : _handleDropdownChange,
            decoration: InputDecoration(
              hintText: widget.placeholder ?? 'Select day',
              hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
              errorText: hasErrors ? _errors.first : null,
              errorStyle: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
              suffixIcon: const Icon(Icons.today, size: 20),
            ),
            style: LexendTextStyles.lexend14Regular.copyWith(
              color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.white,
            ),
            dropdownColor: ColorPalette.darkToneInk,
            items: _getAvailableDays().map((day) {
              return DropdownMenuItem<int>(
                value: day,
                child: Text(
                  day.toString().padLeft(2, '0'),
                  style: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.white,
                  ),
                ),
              );
            }).toList(),
          )
        else
          TextFormField(
            controller: _controller,
            focusNode: _focusNode,
            enabled: !widget.disabled,
            readOnly: widget.readOnly,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(2),
              _DayInputFormatter(_maxDayForMonth),
            ],
            onChanged: _handleChange,
            decoration: InputDecoration(
              hintText: widget.placeholder ?? 'DD',
              hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
              errorText: hasErrors ? _errors.first : null,
              errorStyle: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
              suffixIcon: const Icon(Icons.today, size: 20),
            ),
            style: LexendTextStyles.lexend14Regular.copyWith(
              color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.white,
            ),
          ),

        // Helper text for day range
        if (!hasErrors && widget.month != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Valid range: 1 - $_maxDayForMonth',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          ),
      ],
    );
  }
}

/// Custom input formatter for day values
class _DayInputFormatter extends TextInputFormatter {
  final int maxDay;

  _DayInputFormatter(this.maxDay);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;
    if (text.isEmpty) return newValue;

    final value = int.tryParse(text);
    if (value == null) return oldValue;

    // Limit to valid day range
    if (value > maxDay) {
      return oldValue;
    }

    return newValue;
  }
}
