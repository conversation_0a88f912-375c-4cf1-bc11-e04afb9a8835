{"succeeded": true, "data": {"products": [{"id": "29d658dc-cdc7-4859-b950-f5f69e445ac2", "name": "Inventory Management", "description": "Comprehensive inventory management system for residential properties", "version": "1.0.0", "isActive": true, "metadata": [], "rootObjects": [{"id": "033f0a76-8f1d-4537-8045-54b08e621014", "name": "Organization", "description": "Organization details", "parentObjectId": null, "isActive": true, "hierarchyLevel": 0, "hierarchyPath": "Organization", "metadata": [{"metadata": {"id": "4e0e79e5-7794-4f88-aaf1-56328be8051a", "metadataKey": "OrganizationEmail", "displayLabel": "OrganizationEmail", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5c76cf92-55c3-4ceb-a6d7-63d59a3838fd", "name": "email", "displayName": "Email", "category": "Input", "uiComponent": "EmailInput", "validationPattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "minLength": 5, "maxLength": 320, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "email", "inputMask": null, "placeholder": "Enter email address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "2fde43b6-512f-4c87-87c0-68a7dcaaebf2", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "9b66dcd0-2b7b-4494-ae0a-061be857bda2", "metadataKey": "OrganizationWebsite", "displayLabel": "OrganizationWebsite", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "7470458d-5752-45c7-a691-317d03f2414c", "name": "url", "displayName": "URL", "category": "Input", "uiComponent": "UrlInput", "validationPattern": "^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$", "minLength": 10, "maxLength": 2083, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "url", "inputMask": null, "placeholder": "Enter URL...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "40b77520-2361-4dd7-b731-78be469addb9", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "2863c1d3-fb7b-4376-b46a-6a52e311a15e", "metadataKey": "OrganizationLatitude", "displayLabel": "OrganizationLatitude", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "537e2604-9e9e-43df-80ea-3a15cde9155f", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "64d6b068-592f-4fdc-90b7-f304a2f900c2", "metadataKey": "OrganizationAddress", "displayLabel": "OrganizationAddress", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dce2def4-bcdd-443d-85b7-fe906750a77e", "name": "address", "displayName": "Address", "category": "Input", "uiComponent": "AddressInput", "validationPattern": null, "minLength": 10, "maxLength": 500, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "60455e01-d558-461b-b039-2adf09a26412", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "2bd70a69-7a70-42a6-ac34-7ced165ea8bb", "metadataKey": "OrganizationName", "displayLabel": "OrganizationName", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "854db5a0-bfd1-40e9-a7cb-b5b09875485f", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "04f19c61-6e3f-42bc-9742-9a8125a3b23e", "metadataKey": "OrganizationLongitude", "displayLabel": "OrganizationLongitude", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "93fc248a-7c05-4bc5-9cfe-539a986287e2", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "151752dc-4df6-48e1-b390-3333f6702b22", "metadataKey": "OrganizationLogo", "displayLabel": "OrganizationLogo", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "d9356e89-7195-4bfc-8f37-0ce892ef9614", "name": "image", "displayName": "Image", "category": "Upload", "uiComponent": "ImageUpload", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "file", "inputMask": null, "placeholder": "Choose image...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "a51ec83a-8a73-4c84-9fd9-9add016d2a1b", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "cc998199-8131-4eba-aa0b-21591b0844b8", "metadataKey": "OrganizationDescription", "displayLabel": "OrganizationDescription", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "56da8d60-3396-4387-b275-c03f7f9568c0", "name": "textarea", "displayName": "Text Area", "category": "Input", "uiComponent": "TextArea", "validationPattern": null, "minLength": 10, "maxLength": 5000, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "textarea", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "bc82f7a0-cb99-43fd-bd66-9782041766f6", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "267bacd9-87d0-40da-bfbb-3d7e2d6e394e", "metadataKey": "OrganizationPhone", "displayLabel": "OrganizationPhone", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "314e3b05-23df-4142-a7a4-1c87d498a425", "name": "phone", "displayName": "Phone", "category": "Input", "uiComponent": "PhoneInput", "validationPattern": "^[\\+]?[1-9][\\d]{0,15}$", "minLength": 10, "maxLength": 15, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "tel", "inputMask": null, "placeholder": "Enter phone number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "f6d976c0-358b-47ff-8b9e-fb7ed278f018", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": [{"id": "8f4e1201-1d80-4f4b-8dc8-a190be0831bb", "name": "Building", "description": "Building details", "parentObjectId": "033f0a76-8f1d-4537-8045-54b08e621014", "isActive": true, "hierarchyLevel": 1, "hierarchyPath": "Organization > Building", "metadata": [{"metadata": {"id": "bb131edd-038f-40cf-a9a8-d94f6d003db5", "metadataKey": "Facade", "displayLabel": "Facade", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "1a16388c-aa8a-4327-affc-145af28888a0", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "6f1042f8-4578-420d-9697-03ff98d7e9d2", "metadataKey": "ElevatorType", "displayLabel": "ElevatorType", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "24bf6aa9-8786-4d70-a6bf-a44fd2b71e55", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "9efa1efe-8efc-4785-a6a4-49da54778fea", "metadataKey": "BuildingName", "displayLabel": "BuildingName", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "8774c1b3-af6e-429f-bc14-f8f7340d63b2", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "0a0f77f3-3560-45cd-b80a-c62377e7973d", "metadataKey": "ParkingType", "displayLabel": "ParkingType", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "8e27ec6a-6be7-4272-8a7d-07598c64b912", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "74bdfd5c-c022-4f07-a6b7-7ebdfcbaeff4", "metadataKey": "DeliveryDate", "displayLabel": "DeliveryDate", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "52e41f83-eefa-4908-a62f-7eb15a53c588", "name": "date", "displayName": "Date", "category": "Input", "uiComponent": "DatePicker", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "date", "inputMask": null, "placeholder": "Select date...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "a592f625-66d5-44e1-b9e5-0636483ae2cb", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "b98f43f0-4cc6-43d8-b08b-a5f86d0b0d18", "metadataKey": "NumberOfFloors", "displayLabel": "NumberOfFloors", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "b74cf485-3402-4fe5-b9ae-04d583fee88a", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": [{"id": "1bee1216-4bf1-4527-88b1-ee644c788814", "name": "Floor", "description": "Floor details", "parentObjectId": "8f4e1201-1d80-4f4b-8dc8-a190be0831bb", "isActive": true, "hierarchyLevel": 2, "hierarchyPath": "Organization > Building > Floor", "metadata": [{"metadata": {"id": "7c401921-343e-44c9-8f26-334a11725002", "metadataKey": "FloorPlan", "displayLabel": "FloorPlan", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "313ea1b3-8169-42f1-9200-b0e682e5db1e", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "8e59cb13-3aeb-480c-95ef-74dbf9d6c096", "metadataKey": "FloorNumber", "displayLabel": "FloorNumber", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "36010b38-eb14-4633-ac9a-e23599d66b36", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "0070704a-a6f5-4b31-80ec-4aa4627862ce", "metadataKey": "NumberOfUnits", "displayLabel": "NumberOfUnits", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "62d67868-a6a6-43a0-9a17-68ecf8369928", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": [{"id": "60f06a2d-ab5d-4d37-8840-1216ed65cd28", "name": "Unit", "description": "Unit details", "parentObjectId": "1bee1216-4bf1-4527-88b1-ee644c788814", "isActive": true, "hierarchyLevel": 3, "hierarchyPath": "Organization > Building > Floor > Unit", "metadata": [{"metadata": {"id": "525f6c68-847e-4f03-bb91-e5f06569a1bc", "metadataKey": "SoldOut", "displayLabel": "SoldOut", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "3f400b16-a4f9-4860-8b8a-4d189882f03a", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "1bf0ab4d-97f6-4dfc-9cca-1d07ec9a4055", "metadataKey": "FinishingType", "displayLabel": "FinishingType", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "566ab29c-9d54-44ec-a441-3d682ace5ce1", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "a1b17a19-4959-4b59-99bc-bbfea2a13590", "metadataKey": "BedroomCount", "displayLabel": "BedroomCount", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "768b31de-8ae4-4a6c-a25a-462914f97926", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "316a99d7-e8c5-42fe-83a7-446645f5f270", "metadataKey": "AreaTotal", "displayLabel": "AreaTotal", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "7c8142ab-ed18-4dbd-97a4-16f1c157ee07", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "01c40625-37d5-40ef-b524-2a284c1bc733", "metadataKey": "AreaInternal", "displayLabel": "AreaInternal", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "7f3ef9c1-543d-475f-ad8f-54de379bc8aa", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "7c401921-343e-44c9-8f26-334a11725002", "metadataKey": "FloorPlan", "displayLabel": "FloorPlan", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "7fc64ac9-faa1-4225-add1-d2174193c4c7", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "e4bf890d-f7a4-4115-ae7b-1b5ca7adc3d8", "metadataKey": "Images", "displayLabel": "Images", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5542683d-af98-4d34-8dae-76cc5aae8df0", "name": "file", "displayName": "File", "category": "Upload", "uiComponent": "FileUpload", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "file", "inputMask": null, "placeholder": "Choose file...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "9eff9b9e-5909-4a8b-8b57-362d6533ee3a", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "6de2267a-bc0d-4d8e-99e7-e34e407b687c", "metadataKey": "ToiletCount", "displayLabel": "ToiletCount", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "a9ae8c14-d3c7-4458-a892-3af00d44aac3", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "34a775da-0aa9-43e9-b8ad-ac1226bc5353", "metadataKey": "Price100Percent", "displayLabel": "Price100Percent", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "e533c604-dae3-4ea7-a7f0-ec8b150c8174", "name": "percentage", "displayName": "Percentage", "category": "Input", "uiComponent": "PercentageInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": 100, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter percentage...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "baf1387b-3f25-487e-80a9-1d18efd5e775", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "ed851143-2bb1-4fd8-bc88-981390a79137", "metadataKey": "PriceSelling", "displayLabel": "PriceSelling", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dfbc9788-3ffc-4650-8ed3-673edc4ac86e", "name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "category": "Input", "uiComponent": "CurrencyInput", "validationPattern": "^[0-9]+(\\.[0-9]{1,2})?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter amount...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "bd449086-1a72-4697-acb2-fc923796e333", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "1f0ee2dd-7a48-40fd-8c4a-5611b9a3eb68", "metadataKey": "Status", "displayLabel": "Status", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "cd900747-65e0-4f76-9ae6-cf929a7cbc84", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "90449ac7-6c13-4223-8fb3-753478a82c16", "metadataKey": "View", "displayLabel": "View", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "ce3092df-2da5-4e94-93a1-8f225f0d6a6c", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "290d6187-8fd7-42be-94a3-66b682077827", "metadataKey": "PaymentPlan", "displayLabel": "PaymentPlan", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dfbc9788-3ffc-4650-8ed3-673edc4ac86e", "name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "category": "Input", "uiComponent": "CurrencyInput", "validationPattern": "^[0-9]+(\\.[0-9]{1,2})?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter amount...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "d89c965b-b302-4c73-a45f-429ff02db8e7", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "a140c3eb-574c-4280-a3d3-6e294b3ea535", "metadataKey": "Type", "displayLabel": "Type", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "e5524e2e-034d-4175-8acc-7cc3def099ff", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "c037c00d-a28e-43f5-8411-bda28543a0e8", "metadataKey": "BalconyType", "displayLabel": "BalconyType", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "ee38ec8f-0c57-4339-bedf-a778e9d0d5c4", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "7954c6d4-dda9-47fa-aa7e-b80747c01135", "metadataKey": "UnitNumber", "displayLabel": "UnitNumber", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "fe8e5436-2442-4a5e-86da-1a41338042c7", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "0469bf47-c41f-400d-814b-449bcb676453", "metadataKey": "WindowsView", "displayLabel": "WindowsView", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "ffc93233-caa2-47c9-bdee-e34bec0b945d", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": [{"id": "a7487160-33be-410d-87c6-bedf49211cc5", "name": "Attachment", "description": "Attachment details", "parentObjectId": "60f06a2d-ab5d-4d37-8840-1216ed65cd28", "isActive": true, "hierarchyLevel": 4, "hierarchyPath": "Organization > Building > Floor > Unit > Attachment", "metadata": [{"metadata": {"id": "8fefde02-ef9b-4f5a-ac97-488c14942415", "metadataKey": "FileName", "displayLabel": "FileName", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5542683d-af98-4d34-8dae-76cc5aae8df0", "name": "file", "displayName": "File", "category": "Upload", "uiComponent": "FileUpload", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "file", "inputMask": null, "placeholder": "Choose file...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "4a2ae143-af1c-4d46-b158-fe920a3103e9", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "55746345-afa4-4cae-a31f-981ab07c450a", "metadataKey": "FileURL", "displayLabel": "FileURL", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "7470458d-5752-45c7-a691-317d03f2414c", "name": "url", "displayName": "URL", "category": "Input", "uiComponent": "UrlInput", "validationPattern": "^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$", "minLength": 10, "maxLength": 2083, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "url", "inputMask": null, "placeholder": "Enter URL...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "72f5eb73-da57-4077-811e-656de697d9f8", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "d563703a-d36f-46b2-b4f6-b1efadc9a93a", "metadataKey": "FileType", "displayLabel": "FileType", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5542683d-af98-4d34-8dae-76cc5aae8df0", "name": "file", "displayName": "File", "category": "Upload", "uiComponent": "FileUpload", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "file", "inputMask": null, "placeholder": "Choose file...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "9bafb332-53c3-400b-8c5c-c88b2abe59c5", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "cf49263a-d86d-49d5-ba95-d16eb19b96c7", "metadataKey": "FileSize", "displayLabel": "FileSize", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5542683d-af98-4d34-8dae-76cc5aae8df0", "name": "file", "displayName": "File", "category": "Upload", "uiComponent": "FileUpload", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "file", "inputMask": null, "placeholder": "Choose file...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "b48c9d1d-c4f9-4494-8db7-52f61137070e", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "1f0ee2dd-7a48-40fd-8c4a-5611b9a3eb68", "metadataKey": "Status", "displayLabel": "Status", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "f0a7e882-e801-4d6f-bb35-7310b963ddff", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": []}, {"id": "f03cbcdd-6b6d-40b3-8dac-ad079389dae8", "name": "Enquiry", "description": "Enquiry details", "parentObjectId": "60f06a2d-ab5d-4d37-8840-1216ed65cd28", "isActive": true, "hierarchyLevel": 4, "hierarchyPath": "Organization > Building > Floor > Unit > Enquiry", "metadata": [{"metadata": {"id": "b4d31974-eac4-4e7f-9fa7-c1ee0f737981", "metadataKey": "InterestedUnit", "displayLabel": "InterestedUnit", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "e533c604-dae3-4ea7-a7f0-ec8b150c8174", "name": "percentage", "displayName": "Percentage", "category": "Input", "uiComponent": "PercentageInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": 100, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter percentage...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "1b297f10-fcc9-40df-90cf-67beafe701ef", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "4019d46a-b095-40c7-a286-b03d11e96f07", "metadataKey": "Customer", "displayLabel": "Customer", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "2fe19e05-5e44-4f3e-8c46-4b0da342ce4c", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "bac34127-e83f-4c4d-8abe-9fdaaf721079", "metadataKey": "Source", "displayLabel": "Source", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "31408373-8abe-4851-8c6b-defb8aae9b11", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "fcbd5beb-f103-41bf-a149-a906d93997ed", "metadataKey": "EnquiryID", "displayLabel": "EnquiryID", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "3b762657-98cc-42cd-9e1f-4ed9cc1ad36c", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "bf4e1c8f-cd35-4cf2-b25f-c6589c6becfd", "metadataKey": "Notes", "displayLabel": "Notes", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "56da8d60-3396-4387-b275-c03f7f9568c0", "name": "textarea", "displayName": "Text Area", "category": "Input", "uiComponent": "TextArea", "validationPattern": null, "minLength": 10, "maxLength": 5000, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "textarea", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "53dd1b0c-82a0-4afb-aae8-5e75ded6b9f2", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "1f0ee2dd-7a48-40fd-8c4a-5611b9a3eb68", "metadataKey": "Status", "displayLabel": "Status", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "bf3804f8-c126-4251-abfa-e4c0e2ae1ec2", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": [{"id": "3defab0f-5690-4c91-baa4-f2247aab4f08", "name": "Booking", "description": "Booking details", "parentObjectId": "f03cbcdd-6b6d-40b3-8dac-ad079389dae8", "isActive": true, "hierarchyLevel": 5, "hierarchyPath": "Organization > Building > Floor > Unit > Enquiry > Booking", "metadata": [{"metadata": {"id": "55bb1262-a145-424a-8847-ed438c5afc4a", "metadataKey": "BookingDate", "displayLabel": "BookingDate", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "52e41f83-eefa-4908-a62f-7eb15a53c588", "name": "date", "displayName": "Date", "category": "Input", "uiComponent": "DatePicker", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "date", "inputMask": null, "placeholder": "Select date...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "097c3613-c06e-40da-825f-6f41b73b1363", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "1f0ee2dd-7a48-40fd-8c4a-5611b9a3eb68", "metadataKey": "Status", "displayLabel": "Status", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "85b65643-ca41-4ecb-ad47-858816ad8a16", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "adb73577-ee80-4966-a2ff-f461b9a5706f", "metadataKey": "SpecialTermsNotes", "displayLabel": "SpecialTermsNotes", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "56da8d60-3396-4387-b275-c03f7f9568c0", "name": "textarea", "displayName": "Text Area", "category": "Input", "uiComponent": "TextArea", "validationPattern": null, "minLength": 10, "maxLength": 5000, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "textarea", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "877fba39-08a3-47e8-9999-0869b869c794", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "290d6187-8fd7-42be-94a3-66b682077827", "metadataKey": "PaymentPlan", "displayLabel": "PaymentPlan", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dfbc9788-3ffc-4650-8ed3-673edc4ac86e", "name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "category": "Input", "uiComponent": "CurrencyInput", "validationPattern": "^[0-9]+(\\.[0-9]{1,2})?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter amount...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "a898a0a4-b219-4d52-8669-ec6761b80642", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "e49f2913-d902-4562-8482-08cb7c1941ef", "metadataKey": "AgreementDocument", "displayLabel": "AgreementDocument", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5542683d-af98-4d34-8dae-76cc5aae8df0", "name": "file", "displayName": "File", "category": "Upload", "uiComponent": "FileUpload", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "file", "inputMask": null, "placeholder": "Choose file...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "be0a7062-b9e7-4bb8-ab05-c4c816ac8a90", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "f89e5c04-dbe7-40ad-8402-c6274da43a0d", "metadataKey": "UnitApartment", "displayLabel": "UnitApartment", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "e533c604-dae3-4ea7-a7f0-ec8b150c8174", "name": "percentage", "displayName": "Percentage", "category": "Input", "uiComponent": "PercentageInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": 100, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter percentage...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "caa1842b-3875-49a9-8775-63b0c6d079b5", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "71842b41-a35f-4d91-bc15-c9eed9c52c31", "metadataKey": "BookingID", "displayLabel": "BookingID", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "cdd01430-b688-4cd1-ac70-4072c00f119f", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "4019d46a-b095-40c7-a286-b03d11e96f07", "metadataKey": "Customer", "displayLabel": "Customer", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "ffe1d004-0e56-4b48-91a5-642a14647f1f", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": [{"id": "08715fe7-40ec-45e0-bc0a-1bba0c47e9d1", "name": "PaymentPlan", "description": "Payment plan details", "parentObjectId": "3defab0f-5690-4c91-baa4-f2247aab4f08", "isActive": true, "hierarchyLevel": 6, "hierarchyPath": "Organization > Building > Floor > Unit > Enquiry > Booking > PaymentPlan", "metadata": [{"metadata": {"id": "1a84c11e-1277-4c4f-8be8-22cb0af4b24f", "metadataKey": "PaymentPlanName", "displayLabel": "PaymentPlanName", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "6abc27f4-99de-4afe-8b83-c92a0beed98f", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "b3ecced1-8aea-4c5a-8d47-222feaf32d57", "metadataKey": "TotalPrice", "displayLabel": "TotalPrice", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "9070edaa-b9ad-42e6-a7e0-3f7d1d6d58fe", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "34321a79-f2db-4e84-99d0-2cbdd437a9f4", "metadataKey": "Milestones", "displayLabel": "Milestones", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "930e05c4-8027-4dc1-9962-9c93d550bfb6", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "20b6173d-ac09-4847-8d60-3ccc87158ef9", "metadataKey": "LinkedUnitBooking", "displayLabel": "LinkedUnitBooking", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "7470458d-5752-45c7-a691-317d03f2414c", "name": "url", "displayName": "URL", "category": "Input", "uiComponent": "UrlInput", "validationPattern": "^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$", "minLength": 10, "maxLength": 2083, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "url", "inputMask": null, "placeholder": "Enter URL...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "d8571cbc-76a7-4eb7-8f73-ddddd68c4421", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "1f0ee2dd-7a48-40fd-8c4a-5611b9a3eb68", "metadataKey": "Status", "displayLabel": "Status", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "fe63fb34-3d6e-4d7f-b032-31293185d5c3", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": [{"id": "bb289b15-bd8d-4820-bb9d-4d94b532590c", "name": "Payment", "description": "Payment details", "parentObjectId": "08715fe7-40ec-45e0-bc0a-1bba0c47e9d1", "isActive": true, "hierarchyLevel": 7, "hierarchyPath": "Organization > Building > Floor > Unit > Enquiry > Booking > PaymentPlan > Payment", "metadata": [{"metadata": {"id": "d944110c-2e43-459b-8623-675b2e27f73e", "metadataKey": "PaymentStatus", "displayLabel": "PaymentStatus", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "1ec34d07-ab40-4361-81e1-f1c11c56caa2", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "30d8873c-43b3-4c20-a252-f402fcfb22d2", "metadataKey": "PaymentAmount", "displayLabel": "PaymentAmount", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "1fb98f3c-f9b5-47b5-bb53-830782810183", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "08b522fa-2bf5-4d13-9c65-03fa3c97cc24", "metadataKey": "PaymentMilestone", "displayLabel": "PaymentMilestone", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dfbc9788-3ffc-4650-8ed3-673edc4ac86e", "name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "category": "Input", "uiComponent": "CurrencyInput", "validationPattern": "^[0-9]+(\\.[0-9]{1,2})?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter amount...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "52596895-599b-423d-8e91-14ec6e6f6623", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "138573a2-038f-47a4-9558-dee4f466c37b", "metadataKey": "PaymentPlanReference", "displayLabel": "PaymentPlanReference", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "7470458d-5752-45c7-a691-317d03f2414c", "name": "url", "displayName": "URL", "category": "Input", "uiComponent": "UrlInput", "validationPattern": "^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$", "minLength": 10, "maxLength": 2083, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "url", "inputMask": null, "placeholder": "Enter URL...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "537fd8c6-b125-4b9f-8f3c-17f6b5613efe", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "fc142cee-5696-4e3a-826d-d6a0e63df356", "metadataKey": "BookingReference", "displayLabel": "BookingReference", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "7470458d-5752-45c7-a691-317d03f2414c", "name": "url", "displayName": "URL", "category": "Input", "uiComponent": "UrlInput", "validationPattern": "^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$", "minLength": 10, "maxLength": 2083, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "url", "inputMask": null, "placeholder": "Enter URL...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "5687051a-c951-4aca-9dba-2a9b898de5e7", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "a21ea658-73ca-451b-b2d1-1e26a81e9fbc", "metadataKey": "CustomerReference", "displayLabel": "CustomerReference", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "7470458d-5752-45c7-a691-317d03f2414c", "name": "url", "displayName": "URL", "category": "Input", "uiComponent": "UrlInput", "validationPattern": "^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$", "minLength": 10, "maxLength": 2083, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "url", "inputMask": null, "placeholder": "Enter URL...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "700e361b-7fa0-437d-89e9-ae4a9693c599", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "a56c93cf-0a09-4a0e-8254-b665180e17ec", "metadataKey": "PaymentID", "displayLabel": "PaymentID", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "72616f94-8ea0-4330-bff0-bf02306fc91c", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "9edb010b-69fe-40ab-a7be-ee570ac0ae8c", "metadataKey": "PaymentDate", "displayLabel": "PaymentDate", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dfbc9788-3ffc-4650-8ed3-673edc4ac86e", "name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "category": "Input", "uiComponent": "CurrencyInput", "validationPattern": "^[0-9]+(\\.[0-9]{1,2})?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter amount...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "8a1c4fc2-182c-4d37-af0d-78e73e2a7140", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "47754310-56bd-4ca3-bd63-1aba9cae07f1", "metadataKey": "ReceiptNumber", "displayLabel": "ReceiptNumber", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "934916d6-a55c-4304-b6a2-4db60bacfc7b", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "56f7f9a1-61b0-4d0c-97eb-82bf8ef6d336", "metadataKey": "PaymentMethod", "displayLabel": "PaymentMethod", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dfbc9788-3ffc-4650-8ed3-673edc4ac86e", "name": "currency", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "category": "Input", "uiComponent": "CurrencyInput", "validationPattern": "^[0-9]+(\\.[0-9]{1,2})?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter amount...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "9ce20f34-5c67-403f-aaf2-3377adb67a87", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "bf4e1c8f-cd35-4cf2-b25f-c6589c6becfd", "metadataKey": "Notes", "displayLabel": "Notes", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "56da8d60-3396-4387-b275-c03f7f9568c0", "name": "textarea", "displayName": "Text Area", "category": "Input", "uiComponent": "TextArea", "validationPattern": null, "minLength": 10, "maxLength": 5000, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "textarea", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "d6008b48-5881-4af8-8671-5eb62c5c88f0", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "55c26b82-e01c-4599-b883-2c3f76505d36", "metadataKey": "Attachment", "displayLabel": "Attachment", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5542683d-af98-4d34-8dae-76cc5aae8df0", "name": "file", "displayName": "File", "category": "Upload", "uiComponent": "FileUpload", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "file", "inputMask": null, "placeholder": "Choose file...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "e177ebc6-1b6a-46f6-98aa-a9bb3e91bcc5", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "c9bbb9f4-b582-4b28-91d2-9c5360f71f83", "metadataKey": "TransactionReference", "displayLabel": "TransactionReference", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "7470458d-5752-45c7-a691-317d03f2414c", "name": "url", "displayName": "URL", "category": "Input", "uiComponent": "UrlInput", "validationPattern": "^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$", "minLength": 10, "maxLength": 2083, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "url", "inputMask": null, "placeholder": "Enter URL...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "e1ed5e74-7ab1-4ea4-a646-fe1effa34015", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": []}]}]}]}]}]}]}, {"id": "50591274-60ca-45e1-8915-a545ad50819b", "name": "ChannelPartner", "description": "Channel partner details", "parentObjectId": "033f0a76-8f1d-4537-8045-54b08e621014", "isActive": true, "hierarchyLevel": 1, "hierarchyPath": "Organization > ChannelPartner", "metadata": [{"metadata": {"id": "bdae2258-3133-4401-9b6e-a4c43d1bdf0f", "metadataKey": "<PERSON><PERSON><PERSON>", "displayLabel": "<PERSON><PERSON><PERSON>", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "314e3b05-23df-4142-a7a4-1c87d498a425", "name": "phone", "displayName": "Phone", "category": "Input", "uiComponent": "PhoneInput", "validationPattern": "^[\\+]?[1-9][\\d]{0,15}$", "minLength": 10, "maxLength": 15, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "tel", "inputMask": null, "placeholder": "Enter phone number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "0a09b88d-f67a-45d8-a110-a838521c687d", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "4475c5e2-d96b-4270-989b-3df5bbc9449f", "metadataKey": "Address", "displayLabel": "Address", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dce2def4-bcdd-443d-85b7-fe906750a77e", "name": "address", "displayName": "Address", "category": "Input", "uiComponent": "AddressInput", "validationPattern": null, "minLength": 10, "maxLength": 500, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "1ca5bdcc-d410-409b-83d4-912bc49ba45d", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "5e7803ae-2109-4b25-88cf-be5bcb424384", "metadataKey": "Organization", "displayLabel": "Organization", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "1cc0cad1-87d6-43a6-8bd8-587cad4cd491", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "4a8b01f9-92c7-4971-9b35-f1705b9f447a", "metadataKey": "PhoneNumber", "displayLabel": "PhoneNumber", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "5b46d815-ed03-4bf1-9cdf-d0917d3595d5", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "72916c14-40f2-49aa-b770-390efdcab2c2", "metadataKey": "CommissionStructure", "displayLabel": "CommissionStructure", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "e533c604-dae3-4ea7-a7f0-ec8b150c8174", "name": "percentage", "displayName": "Percentage", "category": "Input", "uiComponent": "PercentageInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": 100, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter percentage...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "837dd1e6-303a-495d-a302-031b3db0d89d", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "c85248c4-59cd-497b-8225-a671381c517b", "metadataKey": "Email", "displayLabel": "Email", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5c76cf92-55c3-4ceb-a6d7-63d59a3838fd", "name": "email", "displayName": "Email", "category": "Input", "uiComponent": "EmailInput", "validationPattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "minLength": 5, "maxLength": 320, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "email", "inputMask": null, "placeholder": "Enter email address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "963e924b-172c-4129-b114-afc5a18266ee", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "feb283d8-08a3-4f0d-a52b-a6751117623e", "metadataKey": "ChannelPartnerName", "displayLabel": "ChannelPartnerName", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "e533c604-dae3-4ea7-a7f0-ec8b150c8174", "name": "percentage", "displayName": "Percentage", "category": "Input", "uiComponent": "PercentageInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": 100, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter percentage...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "ad134211-ab87-49eb-bf5f-2f322fb11e34", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": [{"id": "eb2cbe96-6057-4786-9d94-3de9862b785a", "name": "ChannelPartnerCustomer", "description": "Channel partner customer details", "parentObjectId": "50591274-60ca-45e1-8915-a545ad50819b", "isActive": true, "hierarchyLevel": 2, "hierarchyPath": "Organization > ChannelPartner > ChannelPartnerCustomer", "metadata": [{"metadata": {"id": "91c442ab-ee78-4cdc-98bf-ef98cce6a63c", "metadataKey": "CustomerName", "displayLabel": "CustomerName", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "015bdcaa-dda3-45db-bb61-964ce8363aa0", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "4475c5e2-d96b-4270-989b-3df5bbc9449f", "metadataKey": "Address", "displayLabel": "Address", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dce2def4-bcdd-443d-85b7-fe906750a77e", "name": "address", "displayName": "Address", "category": "Input", "uiComponent": "AddressInput", "validationPattern": null, "minLength": 10, "maxLength": 500, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "412ca323-70ec-46a5-8ccc-245f4f2d9b92", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "4a8b01f9-92c7-4971-9b35-f1705b9f447a", "metadataKey": "PhoneNumber", "displayLabel": "PhoneNumber", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "5210acb2-e8bb-4de3-ab17-4ac8df15debf", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "1f4962a9-38a5-41c1-a362-b5cf65435020", "metadataKey": "KYCDetails", "displayLabel": "KYCDetails", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "56da8d60-3396-4387-b275-c03f7f9568c0", "name": "textarea", "displayName": "Text Area", "category": "Input", "uiComponent": "TextArea", "validationPattern": null, "minLength": 10, "maxLength": 5000, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "textarea", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "6a32a274-b379-47d2-8406-acf927a52249", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "c85248c4-59cd-497b-8225-a671381c517b", "metadataKey": "Email", "displayLabel": "Email", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5c76cf92-55c3-4ceb-a6d7-63d59a3838fd", "name": "email", "displayName": "Email", "category": "Input", "uiComponent": "EmailInput", "validationPattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "minLength": 5, "maxLength": 320, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "email", "inputMask": null, "placeholder": "Enter email address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "7491c96b-fd01-4b61-a415-28704875db84", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "9152e983-6caf-468d-9828-fec406f3140f", "metadataKey": "ChannelPartner", "displayLabel": "ChannelPartner", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "e533c604-dae3-4ea7-a7f0-ec8b150c8174", "name": "percentage", "displayName": "Percentage", "category": "Input", "uiComponent": "PercentageInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": 0, "maxValue": 100, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter percentage...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "984b1f1c-f674-438e-a0a1-5f0aab14f554", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": []}]}, {"id": "6e78fafd-bd5f-4176-8d17-b15b04b2ea95", "name": "Customer", "description": "Customer details", "parentObjectId": "033f0a76-8f1d-4537-8045-54b08e621014", "isActive": true, "hierarchyLevel": 1, "hierarchyPath": "Organization > Customer", "metadata": [{"metadata": {"id": "f8b8211c-7b0f-4258-b076-3f8d53c31ece", "metadataKey": "CustomerType", "displayLabel": "CustomerType", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "42ebf3ec-9a53-460b-892d-e4815f527047", "name": "select", "displayName": "Select", "category": "Selection", "uiComponent": "Select", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "select", "inputMask": null, "placeholder": "Choose option...", "htmlAttributes": null, "defaultOptions": "[]", "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "1db8bdf0-e96b-4d10-9d5b-81d270f245df", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "c85248c4-59cd-497b-8225-a671381c517b", "metadataKey": "Email", "displayLabel": "Email", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5c76cf92-55c3-4ceb-a6d7-63d59a3838fd", "name": "email", "displayName": "Email", "category": "Input", "uiComponent": "EmailInput", "validationPattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "minLength": 5, "maxLength": 320, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "email", "inputMask": null, "placeholder": "Enter email address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "2ad71179-3303-4029-9f99-06036db7b74d", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "1f4962a9-38a5-41c1-a362-b5cf65435020", "metadataKey": "KYCDetails", "displayLabel": "KYCDetails", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "56da8d60-3396-4387-b275-c03f7f9568c0", "name": "textarea", "displayName": "Text Area", "category": "Input", "uiComponent": "TextArea", "validationPattern": null, "minLength": 10, "maxLength": 5000, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "textarea", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "36ccc96d-f05a-4010-895c-5a2f2b2de67f", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "4a8b01f9-92c7-4971-9b35-f1705b9f447a", "metadataKey": "PhoneNumber", "displayLabel": "PhoneNumber", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "817a5d1c-0df7-44b5-844d-1687e22d5c21", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "91c442ab-ee78-4cdc-98bf-ef98cce6a63c", "metadataKey": "CustomerName", "displayLabel": "CustomerName", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "a26ec3cd-b385-4efa-9e27-a80d68690565", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "4475c5e2-d96b-4270-989b-3df5bbc9449f", "metadataKey": "Address", "displayLabel": "Address", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dce2def4-bcdd-443d-85b7-fe906750a77e", "name": "address", "displayName": "Address", "category": "Input", "uiComponent": "AddressInput", "validationPattern": null, "minLength": 10, "maxLength": 500, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "d0929d88-fbf0-4b16-bbea-f995886dc3b2", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": []}, {"id": "41fa40d3-4c6f-47d0-8759-c056729d8a32", "name": "District", "description": "District details", "parentObjectId": "033f0a76-8f1d-4537-8045-54b08e621014", "isActive": true, "hierarchyLevel": 1, "hierarchyPath": "Organization > District", "metadata": [{"metadata": {"id": "e4bf890d-f7a4-4115-ae7b-1b5ca7adc3d8", "metadataKey": "Images", "displayLabel": "Images", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "5542683d-af98-4d34-8dae-76cc5aae8df0", "name": "file", "displayName": "File", "category": "Upload", "uiComponent": "FileUpload", "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "file", "inputMask": null, "placeholder": "Choose file...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "09289e94-404f-45e9-9db2-002552b8519d", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "22aa3afc-27fc-4238-9014-4b5fa01e2510", "metadataKey": "City", "displayLabel": "City", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "66fa4bd6-77c0-4085-ae59-66795949e885", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "6a0981d1-6d9a-4141-8c2d-f5226dce1be3", "metadataKey": "Description", "displayLabel": "Description", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "56da8d60-3396-4387-b275-c03f7f9568c0", "name": "textarea", "displayName": "Text Area", "category": "Input", "uiComponent": "TextArea", "validationPattern": null, "minLength": 10, "maxLength": 5000, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "textarea", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "8ba5f055-d9a1-4c58-aad4-d5a71b3d7840", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "5c053009-ebf6-47ab-9742-96452049ee77", "metadataKey": "CenterCoordinates", "displayLabel": "CenterCoordinates", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dce2def4-bcdd-443d-85b7-fe906750a77e", "name": "address", "displayName": "Address", "category": "Input", "uiComponent": "AddressInput", "validationPattern": null, "minLength": 10, "maxLength": 500, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "9be30949-5b33-492d-bc3a-2aa94b589632", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "f0e0ad90-f4cf-402d-a35c-e59a124ca6b3", "metadataKey": "PolygonCoordinates", "displayLabel": "PolygonCoordinates", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "dce2def4-bcdd-443d-85b7-fe906750a77e", "name": "address", "displayName": "Address", "category": "Input", "uiComponent": "AddressInput", "validationPattern": null, "minLength": 10, "maxLength": 500, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter address...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "b2f0b8d9-903b-4d96-891c-3ed002fbcd53", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "725a8777-f915-4bcb-9127-e3984bf504e9", "metadataKey": "Country", "displayLabel": "Country", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "35af77b4-f7e5-4e5a-93f4-3af0304a002a", "name": "number", "displayName": "Number", "category": "Input", "uiComponent": "NumberInput", "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "number", "inputMask": null, "placeholder": "Enter number...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "badd4a0d-ee5d-4697-9003-ec2d6ca22299", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}, {"metadata": {"id": "ccf90d4e-0d28-45a0-bf8c-ac063c4f0a20", "metadataKey": "DistrictName", "displayLabel": "DistrictName", "helpText": null, "fieldOrder": null, "isVisible": true, "isReadonly": null, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": null, "placeholder": null, "defaultOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSize": null, "errorMessage": null, "dataType": {"id": "88079632-6e08-4358-8d9e-05b6298cad67", "name": "text", "displayName": "Text", "category": "Input", "uiComponent": "TextInput", "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "decimalPlaces": null, "stepValue": null, "isRequired": false, "inputType": "text", "inputMask": null, "placeholder": "Enter text...", "htmlAttributes": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": null, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "requiredErrorMessage": null, "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "fileSizeErrorMessage": null, "isActive": null}, "metadataLink": {"objectMetaDataId": "cf766b18-9949-4bae-9f1f-ebff7a95eef7", "isUnique": false, "isActive": true, "shouldVisibleInList": null, "shouldVisibleInEdit": null, "shouldVisibleInCreate": null, "shouldVisibleInView": null, "isCalculate": null}}, "values": []}], "childObjects": []}]}]}], "totalObjectsCount": 13, "totalMetadataCount": 98, "maxHierarchyDepth": 7}, "message": null, "statusCode": 200}