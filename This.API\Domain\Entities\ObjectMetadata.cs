using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links Object types to their metadata with IsUnique support
/// </summary>
public class ObjectMetadata : AuditableEntity, IAggregateRoot
{

    /// <summary>
    /// Object ID
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the object
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInList { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInEdit { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInCreate { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInView { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsCalculated { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Object
    /// </summary>
    public virtual Object Object { get; set; } = null!;

    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// Object values
    /// </summary>
    public virtual ICollection<ObjectValue> ObjectValues { get; set; } = new List<ObjectValue>();

    /// <summary>
    /// Field mappings targeting this object metadata
    /// </summary>
    public virtual ICollection<FieldMapping> FieldMappings { get; set; } = new List<FieldMapping>();
}
