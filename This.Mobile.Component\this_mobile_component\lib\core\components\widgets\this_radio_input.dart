import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// Option model for radio items
class RadioOption {
  final String value;
  final String label;
  final String? description;
  final bool disabled;
  final Widget? icon;

  const RadioOption({
    required this.value,
    required this.label,
    this.description,
    this.disabled = false,
    this.icon,
  });
}

/// A customizable radio input widget following the 'this_componentName_relatedTo' naming convention
/// This widget handles single selection from multiple radio options with validation and theming
class ThisRadioInput extends StatefulWidget {
  final String id;
  final String label;
  final List<RadioOption> options;
  final String? value;
  final ValueChanged<String?> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final Axis direction;
  final int? crossAxisCount;
  final double spacing;
  final double runSpacing;
  final String? Function(String?)? customValidation;

  const ThisRadioInput({
    super.key,
    required this.id,
    required this.label,
    required this.options,
    required this.value,
    required this.onChanged,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.direction = Axis.vertical,
    this.crossAxisCount,
    this.spacing = 8.0,
    this.runSpacing = 8.0,
    this.customValidation,
  });

  @override
  State<ThisRadioInput> createState() => _ThisRadioInputState();
}

class _ThisRadioInputState extends State<ThisRadioInput> {
  List<String> _errors = [];

  @override
  void initState() {
    super.initState();
    _validateValue(widget.value);
  }

  List<String> _validateValue(String? value) {
    final errors = <String>[];

    // Required validation
    if (widget.required && (value == null || value.isEmpty)) {
      errors.add('${widget.label} is required');
      return errors;
    }

    // Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(value);
      if (customError != null) {
        errors.add(customError);
      }
    }

    return errors;
  }

  void _handleChange(String? newValue) {
    if (widget.disabled || widget.readOnly) return;

    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.white,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Options
        if (widget.crossAxisCount != null)
          // Grid layout
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: widget.crossAxisCount!,
              crossAxisSpacing: widget.spacing,
              mainAxisSpacing: widget.runSpacing,
              childAspectRatio: 4,
            ),
            itemCount: widget.options.length,
            itemBuilder: (context, index) => _buildRadioTile(widget.options[index]),
          )
        else
          // List layout
          Column(
            children: widget.options.map(_buildRadioTile).toList(),
          ),

        // Error Messages
        if (hasErrors)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _errors.first,
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildRadioTile(RadioOption option) {
    final isSelected = widget.value == option.value;
    final isDisabled = widget.disabled || widget.readOnly || option.disabled;

    return RadioListTile<String>(
      value: option.value,
      groupValue: widget.value,
      onChanged: isDisabled ? null : (value) => _handleChange(value),
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (option.icon != null) ...[
            option.icon!,
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  option.label,
                  style: LexendTextStyles.lexend14Regular.copyWith(
                    color: isDisabled ? ColorPalette.placeHolderTextColor : ColorPalette.white,
                  ),
                ),
                if (option.description != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      option.description!,
                      style: LexendTextStyles.lexend12Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: EdgeInsets.zero,
      dense: true,
      activeColor: ColorPalette.white,
    );
  }
}

/// A specialized version of ThisRadioInput for button-style radio selection
class ThisRadioButtonInput extends StatefulWidget {
  final String id;
  final String label;
  final List<RadioOption> options;
  final String? value;
  final ValueChanged<String?> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final Axis direction;
  final double spacing;
  final String? Function(String?)? customValidation;

  const ThisRadioButtonInput({
    super.key,
    required this.id,
    required this.label,
    required this.options,
    required this.value,
    required this.onChanged,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.direction = Axis.horizontal,
    this.spacing = 8.0,
    this.customValidation,
  });

  @override
  State<ThisRadioButtonInput> createState() => _ThisRadioButtonInputState();
}

class _ThisRadioButtonInputState extends State<ThisRadioButtonInput> {
  List<String> _errors = [];

  @override
  void initState() {
    super.initState();
    _validateValue(widget.value);
  }

  List<String> _validateValue(String? value) {
    final errors = <String>[];

    // Required validation
    if (widget.required && (value == null || value.isEmpty)) {
      errors.add('${widget.label} is required');
      return errors;
    }

    // Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(value);
      if (customError != null) {
        errors.add(customError);
      }
    }

    return errors;
  }

  void _handleChange(String? newValue) {
    if (widget.disabled || widget.readOnly) return;

    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.white,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Button Options
        widget.direction == Axis.horizontal
            ? Wrap(
                spacing: widget.spacing,
                runSpacing: widget.spacing,
                children: widget.options.map(_buildRadioButton).toList(),
              )
            : Column(
                children: widget.options
                    .map((option) => Padding(
                          padding: EdgeInsets.only(bottom: widget.spacing),
                          child: _buildRadioButton(option),
                        ))
                    .toList(),
              ),

        // Error Messages
        if (hasErrors)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _errors.first,
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildRadioButton(RadioOption option) {
    final isSelected = widget.value == option.value;
    final isDisabled = widget.disabled || widget.readOnly || option.disabled;

    return GestureDetector(
      onTap: isDisabled ? null : () => _handleChange(option.value),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? ColorPalette.white.withOpacity(0.2) : Colors.transparent,
          border: Border.all(
            color: isSelected ? ColorPalette.white : ColorPalette.gray300.withOpacity(0.5),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (option.icon != null) ...[
              option.icon!,
              const SizedBox(width: 8),
            ],
            Text(
              option.label,
              style: LexendTextStyles.lexend14Regular.copyWith(
                color: isDisabled ? ColorPalette.placeHolderTextColor : (isSelected ? ColorPalette.white : ColorPalette.white.withOpacity(0.8)),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
