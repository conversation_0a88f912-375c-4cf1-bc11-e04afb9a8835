using Abstraction.Database.Repositories;
using Application.Templates.DTOs;
using Application.Templates.Specifications;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Templates.Commands;

/// <summary>
/// Handler for PublishTemplateCommand
/// </summary>
public class PublishTemplateCommandHandler : IRequestHandler<PublishTemplateCommand, Result<TemplateDto>>
{
    private readonly IRepository<Template> _templateRepository;
    private readonly ILogger<PublishTemplateCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public PublishTemplateCommandHandler(
        IRepository<Template> templateRepository,
        ILogger<PublishTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<TemplateDto>> Handle(PublishTemplateCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Publishing template {TemplateId}", request.Id);

            // Get template using specification
            var templateSpec = new TemplateByIdSpec(request.Id);
            var template = await _templateRepository.GetBySpecAsync(templateSpec, cancellationToken);

            if (template == null)
            {
                _logger.LogWarning("Template not found: {TemplateId}", request.Id);
                return Result<TemplateDto>.Failure("Template not found");
            }

            // Set published date and ensure it's active
            template.PublishedAt = DateTime.UtcNow;
            template.IsActive = true;
            template.ModifiedAt = DateTime.UtcNow;

            await _templateRepository.UpdateAsync(template, cancellationToken);

            var publishedTemplate = new TemplateDto
            {
                Id = template.Id,
                ProductId = template.ProductId,
                ProductName = "Unknown", // No relationship with Product table
                Version = template.Version,
                Stage = template.Stage,
                TemplateJson = template.TemplateJson,
                CreatedAt = template.CreatedAt,
                CreatedBy = template.CreatedBy,
                PublishedAt = template.PublishedAt,
                IsActive = template.IsActive,
                IsDeleted = template.IsDeleted
            };

            _logger.LogInformation("Successfully published template {TemplateId}", request.Id);
            return Result<TemplateDto>.Success(publishedTemplate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing template {TemplateId}", request.Id);
            return Result<TemplateDto>.Failure("Failed to publish template");
        }
    }
}
