using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectMetadataManagement.Specifications;

/// <summary>
/// Specification to get ObjectMetadata by ObjectId
/// </summary>
public class ObjectMetadataByObjectIdSpec : Specification<ObjectMetadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectMetadataByObjectIdSpec(Guid objectId, bool onlyActive = true)
    {
        Query.Where(om => om.ObjectId == objectId && !om.IsDeleted);

        if (onlyActive)
        {
            Query.Where(om => om.IsActive);
        }

        // Include related data
        Query.Include(om => om.Object);
        Query.Include(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        // Order by field order and metadata key
        Query.OrderBy(om => om.Metadata.FieldOrder ?? int.MaxValue)
             .ThenBy(om => om.Metadata.Name);
    }
}
