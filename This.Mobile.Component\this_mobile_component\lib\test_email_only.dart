import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/app_theme.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/this_email_input.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Email Widget Test',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.darkThemeMode,
      home: const EmailTestPage(),
    );
  }
}

class EmailTestPage extends StatefulWidget {
  const EmailTestPage({super.key});

  @override
  State<EmailTestPage> createState() => _EmailTestPageState();
}

class _EmailTestPageState extends State<EmailTestPage> {
  String _emailValue = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorPalette.primaryDarkColor,
        title: Text(
          'Email Widget Test',
          style: LexendTextStyles.lexend16ExtraLight.copyWith(
            color: ColorPalette.white,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Testing Email Input Widget',
              style: LexendTextStyles.lexend18Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 24),

            ThisEmailInput(
              id: 'email_test',
              label: 'Email Address',
              value: _emailValue,
              onChanged: (value) => setState(() => _emailValue = value),
              placeholder: 'Enter your email...',
              required: true,
              allowedDomains: ['gmail.com', 'yahoo.com'],
              showValidationIcon: true,
              helpText: 'Enter a valid email address',
            ),

            const SizedBox(height: 24),

            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorPalette.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: ColorPalette.green),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ Email Widget Working!',
                    style: LexendTextStyles.lexend14Bold.copyWith(
                      color: ColorPalette.green,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Current value: ${_emailValue.isEmpty ? "Empty" : _emailValue}',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
