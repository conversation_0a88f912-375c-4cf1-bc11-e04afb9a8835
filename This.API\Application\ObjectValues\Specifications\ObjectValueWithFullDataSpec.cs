using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get ObjectValues with full metadata information
/// </summary>
public class ObjectValueWithFullDataSpec : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectValueWithFullDataSpec(
        Guid? productId = null,
        Guid? objectId = null,
        Guid? metadataId = null,
        Guid? refId = null,
        string? searchTerm = null,
        bool onlyVisibleFields = false,
        bool onlyParentValues = false,
        string? orderBy = null,
        int skip = 0,
        int take = 0)
    {
        Query.Where(ov => !ov.IsDeleted);

        // Include all related data
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object);

        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        // Filter by product
        if (productId.HasValue)
        {
            Query.Where(ov => ov.ObjectMetadata.Object.ProductId == productId.Value);
        }

        // Filter by specific object
        if (objectId.HasValue)
        {
            Query.Where(ov => ov.ObjectMetadata.ObjectId == objectId.Value);
        }

        // Filter by metadata
        if (metadataId.HasValue)
        {
            Query.Where(ov => ov.ObjectMetadata.MetadataId == metadataId.Value);
        }

        // Filter by RefId
        if (refId.HasValue)
        {
            Query.Where(ov => ov.RefId == refId.Value);
        }

        // Search term filter
        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(ov => ov.Value != null && ov.Value.Contains(searchTerm) ||
                             ov.ObjectMetadata.Metadata.Name.Contains(searchTerm) ||
                             ov.ObjectMetadata.Object.Name.Contains(searchTerm));
        }

        // Filter by visible fields only
        if (onlyVisibleFields)
        {
            Query.Where(ov => ov.ObjectMetadata.Metadata.IsVisible);
        }

        // Filter only parent values (no parent)
        if (onlyParentValues)
        {
            Query.Where(ov => ov.ParentObjectValueId == null);
        }

        // Additional filters for active records
        Query.Where(ov => ov.ObjectMetadata.Object.IsActive && 
                         !ov.ObjectMetadata.Object.IsDeleted &&
                         ov.ObjectMetadata.IsActive && 
                         !ov.ObjectMetadata.IsDeleted);

        // Apply ordering
        if (!string.IsNullOrEmpty(orderBy))
        {
            switch (orderBy.ToLower())
            {
                case "metadata":
                    Query.OrderBy(ov => ov.ObjectMetadata.Metadata.Name);
                    break;
                case "metadata_desc":
                    Query.OrderByDescending(ov => ov.ObjectMetadata.Metadata.Name);
                    break;
                case "object":
                    Query.OrderBy(ov => ov.ObjectMetadata.Object.Name);
                    break;
                case "object_desc":
                    Query.OrderByDescending(ov => ov.ObjectMetadata.Object.Name);
                    break;
                case "value":
                    Query.OrderBy(ov => ov.Value);
                    break;
                case "value_desc":
                    Query.OrderByDescending(ov => ov.Value);
                    break;
                case "created":
                    Query.OrderBy(ov => ov.CreatedAt);
                    break;
                case "created_desc":
                    Query.OrderByDescending(ov => ov.CreatedAt);
                    break;
                default:
                    Query.OrderBy(ov => ov.ObjectMetadata.Metadata.FieldOrder ?? 999)
                         .ThenBy(ov => ov.ObjectMetadata.Metadata.Name);
                    break;
            }
        }
        else
        {
            Query.OrderBy(ov => ov.ObjectMetadata.Metadata.FieldOrder ?? 999)
                 .ThenBy(ov => ov.ObjectMetadata.Metadata.Name);
        }

        // Apply pagination
        if (skip > 0)
        {
            Query.Skip(skip);
        }

        if (take > 0)
        {
            Query.Take(take);
        }
    }
}
