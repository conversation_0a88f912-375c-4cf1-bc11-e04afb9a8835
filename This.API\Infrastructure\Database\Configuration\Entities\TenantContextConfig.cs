using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for TenantContext entity
/// </summary>
public class TenantContextConfig : IEntityTypeConfiguration<TenantContext>
{
    public void Configure(EntityTypeBuilder<TenantContext> builder)
    {
        builder.ToTable("TenantContexts", "Genp");

        // Multi-tenant configuration
        builder.IsMultiTenant();

        // Properties
        builder.Property(e => e.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnType("TEXT");

        builder.Property(e => e.Category)
            .HasMaxLength(50);

        
        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false);

        builder.HasIndex(e => e.Name)
            .HasDatabaseName("IX_TenantContexts_Name");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_TenantContexts_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Relationships
        builder.HasMany(e => e.TenantLookups)
            .WithOne(e => e.TenantContext)
            .HasForeignKey(e => e.TenantContextId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.DataTypes)
            .WithOne(e => e.TenantContext)
            .HasForeignKey(e => e.TenantContextId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(e => e.MetadataOverrides)
            .WithOne(e => e.OverrideTenantContext)
            .HasForeignKey(e => e.OverrideTenantContextId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
