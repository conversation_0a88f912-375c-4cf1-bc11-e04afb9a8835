using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Links TenantInfo types to their metadata with IsUnique support
/// </summary>
public class TenantInfoMetadata : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Metadata ID
    /// </summary>
    public Guid MetadataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the tenant
    /// </summary>
    public bool IsUnique { get; set; } = false;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInList { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInEdit { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInCreate { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsVisibleInView { get; set; } = true;

    /// <summary>
    /// IsVisibleInList
    /// </summary>
    public bool IsCalculated { get; set; } = true;

    // Navigation properties
    /// <summary>
    /// Metadata definition
    /// </summary>
    public virtual Metadata Metadata { get; set; } = null!;

    /// <summary>
    /// Tenant info values
    /// </summary>
    public virtual ICollection<TenantInfoValue> TenantInfoValues { get; set; } = new List<TenantInfoValue>();
}
