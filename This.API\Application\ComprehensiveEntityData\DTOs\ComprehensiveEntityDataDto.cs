namespace Application.ComprehensiveEntityData.DTOs;

/// <summary>
/// STREAMLINED: Hierarchical Entity Data Response DTO
/// Returns data in proper parent-child nested structure
/// </summary>
public class HierarchicalEntityDataResponseDto
{
    /// <summary>
    /// List of products with their complete hierarchical data
    /// </summary>
    public List<ProductHierarchicalDto> Products { get; set; } = new();



    /// <summary>
    /// Total count of all objects across all products (including nested)
    /// </summary>
    public int TotalObjectsCount => Products.Sum(p => p.GetTotalObjectsCount());

    /// <summary>
    /// Total count of all metadata entries across all products
    /// </summary>
    public int TotalMetadataCount => Products.Sum(p => p.GetTotalMetadataCount());

    /// <summary>
    /// Maximum hierarchy depth across all products
    /// </summary>
    public int MaxHierarchyDepth => Products.SelectMany(f => f.RootObjects).DefaultIfEmpty().Max(o => o?.GetMaxDepth() ?? 0);
}

/// <summary>
/// STREAMLINED: Product with hierarchical structure
/// </summary>
public class ProductHierarchicalDto
{
    /// <summary>
    /// Product ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Product version
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// Whether the product is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Product metadata with values
    /// </summary>
    public List<MetadataWithValuesDto> Metadata { get; set; } = new();

    /// <summary>
    /// ROOT OBJECTS ONLY (ParentObjectId = null) - Direct objects under product (Features removed)
    /// Child objects are nested within their parents
    /// </summary>
    public List<ObjectHierarchicalDto> RootObjects { get; set; } = new();

    /// <summary>
    /// Get total objects count (including all nested objects)
    /// </summary>
    public int GetTotalObjectsCount() => RootObjects.Sum(o => o.GetTotalObjectsCount());

    /// <summary>
    /// Get total metadata count (including all nested metadata)
    /// </summary>
    public int GetTotalMetadataCount() => Metadata.Count + RootObjects.Sum(o => o.GetTotalMetadataCount());
}

/// <summary>
/// STREAMLINED: Object with proper hierarchical nesting structure
/// Child objects are nested within their parent objects
/// </summary>
public class ObjectHierarchicalDto
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Parent object ID (null for root objects)
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Hierarchy level (0 = root, 1 = first child, etc.)
    /// </summary>
    public int HierarchyLevel { get; set; }

    /// <summary>
    /// Full hierarchy path (e.g., "Organization > Building > Floor > Unit")
    /// </summary>
    public string HierarchyPath { get; set; } = string.Empty;

    /// <summary>
    /// Object metadata with values and data types
    /// </summary>
    public List<MetadataWithValuesDto> Metadata { get; set; } = new();

    /// <summary>
    /// CHILD OBJECTS - Nested within their parent (recursive structure)
    /// </summary>
    public List<ObjectHierarchicalDto> ChildObjects { get; set; } = new();

    /// <summary>
    /// Get total objects count (1 + all nested children)
    /// </summary>
    public int GetTotalObjectsCount() => 1 + ChildObjects.Sum(c => c.GetTotalObjectsCount());

    /// <summary>
    /// Get total metadata count (including all nested metadata)
    /// </summary>
    public int GetTotalMetadataCount() => Metadata.Count + ChildObjects.Sum(c => c.GetTotalMetadataCount());

    /// <summary>
    /// Get maximum depth of nested hierarchy
    /// </summary>
    public int GetMaxDepth()
    {
        if (!ChildObjects.Any())
            return HierarchyLevel;

        return ChildObjects.Max(c => c.GetMaxDepth());
    }
}

/// <summary>
/// Metadata with values DTO
/// </summary>
public class MetadataWithValuesDto
{
    /// <summary>
    /// Metadata information (includes nested dataType and metadataLink)
    /// </summary>
    public MetadataInfoDto Metadata { get; set; } = new();

    /// <summary>
    /// Values for this metadata
    /// </summary>
    public List<ValueInfoDto> Values { get; set; } = new();
}

/// <summary>
/// Metadata information DTO - COMPLETE with ALL properties
/// </summary>
public class MetadataInfoDto
{
    public Guid? Id { get; set; }
    public string? Name { get; set; }

    // UI Display Properties - ALL FIELDS
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }

    // Validation overrides - UPDATED FIELDS
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool? IsRequired { get; set; }

    // UI overrides - UPDATED FIELDS
    public string? Placeholder { get; set; }
    public string? DefaultOptions { get; set; }
    public int? MaxSelections { get; set; }
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSize { get; set; }

    // Error messages - UPDATED FIELDS
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Data type information (nested) - COMPLETE
    /// </summary>
    public DataTypeInfoDto? DataType { get; set; }

    /// <summary>
    /// Metadata link information (nested) - COMPLETE
    /// </summary>
    public MetadataLinkInfoDto? MetadataLink { get; set; }
}

/// <summary>
/// Data type information DTO - COMPLETE with ALL properties
/// </summary>
public class DataTypeInfoDto
{
    public Guid? Id { get; set; }
    public string? Name { get; set; }
    public string? DisplayName { get; set; }
    public string? Category { get; set; }
    public string? UiComponent { get; set; }

    // Validation Rules - ALL FIELDS
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public int? DecimalPlaces { get; set; }
    public decimal? StepValue { get; set; }
    public bool? IsRequired { get; set; }

    // UI Properties - ALL FIELDS
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
    public string? Placeholder { get; set; }
    public string? HtmlAttributes { get; set; }

    // Choice Options - ALL FIELDS
    public string? DefaultOptions { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }
    public int? MaxSelections { get; set; }

    // File/Media Properties - ALL FIELDS
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSizeBytes { get; set; }

    // Error Messages - ALL FIELDS
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }
    public string? FileSizeErrorMessage { get; set; }

    // Status
    public bool? IsActive { get; set; }
}

/// <summary>
/// Metadata link information DTO - COMPLETE with ALL properties
/// </summary>
public class MetadataLinkInfoDto
{
    /// <summary>
    /// The ID of the specific metadata link (ProductMetadataId or ObjectMetadataId - FeatureMetadataId removed)
    /// </summary>
    public Guid? ObjectMetaDataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the entity
    /// </summary>
    public bool IsUnique { get; set; }

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Visibility settings - ALL FIELDS from ProductMetadata/ObjectMetadata (FeatureMetadata removed)
    /// </summary>
    public bool? ShouldVisibleInList { get; set; }
    public bool? ShouldVisibleInEdit { get; set; }
    public bool? ShouldVisibleInCreate { get; set; }
    public bool? ShouldVisibleInView { get; set; }
    public bool? IsCalculate { get; set; }
}

/// <summary>
/// Value information DTO with hierarchy support
/// </summary>
public class ValueInfoDto
{
    public Guid Id { get; set; }
    public Guid RefId { get; set; }
    public string? Value { get; set; }

    /// <summary>
    /// Parent object value ID (for nested values)
    /// </summary>
    public Guid? ParentObjectValueId { get; set; }
}


