using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// TenantLookup entity for tenant-specific lookup values
/// Represents lookup values that are specific to individual tenants
/// </summary>
public class TenantLookup : AuditableEntity, IAggregateRoot
{ 
    /// <summary>
    /// Foreign key to the TenantContext
    /// </summary>
    public Guid TenantContextId { get; set; }

    /// <summary>
    /// Primary value of the tenant lookup item
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default value for the tenant context
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Additional value field 1 (for extended data)
    /// </summary>
    public string? Value1 { get; set; }

    /// <summary>
    /// Additional value field 2 (for extended data)
    /// </summary>
    public string? Value2 { get; set; }

    /// <summary>
    /// Display sequence order
    /// </summary>
    public int ShowSequence { get; set; } = 0;
  
    /// <summary>
    /// Navigation property to the parent TenantContext
    /// </summary>
    public virtual TenantContext TenantContext { get; set; } = null!;
}
