using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Specification to get ObjectMetadata by ObjectId and Name (prioritizes Name over DisplayLabel)
/// </summary>
public class ObjectMetadataByObjectIdAndMetadataKeySpec : Specification<ObjectMetadata>, ISingleResultSpecification<ObjectMetadata>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ObjectMetadataByObjectIdAndMetadataKeySpec(Guid objectId, string metadataKey, bool onlyActive = true)
    {
        Query.Where(om => om.ObjectId == objectId &&
                         !om.IsDeleted);

        // Prioritize Name matching first, then fallback to DisplayLabel
        Query.Where(om => om.Metadata.Name == metadataKey ||
                         (om.Metadata.Name != null && om.Metadata.Name.ToLower() == metadataKey.ToLower()) ||
                         (om.Metadata.Name != null && om.Metadata.Name.Contains(metadataKey)) ||
                         (om.Metadata.DisplayLabel != null && om.Metadata.DisplayLabel.ToLower() == metadataKey.ToLower()) ||
                         (om.Metadata.DisplayLabel != null && om.Metadata.DisplayLabel.Contains(metadataKey)));

        if (onlyActive)
        {
            Query.Where(om => om.IsActive && om.Object.IsActive && om.Metadata.IsVisible != false);
        }

        // Include related data
        Query.Include(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        Query.Include(om => om.Object);

        // Order by Name exact match first, then case match, then DisplayLabel matches
        Query.OrderBy(om => om.Metadata.Name == metadataKey ? 0 :
                           (om.Metadata.Name != null && om.Metadata.Name.ToLower() == metadataKey.ToLower()) ? 1 :
                           (om.Metadata.DisplayLabel != null && om.Metadata.DisplayLabel.ToLower() == metadataKey.ToLower()) ? 2 : 3);

        // Take only one record
        Query.Take(1);
    }
}
