// COMMENTED OUT - Contains FeatureValue entity references that have been removed
// TODO: Update this handler to remove FeatureValue references or remove entirely if not needed

/*
using Abstraction.Common;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.Common.Commands;

/// <summary>
/// Handler for dynamic value operation commands
/// </summary>
public class DynamicValueOperationCommandHandler : 
    IRequestHandler<DynamicValueOperationCommand, Result<DynamicOperationResponse>>,
    IRequestHandler<BulkDynamicValueOperationCommand, Result<DynamicOperationResponse>>
{
    private readonly IDynamicRepository<ProductValue> _productValueRepository;
    private readonly IDynamicRepository<FeatureValue> _featureValueRepository;
    private readonly IDynamicRepository<ObjectValue> _objectValueRepository;
    private readonly IDynamicRepository<UserValue> _userValueRepository;
    private readonly IDynamicRepository<RoleValue> _roleValueRepository;
    private readonly IDynamicRepository<SubscriptionValue> _subscriptionValueRepository;
    private readonly ILogger<DynamicValueOperationCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public DynamicValueOperationCommandHandler(
        IDynamicRepository<ProductValue> productValueRepository,
        IDynamicRepository<FeatureValue> featureValueRepository,
        IDynamicRepository<ObjectValue> objectValueRepository,
        IDynamicRepository<UserValue> userValueRepository,
        IDynamicRepository<RoleValue> roleValueRepository,
        IDynamicRepository<SubscriptionValue> subscriptionValueRepository,
        ILogger<DynamicValueOperationCommandHandler> logger)
    {
        _productValueRepository = productValueRepository;
        _featureValueRepository = featureValueRepository;
        _objectValueRepository = objectValueRepository;
        _userValueRepository = userValueRepository;
        _roleValueRepository = roleValueRepository;
        _subscriptionValueRepository = subscriptionValueRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle single dynamic value operation command
    /// </summary>
    public async Task<Result<DynamicOperationResponse>> Handle(DynamicValueOperationCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Processing dynamic value operation: {ValueEntityType} - {OperationType} - {Count} items", 
                request.ValueEntityType, request.OperationType, request.ValueData.Count);

            var response = new DynamicOperationResponse
            {
                Success = true,
                Message = $"Dynamic value {request.OperationType} operation completed successfully"
            };

            switch (request.ValueEntityType.ToLowerInvariant())
            {
                case "productvalue":
                    await ProcessValueOperation(_productValueRepository, request, response, cancellationToken);
                    break;
                case "featurevalue":
                    await ProcessValueOperation(_featureValueRepository, request, response, cancellationToken);
                    break;
                case "objectvalue":
                    await ProcessValueOperation(_objectValueRepository, request, response, cancellationToken);
                    break;
                case "uservalue":
                    await ProcessValueOperation(_userValueRepository, request, response, cancellationToken);
                    break;
                case "rolevalue":
                    await ProcessValueOperation(_roleValueRepository, request, response, cancellationToken);
                    break;
                case "subscriptionvalue":
                    await ProcessValueOperation(_subscriptionValueRepository, request, response, cancellationToken);
                    break;
                default:
                    return Result<DynamicOperationResponse>.Failure($"Unsupported value entity type: {request.ValueEntityType}");
            }

            stopwatch.Stop();
            response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            response.ProcessedCount = request.ValueData.Count;

            _logger.LogInformation("Dynamic value operation completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return Result<DynamicOperationResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing dynamic value operation for {ValueEntityType}", request.ValueEntityType);
            return Result<DynamicOperationResponse>.Failure($"Error processing dynamic value operation: {ex.Message}");
        }
    }

    /// <summary>
    /// Handle bulk dynamic value operation command
    /// </summary>
    public async Task<Result<DynamicOperationResponse>> Handle(BulkDynamicValueOperationCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Processing bulk dynamic value operation: {ValueEntityType} - {OperationType} - {Count} items", 
                request.ValueEntityType, request.OperationType, request.ValueDataList.Count);

            var response = new DynamicOperationResponse
            {
                Success = true,
                Message = $"Bulk dynamic value {request.OperationType} operation completed successfully"
            };

            switch (request.ValueEntityType.ToLowerInvariant())
            {
                case "productvalue":
                    await ProcessBulkValueOperation(_productValueRepository, request, response, cancellationToken);
                    break;
                case "featurevalue":
                    await ProcessBulkValueOperation(_featureValueRepository, request, response, cancellationToken);
                    break;
                case "objectvalue":
                    await ProcessBulkValueOperation(_objectValueRepository, request, response, cancellationToken);
                    break;
                case "uservalue":
                    await ProcessBulkValueOperation(_userValueRepository, request, response, cancellationToken);
                    break;
                case "rolevalue":
                    await ProcessBulkValueOperation(_roleValueRepository, request, response, cancellationToken);
                    break;
                case "subscriptionvalue":
                    await ProcessBulkValueOperation(_subscriptionValueRepository, request, response, cancellationToken);
                    break;
                default:
                    return Result<DynamicOperationResponse>.Failure($"Unsupported value entity type: {request.ValueEntityType}");
            }

            stopwatch.Stop();
            response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
            response.ProcessedCount = request.ValueDataList.Count;

            _logger.LogInformation("Bulk dynamic value operation completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return Result<DynamicOperationResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing bulk dynamic value operation for {ValueEntityType}", request.ValueEntityType);
            return Result<DynamicOperationResponse>.Failure($"Error processing bulk dynamic value operation: {ex.Message}");
        }
    }

    /// <summary>
    /// Process value operation for specific repository
    /// </summary>
    private async Task ProcessValueOperation<T>(
        IDynamicRepository<T> repository,
        DynamicValueOperationCommand request,
        DynamicOperationResponse response,
        CancellationToken cancellationToken) where T : class, Domain.Common.Contracts.IAggregateRoot
    {
        // Process each value item individually
        var processedValueIds = new List<Guid>();
        foreach (var valueDict in request.ValueData)
        {
            try
            {
                // Add RefId to the value data if provided
                if (request.RefId.HasValue)
                {
                    valueDict["RefId"] = request.RefId.Value;
                }

                switch (request.OperationType)
                {
                    case DynamicOperationType.Insert:
                        var insertedEntity = await repository.DynamicInsertAsync(valueDict, cancellationToken);
                        response.InsertedCount++;
                        response.CreatedIds.Add(((Domain.Common.Contracts.IEntity<Guid>)insertedEntity).Id);
                        break;

                    case DynamicOperationType.Update:
                        var updatedEntity = await repository.DynamicUpdateAsync(valueDict, cancellationToken);
                        response.UpdatedCount++;
                        response.CreatedIds.Add(((Domain.Common.Contracts.IEntity<Guid>)updatedEntity).Id);
                        break;

                    case DynamicOperationType.Upsert:
                        var upsertedEntity = await repository.DynamicUpsertAsync(valueDict, cancellationToken);
                        response.InsertedCount += repository.GetLastUpsertInsertedCount();
                        response.UpdatedCount += repository.GetLastUpsertUpdatedCount();
                        response.CreatedIds.Add(((Domain.Common.Contracts.IEntity<Guid>)upsertedEntity).Id);
                        break;

                    default:
                        throw new NotSupportedException($"Operation type {request.OperationType} not supported for value operations");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process individual value item for {ValueEntityType}", request.ValueEntityType);
                // Continue processing other items
            }
        }
    }

    /// <summary>
    /// Process bulk value operation for specific repository
    /// </summary>
    private async Task ProcessBulkValueOperation<T>(
        IDynamicRepository<T> repository,
        BulkDynamicValueOperationCommand request,
        DynamicOperationResponse response,
        CancellationToken cancellationToken) where T : class, Domain.Common.Contracts.IAggregateRoot
    {
        // Add RefId to all value data items if provided
        var processedValueData = request.ValueDataList.ToList();
        if (request.RefId.HasValue)
        {
            foreach (var valueDict in processedValueData)
            {
                valueDict["RefId"] = request.RefId.Value;
            }
        }

        switch (request.OperationType)
        {
            case DynamicOperationType.BulkInsert:
                var insertedEntities = await repository.DynamicBulkInsertAsync(processedValueData, cancellationToken);
                response.InsertedCount = insertedEntities.Count();
                response.CreatedIds.AddRange(insertedEntities.Select(e => ((Domain.Common.Contracts.IEntity<Guid>)e).Id));
                break;

            case DynamicOperationType.BulkUpsert:
            case DynamicOperationType.Upsert:
                var upsertedEntities = await repository.DynamicBulkUpsertAsync(processedValueData, cancellationToken);
                response.InsertedCount = repository.GetLastUpsertInsertedCount();
                response.UpdatedCount = repository.GetLastUpsertUpdatedCount();
                response.CreatedIds.AddRange(upsertedEntities.Select(e => ((Domain.Common.Contracts.IEntity<Guid>)e).Id));
                break;

            default:
                throw new NotSupportedException($"Operation type {request.OperationType} not supported for bulk value operations");
        }
    }
}
*/
