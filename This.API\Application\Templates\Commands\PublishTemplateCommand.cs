using Application.Templates.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Templates.Commands;

/// <summary>
/// Command to publish a template
/// </summary>
public class PublishTemplateCommand : IRequest<Result<TemplateDto>>
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public PublishTemplateCommand(Guid id)
    {
        Id = id;
    }
}
