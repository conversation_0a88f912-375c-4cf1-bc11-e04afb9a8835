import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// Option model for dropdown input
class DropdownOption {
  final String value;
  final String label;
  final String? description;
  final bool enabled;
  final Widget? icon;

  const DropdownOption({
    required this.value,
    required this.label,
    this.description,
    this.enabled = true,
    this.icon,
  });
}

/// A customizable dropdown input widget following the 'this_componentName_input' naming convention
/// This widget handles dropdown selection with validation based on API configuration
class ThisDropdownInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final dynamic value; // Can be String or List<String> for multiple
  final ValueChanged<dynamic> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  
  // API-based validation parameters
  final String? validationPattern;
  final int? minLength;
  final int? maxLength;
  final String? inputMask;
  final String? requiredErrorMessage;
  final String? patternErrorMessage;
  final String? minLengthErrorMessage;
  final String? maxLengthErrorMessage;
  
  // Dropdown-specific parameters from API
  final List<DropdownOption> options;
  final bool allowsMultiple;
  final bool allowsCustomOptions;
  final int? maxSelections;
  final bool searchable;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool autoFocus;
  final String? Function(dynamic)? customValidation;

  const ThisDropdownInput({
    super.key,
    required this.id,
    required this.label,
    required this.options,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.validationPattern,
    this.minLength,
    this.maxLength,
    this.inputMask,
    this.requiredErrorMessage,
    this.patternErrorMessage,
    this.minLengthErrorMessage,
    this.maxLengthErrorMessage,
    this.allowsMultiple = false,
    this.allowsCustomOptions = false,
    this.maxSelections,
    this.searchable = false,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisDropdownInput> createState() => _ThisDropdownInputState();
}

class _ThisDropdownInputState extends State<ThisDropdownInput> {
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<String> get _selectedValues {
    if (widget.allowsMultiple) {
      return widget.value is List<String> ? widget.value : [];
    } else {
      return widget.value != null ? [widget.value.toString()] : [];
    }
  }

  List<DropdownOption> get _filteredOptions {
    if (!widget.searchable || _searchQuery.isEmpty) {
      return widget.options.where((option) => option.enabled).toList();
    }
    
    return widget.options.where((option) => 
      option.enabled && 
      (option.label.toLowerCase().contains(_searchQuery.toLowerCase()) ||
       option.value.toLowerCase().contains(_searchQuery.toLowerCase()))
    ).toList();
  }

  List<String> _validateValue(dynamic value) {
    final errors = <String>[];
    final selectedValues = _selectedValues;

    // 1. Required validation
    if (widget.required && selectedValues.isEmpty) {
      errors.add(widget.requiredErrorMessage ?? '${widget.label} is required');
      return errors;
    }

    // Skip other validations if empty and not required
    if (selectedValues.isEmpty && !widget.required) {
      return errors;
    }

    // 2. Multiple selection validation
    if (widget.allowsMultiple) {
      // Max selections validation
      if (widget.maxSelections != null && selectedValues.length > widget.maxSelections!) {
        errors.add('Maximum ${widget.maxSelections} selections allowed');
        return errors;
      }

      // Min selections validation (if specified)
      if (widget.minLength != null && selectedValues.length < widget.minLength!) {
        errors.add(widget.minLengthErrorMessage ?? 'Minimum ${widget.minLength} selections required');
        return errors;
      }

      // Max selections validation (if specified)
      if (widget.maxLength != null && selectedValues.length > widget.maxLength!) {
        errors.add(widget.maxLengthErrorMessage ?? 'Maximum ${widget.maxLength} selections allowed');
        return errors;
      }
    }

    // 3. Valid option validation
    for (final selectedValue in selectedValues) {
      final isValidOption = widget.options.any((option) => option.value == selectedValue);
      if (!isValidOption && !widget.allowsCustomOptions) {
        errors.add('Invalid selection: $selectedValue');
        return errors;
      }
    }

    // 4. Pattern validation (for custom options)
    if (widget.validationPattern != null && widget.allowsCustomOptions) {
      final regex = RegExp(widget.validationPattern!);
      for (final selectedValue in selectedValues) {
        if (!regex.hasMatch(selectedValue)) {
          errors.add(widget.patternErrorMessage ?? 'Invalid format for: $selectedValue');
          return errors;
        }
      }
    }

    // 5. Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(value);
      if (customError != null) {
        errors.add(customError);
        return errors;
      }
    }

    return errors;
  }

  void _handleSelection(String optionValue) {
    dynamic newValue;
    
    if (widget.allowsMultiple) {
      final currentValues = List<String>.from(_selectedValues);
      
      if (currentValues.contains(optionValue)) {
        currentValues.remove(optionValue);
      } else {
        // Check max selections
        if (widget.maxSelections != null && currentValues.length >= widget.maxSelections!) {
          // Show error or replace last selection
          return;
        }
        currentValues.add(optionValue);
      }
      
      newValue = currentValues;
    } else {
      newValue = optionValue;
    }

    widget.onChanged(newValue);

    // Validate
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
      _isValidated = true;
    });
    
    widget.onValidation?.call(errors);
  }

  void _handleBlur() {
    if (widget.validateOnBlur) {
      final errors = _validateValue(widget.value);
      setState(() {
        _errors = errors;
        _isValidated = _selectedValues.isNotEmpty;
      });
      
      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated || _selectedValues.isEmpty) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  String _getDisplayText() {
    if (_selectedValues.isEmpty) {
      return widget.placeholder ?? 'Select option...';
    }

    if (widget.allowsMultiple) {
      if (_selectedValues.length == 1) {
        final option = widget.options.firstWhere(
          (opt) => opt.value == _selectedValues.first,
          orElse: () => DropdownOption(value: _selectedValues.first, label: _selectedValues.first),
        );
        return option.label;
      } else {
        return '${_selectedValues.length} items selected';
      }
    } else {
      final option = widget.options.firstWhere(
        (opt) => opt.value == _selectedValues.first,
        orElse: () => DropdownOption(value: _selectedValues.first, label: _selectedValues.first),
      );
      return option.label;
    }
  }

  Future<void> _showDropdownModal() async {
    if (widget.disabled || widget.readOnly) return;

    await showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalette.darkToneInk,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    widget.label,
                    style: LexendTextStyles.lexend16Bold.copyWith(
                      color: ColorPalette.white,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  color: ColorPalette.white,
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            
            // Search field
            if (widget.searchable) ...[
              const SizedBox(height: 16),
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search options...',
                  hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                  prefixIcon: Icon(Icons.search, color: ColorPalette.placeHolderTextColor),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(6),
                    borderSide: BorderSide(color: ColorPalette.gray300),
                  ),
                ),
                style: LexendTextStyles.lexend14Regular.copyWith(
                  color: ColorPalette.white,
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Options list
            Expanded(
              child: ListView.builder(
                itemCount: _filteredOptions.length,
                itemBuilder: (context, index) {
                  final option = _filteredOptions[index];
                  final isSelected = _selectedValues.contains(option.value);
                  
                  return ListTile(
                    leading: option.icon,
                    title: Text(
                      option.label,
                      style: LexendTextStyles.lexend14Regular.copyWith(
                        color: ColorPalette.white,
                      ),
                    ),
                    subtitle: option.description != null
                        ? Text(
                            option.description!,
                            style: LexendTextStyles.lexend12Regular.copyWith(
                              color: ColorPalette.placeHolderTextColor,
                            ),
                          )
                        : null,
                    trailing: widget.allowsMultiple
                        ? Checkbox(
                            value: isSelected,
                            onChanged: (_) => _handleSelection(option.value),
                            activeColor: ColorPalette.green,
                          )
                        : isSelected
                            ? Icon(Icons.check, color: ColorPalette.green)
                            : null,
                    onTap: () {
                      _handleSelection(option.value);
                      if (!widget.allowsMultiple) {
                        Navigator.of(context).pop();
                      }
                    },
                    selected: isSelected,
                    selectedTileColor: ColorPalette.white.withValues(alpha: 0.1),
                  );
                },
              ),
            ),
            
            // Multiple selection summary
            if (widget.allowsMultiple && _selectedValues.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: ColorPalette.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: ColorPalette.green),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: ColorPalette.green, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${_selectedValues.length} item${_selectedValues.length == 1 ? '' : 's'} selected',
                        style: LexendTextStyles.lexend12Regular.copyWith(
                          color: ColorPalette.white,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        'Done',
                        style: LexendTextStyles.lexend12Bold.copyWith(
                          color: ColorPalette.green,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
    
    _handleBlur();
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors && _selectedValues.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled 
                    ? ColorPalette.placeHolderTextColor 
                    : ColorPalette.white,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        
        // Dropdown Field
        GestureDetector(
          onTap: _showDropdownModal,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(
                color: hasErrors 
                    ? const Color(0xFFC73E1D)
                    : (isValid ? ColorPalette.green : ColorPalette.gray300),
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                if (widget.showIcon) ...[
                  Icon(
                    Icons.arrow_drop_down,
                    size: 20,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: Text(
                    _getDisplayText(),
                    style: LexendTextStyles.lexend14Regular.copyWith(
                      color: _selectedValues.isEmpty
                          ? ColorPalette.placeHolderTextColor
                          : (widget.disabled 
                              ? ColorPalette.placeHolderTextColor 
                              : ColorPalette.white),
                    ),
                  ),
                ),
                if (_getValidationIcon() != null) _getValidationIcon()!,
              ],
            ),
          ),
        ),
        
        // Error message
        if (hasErrors)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              _errors.first,
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
            ),
          ),
        
        // Helper text
        if (!hasErrors && widget.allowsMultiple && widget.maxSelections != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Maximum ${widget.maxSelections} selections allowed',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          ),
      ],
    );
  }
}
