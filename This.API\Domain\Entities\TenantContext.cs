using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// TenantContext entity for tenant-specific lookup categories
/// Represents lookup contexts that are specific to individual tenants
/// </summary>
public class TenantContext : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Name of the tenant context (e.g., "Custom Status", "Department")
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the tenant context
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Category for grouping tenant contexts
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Navigation property for related tenant lookups
    /// </summary>
    public virtual ICollection<TenantLookup> TenantLookups { get; set; } = new List<TenantLookup>();

    /// <summary>
    /// Navigation property for DataTypes that use this tenant context
    /// </summary>
    public virtual ICollection<DataType> DataTypes { get; set; } = new List<DataType>();

    /// <summary>
    /// Navigation property for Metadata that override with this tenant context
    /// </summary>
    public virtual ICollection<Metadata> MetadataOverrides { get; set; } = new List<Metadata>();
}
