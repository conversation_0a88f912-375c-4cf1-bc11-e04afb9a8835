using Abstraction.Database.Repositories;
using Application.Common.DataType;
using Application.Comprehensive.DTOs;
using Application.MetadataManagement.Specifications;
using Application.Objects.DTOs;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.Comprehensive.Commands;

/// <summary>
/// Handler for creating comprehensive product structure
/// </summary>
public partial class CreateProductStructureCommandHandler : IRequestHandler<CreateProductStructureCommand, Result<ProductStructureCreationResult>>
{
    private readonly IRepositoryWithEvents<Product> _productRepository;
    private readonly IRepositoryWithEvents<Domain.Entities.Object> _objectRepository;
    private readonly IRepositoryWithEvents<Metadata> _metadataRepository;
    private readonly IRepositoryWithEvents<DataType> _dataTypeRepository;
    private readonly IRepositoryWithEvents<ObjectMetadata> _objectMetadataRepository;
    private readonly ILogger<CreateProductStructureCommandHandler> _logger;

    public CreateProductStructureCommandHandler(
        IRepositoryWithEvents<Product> productRepository,
        IRepositoryWithEvents<Domain.Entities.Object> objectRepository,
        IRepositoryWithEvents<Metadata> metadataRepository,
        IRepositoryWithEvents<DataType> dataTypeRepository,
        IRepositoryWithEvents<ObjectMetadata> objectMetadataRepository,
        ILogger<CreateProductStructureCommandHandler> logger)
    {
        _productRepository = productRepository;
        _objectRepository = objectRepository;
        _metadataRepository = metadataRepository;
        _dataTypeRepository = dataTypeRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _logger = logger;
    }

    public async Task<Result<ProductStructureCreationResult>> Handle(
        CreateProductStructureCommand request, 
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ProductStructureCreationResult();

        try
        {
            _logger.LogInformation("Starting comprehensive product structure creation for {ProductCount} products",
                request.Products?.Count ?? 0);

            // Phase 1: Validation
            var validationResult = await ValidateProductStructureAsync(request.Products, cancellationToken);
            if (!validationResult.Success)
            {
                result.Success = false;
                result.Errors.AddRange(validationResult.Errors);
                result.Message = "Validation failed";
                return Result<ProductStructureCreationResult>.Failure(result.Message);
            }

            // Phase 2: Initialize processing context
            var context = new ProductStructureProcessingContext
            {
                AllDataTypes = await GetAllDataTypesAsync(cancellationToken),
                AllMetadata = new Dictionary<string, Metadata>(),
                CreatedProducts = new List<Product>(),
                CreatedObjects = new List<Domain.Entities.Object>(),
                CreatedMetadata = new List<Metadata>(),
                CreatedObjectMetadata = new List<ObjectMetadata>(),
                ExternalIdMappings = new Dictionary<string, Guid>(),
                DatabaseQueriesCount = 1 // For GetAllDataTypesAsync
            };

            // Phase 3: Process all products
            foreach (var productDto in request.Products)
            {
                await ProcessProductAsync(productDto, context, cancellationToken);
            }

            // Phase 4: Batch database operations
            await ExecuteBatchOperationsAsync(context, cancellationToken);

            // Phase 5: Build success response
            BuildSuccessResponse(result, context);

            stopwatch.Stop();
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            result.Metrics.TotalProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Product structure creation completed successfully. " +
                "Products: {ProductCount}, Objects: {ObjectCount}, Metadata: {MetadataCount}, Time: {TimeMs}ms",
                result.TotalProductsCreated, result.TotalObjectsCreated,
                result.TotalMetadataCreated, result.ProcessingTimeMs);

            return Result<ProductStructureCreationResult>.Success(result, result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during product structure creation");

            result.Success = false;
            result.Errors.Add($"Internal error: {ex.Message}");
            result.Message = "Processing failed due to internal error";

            stopwatch.Stop();
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return Result<ProductStructureCreationResult>.Failure(result.Message);
        }
    }

    #region Private Helper Methods

    private async Task<ProductStructureCreationResult> ValidateProductStructureAsync(
        List<ProductStructureDto> products,
        CancellationToken cancellationToken)
    {
        var result = new ProductStructureCreationResult();
        var errors = new List<string>();

        try
        {
            // Validate request structure
            if (products == null || !products.Any())
            {
                errors.Add("At least one product must be provided");
            }
            else
            {
                // Validate each product
                foreach (var product in products)
                {
                    ValidateProductStructure(product, errors);
                }

                // Validate unique product names
                var productNames = products.Select(p => p.Name.ToLowerInvariant()).ToList();
                var duplicateNames = productNames.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key);
                foreach (var duplicateName in duplicateNames)
                {
                    errors.Add($"Duplicate product name found: '{duplicateName}'");
                }
            }

            result.Success = !errors.Any();
            result.Errors = errors;
            result.Message = result.Success ? "Validation passed" : "Validation failed";

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during product structure validation");
            result.Success = false;
            result.Errors.Add($"Validation error: {ex.Message}");
            result.Message = "Validation failed due to internal error";
            return result;
        }
    }

    private async Task<Dictionary<string, DataType>> GetAllDataTypesAsync(CancellationToken cancellationToken)
    {
        var dataTypes = await _dataTypeRepository.ListAsync(cancellationToken);
        return dataTypes.ToDictionary(dt => dt.Name.ToLowerInvariant(), dt => dt);
    }

    private async Task ProcessProductAsync(
        ProductStructureDto productDto,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        // Create the product entity
        var productEntity = new Product
        {
            Id = Guid.NewGuid(),
            Name = productDto.Name,
            Description = productDto.Description,
            Version = productDto.Version ?? "1.0.0",
            IsActive = productDto.IsActive,
            CreatedAt = DateTime.UtcNow,
            ModifiedAt = DateTime.UtcNow
        };

        context.CreatedProducts.Add(productEntity);

        // Map external ID to internal ID
        if (!string.IsNullOrEmpty(productDto.Id))
        {
            context.ExternalIdMappings[productDto.Id] = productEntity.Id;
        }

        // Process product metadata
        if (productDto.Metadata?.Any() == true)
        {
            foreach (var metadataDto in productDto.Metadata)
            {
                await ProcessMetadataAsync(metadataDto, context, cancellationToken);
            }
        }

        // Process product objects
        if (productDto.Objects?.Any() == true)
        {
            foreach (var objectDto in productDto.Objects)
            {
                await ProcessObjectAsync(objectDto, productEntity.Id, null, 0, context, cancellationToken);
            }
        }
    }

    private async Task ProcessObjectAsync(
        ObjectStructureDto objectDto,
        Guid productId,
        Guid? parentObjectId,
        int level,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        // Create the object entity
        var objectEntity = new Domain.Entities.Object
        {
            Id = Guid.NewGuid(),
            Name = objectDto.Name,
            Description = objectDto.Description,
            ProductId = productId,
            ParentObjectId = parentObjectId,
            IsActive = objectDto.IsActive,
            CreatedAt = DateTime.UtcNow,
            ModifiedAt = DateTime.UtcNow
        };

        context.CreatedObjects.Add(objectEntity);

        // Map external ID to internal ID
        if (!string.IsNullOrEmpty(objectDto.Id))
        {
            context.ExternalIdMappings[objectDto.Id] = objectEntity.Id;
        }

        // Process object metadata
        if (objectDto.Metadata?.Any() == true)
        {
            foreach (var metadataDto in objectDto.Metadata)
            {
                var metadata = await ProcessMetadataAsync(metadataDto, context, cancellationToken);
                if (metadata != null)
                {
                    // Check if ObjectMetadata relationship already exists
                    var objectMetadataKey = $"{objectEntity.Id}_{metadata.Id}";
                    if (!context.CreatedObjectMetadataKeys.Contains(objectMetadataKey))
                    {
                        // Create ObjectMetadata link
                        var objectMetadata = new ObjectMetadata
                        {
                            Id = Guid.NewGuid(),
                            ObjectId = objectEntity.Id,
                            MetadataId = metadata.Id,
                            IsUnique = false,
                            IsActive = true,
                            IsVisibleInList = true,
                            IsVisibleInEdit = true,
                            IsVisibleInCreate = true,
                            IsVisibleInView = true,
                            IsCalculated = true,
                            CreatedAt = DateTime.UtcNow,
                            ModifiedAt = DateTime.UtcNow
                        };

                        context.CreatedObjectMetadata.Add(objectMetadata);
                        context.CreatedObjectMetadataKeys.Add(objectMetadataKey);
                    }
                    else
                    {
                        _logger.LogWarning("Skipping duplicate ObjectMetadata relationship for Object '{ObjectId}' and Metadata '{MetadataId}'",
                            objectEntity.Id, metadata.Id);
                    }
                }
            }
        }

        // Process child objects recursively
        if (objectDto.Objects?.Any() == true)
        {
            foreach (var childObject in objectDto.Objects)
            {
                await ProcessObjectAsync(childObject, productId, objectEntity.Id, level + 1, context, cancellationToken);
            }
        }
    }

    private async Task<Metadata?> ProcessMetadataAsync(
        MetadataStructureDto metadataDto,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        // Create a unique key for this specific metadata configuration
        var metadataKey = $"{metadataDto.Name}_{metadataDto.Type}_{metadataDto.IsVisible}_{metadataDto.IsReadonly}".ToLowerInvariant();

        // Check if metadata with same configuration already exists in context
        if (context.AllMetadata.TryGetValue(metadataKey, out var existingMetadata))
        {
            return existingMetadata;
        }

        // For database lookup, we'll look for metadata with same name and data type
        // Since Name is not unique, we might get multiple results, so we'll take the first compatible one
        var dataTypeName = MapDataTypeName(metadataDto.Type);
        if (!context.AllDataTypes.TryGetValue(dataTypeName.ToLowerInvariant(), out var dataType))
        {
            _logger.LogWarning("DataType '{DataTypeName}' not found for metadata '{MetadataName}', using 'text' as fallback",
                dataTypeName, metadataDto.Name);
            dataType = context.AllDataTypes.GetValueOrDefault("text") ?? context.AllDataTypes.Values.First();
        }

        // Check if compatible metadata exists in database (same name and data type)
        var metadataSpec = new MetadataByKeySpec(metadataDto.Name);
        var dbMetadataList = await _metadataRepository.ListAsync(metadataSpec, cancellationToken);
        context.DatabaseQueriesCount++;

        var dbMetadata = dbMetadataList.FirstOrDefault(m => m.DataTypeId == dataType.Id);
        if (dbMetadata != null)
        {
            context.AllMetadata[metadataKey] = dbMetadata;
            return dbMetadata;
        }

        // Create new metadata
        var newMetadata = new Metadata
        {
            Id = Guid.NewGuid(),
            Name = metadataDto.Name,
            DataTypeId = dataType.Id,
            DisplayLabel = metadataDto.Name,
            HelpText = metadataDto.Description,
            IsVisible = metadataDto.IsVisible,
            IsReadonly = metadataDto.IsReadonly,
            CreatedAt = DateTime.UtcNow,
            ModifiedAt = DateTime.UtcNow
        };

        context.CreatedMetadata.Add(newMetadata);
        context.AllMetadata[metadataKey] = newMetadata;

        return newMetadata;
    }

    private void ValidateProductStructure(ProductStructureDto product, List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(product.Name))
        {
            errors.Add("Product name is required");
        }

        // Validate objects if present
        if (product.Objects?.Any() == true)
        {
            ValidateObjectsStructure(product.Objects, $"Product '{product.Name}'", 0, errors);
        }
    }

    private void ValidateObjectsStructure(List<ObjectStructureDto> objects, string parentPath, int depth, List<string> errors)
    {
        if (depth > 10) // Prevent excessive nesting
        {
            errors.Add($"Maximum hierarchy depth (10) exceeded at path '{parentPath}'");
            return;
        }

        var names = new HashSet<string>();
        foreach (var obj in objects)
        {
            if (string.IsNullOrWhiteSpace(obj.Name))
            {
                errors.Add($"Object name is required at path '{parentPath}'");
                continue;
            }

            if (!names.Add(obj.Name.ToLowerInvariant()))
            {
                errors.Add($"Duplicate object name '{obj.Name}' found at path '{parentPath}'");
            }

            // Validate child objects recursively
            if (obj.Objects?.Any() == true)
            {
                var currentPath = $"{parentPath}/{obj.Name}";
                ValidateObjectsStructure(obj.Objects, currentPath, depth + 1, errors);
            }
        }
    }

    private async Task ExecuteBatchOperationsAsync(
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        // Add metadata first (dependencies) - handle duplicates gracefully
        if (context.CreatedMetadata.Any())
        {
            try
            {
                await _metadataRepository.AddRangeAsync(context.CreatedMetadata, cancellationToken);
                context.DatabaseQueriesCount++;
            }
            catch (Exception ex) when (ex.Message.Contains("duplicate key") || ex.Message.Contains("IX_Metadata_MetadataKey"))
            {
                _logger.LogWarning("Duplicate key error when saving metadata batch, attempting individual saves");

                // If batch fails due to duplicates, try saving individually
                var successfulMetadata = new List<Metadata>();
                foreach (var metadata in context.CreatedMetadata)
                {
                    try
                    {
                        await _metadataRepository.AddAsync(metadata, cancellationToken);
                        successfulMetadata.Add(metadata);
                        context.DatabaseQueriesCount++;
                    }
                    catch (Exception individualEx) when (individualEx.Message.Contains("duplicate key") || individualEx.Message.Contains("IX_Metadata_MetadataKey"))
                    {
                        _logger.LogWarning("Skipping duplicate metadata '{MetadataName}'", metadata.Name);
                        context.DatabaseQueriesCount++;
                    }
                }

                // Update the created metadata list to only include successful ones
                context.CreatedMetadata.Clear();
                context.CreatedMetadata.AddRange(successfulMetadata);
            }
        }
        // Add products
        if (context.CreatedProducts.Any())
        {
            await _productRepository.AddRangeAsync(context.CreatedProducts, cancellationToken);
            context.DatabaseQueriesCount++;
        }

        // Add objects
        if (context.CreatedObjects.Any())
        {
            await _objectRepository.AddRangeAsync(context.CreatedObjects, cancellationToken);
            context.DatabaseQueriesCount++;
        }

        // Add object-metadata links - handle duplicates gracefully
        if (context.CreatedObjectMetadata.Any())
        {
            try
            {
                await _objectMetadataRepository.AddRangeAsync(context.CreatedObjectMetadata, cancellationToken);
                context.DatabaseQueriesCount++;
            }
            catch (Exception ex) when (ex.Message.Contains("duplicate key") || ex.Message.Contains("IX_ObjectMetadata_ObjectId_MetadataId"))
            {
                _logger.LogWarning("Duplicate key error when saving ObjectMetadata batch, attempting individual saves");

                // If batch fails due to duplicates, try saving individually
                var successfulObjectMetadata = new List<ObjectMetadata>();
                foreach (var objectMetadata in context.CreatedObjectMetadata)
                {
                    try
                    {
                        await _objectMetadataRepository.AddAsync(objectMetadata, cancellationToken);
                        successfulObjectMetadata.Add(objectMetadata);
                        context.DatabaseQueriesCount++;
                    }
                    catch (Exception individualEx) when (individualEx.Message.Contains("duplicate key") || individualEx.Message.Contains("IX_ObjectMetadata_ObjectId_MetadataId"))
                    {
                        _logger.LogWarning("Skipping duplicate ObjectMetadata for Object '{ObjectId}' and Metadata '{MetadataId}'",
                            objectMetadata.ObjectId, objectMetadata.MetadataId);
                        context.DatabaseQueriesCount++;
                    }
                }

                // Update the created object metadata list to only include successful ones
                context.CreatedObjectMetadata.Clear();
                context.CreatedObjectMetadata.AddRange(successfulObjectMetadata);
            }
        }
    }

    private void BuildSuccessResponse(
        ProductStructureCreationResult result,
        ProductStructureProcessingContext context)
    {
        result.Success = true;
        result.Message = "Product structure created successfully";

        result.TotalProductsCreated = context.CreatedProducts.Count;
        result.TotalObjectsCreated = context.CreatedObjects.Count;
        result.TotalMetadataCreated = context.CreatedMetadata.Count;
        result.TotalObjectMetadataCreated = context.CreatedObjectMetadata.Count;

        // Build product response structure
        foreach (var product in context.CreatedProducts)
        {
            var productInfo = BuildCreatedProductInfo(product, context);
            result.CreatedProducts.Add(productInfo);
        }

        // Set metrics
        result.Metrics.DatabaseQueriesCount = context.DatabaseQueriesCount;
        result.Metrics.MaxHierarchyDepth = CalculateMaxDepth(context.CreatedObjects);
    }

    private CreatedProductInfo BuildCreatedProductInfo(
        Product product,
        ProductStructureProcessingContext context)
    {
        var productObjects = context.CreatedObjects.Where(o => o.ProductId == product.Id).ToList();
        var rootObjects = productObjects.Where(o => o.ParentObjectId == null).ToList();

        var productInfo = new CreatedProductInfo
        {
            ProductId = product.Id,
            Name = product.Name,
            ExternalId = context.ExternalIdMappings.FirstOrDefault(kvp => kvp.Value == product.Id).Key,
            ObjectsCount = productObjects.Count,
            MetadataFieldsCount = context.CreatedMetadata.Count // This could be more specific per product
        };

        // Build root objects hierarchy
        foreach (var rootObject in rootObjects)
        {
            var objectInfo = BuildCreatedObjectInfo(rootObject, context, 0);
            productInfo.RootObjects.Add(objectInfo);
        }

        return productInfo;
    }

    private ProductStructureCreatedObjectInfo BuildCreatedObjectInfo(
        Domain.Entities.Object objectEntity,
        ProductStructureProcessingContext context,
        int level)
    {
        var childObjects = context.CreatedObjects.Where(o => o.ParentObjectId == objectEntity.Id).ToList();
        var objectMetadata = context.CreatedObjectMetadata.Where(om => om.ObjectId == objectEntity.Id).ToList();

        var objectInfo = new ProductStructureCreatedObjectInfo
        {
            ObjectId = objectEntity.Id,
            Name = objectEntity.Name,
            ExternalId = context.ExternalIdMappings.FirstOrDefault(kvp => kvp.Value == objectEntity.Id).Key,
            ParentObjectId = objectEntity.ParentObjectId,
            Level = level,
            MetadataFieldsCount = objectMetadata.Count
        };

        // Build child objects recursively
        foreach (var childObject in childObjects)
        {
            var childInfo = BuildCreatedObjectInfo(childObject, context, level + 1);
            objectInfo.Children.Add(childInfo);
        }

        return objectInfo;
    }

    private static int CalculateMaxDepth(List<Domain.Entities.Object> objects)
    {
        if (!objects.Any()) return 0;

        var rootObjects = objects.Where(o => o.ParentObjectId == null).ToList();
        return rootObjects.Any() ? rootObjects.Max(root => CalculateDepth(root, objects, 1)) : 0;
    }

    private static int CalculateDepth(Domain.Entities.Object obj, List<Domain.Entities.Object> allObjects, int currentDepth)
    {
        var children = allObjects.Where(o => o.ParentObjectId == obj.Id).ToList();
        return children.Any() ? children.Max(child => CalculateDepth(child, allObjects, currentDepth + 1)) : currentDepth;
    }

    private static string MapDataTypeName(string inputType)
    {
        return inputType?.ToLowerInvariant() switch
        {
            "string" or "text" => "text",
            "int" or "integer" or "number" => "number",
            "bool" or "boolean" => "boolean",
            "datetime" or "date" => "datetime",
            "email" => "email",
            "phone" => "phone",
            "url" => "url",
            "address" => "address",
            "currency" => "currency",
            "percentage" => "percentage",
            "file" => "file",
            "image" => "image",
            "select" => "select",
            "multiselect" => "multiselect",
            "radio" => "radio",
            "checkbox" => "checkbox",
            "textarea" => "textarea",
            "richtext" => "richtext",
            "color" => "color",
            "slider" => "slider",
            "rating" => "rating",
            "tag" => "tag",
            "guid" => "guid",
            "year" => "year",
            "month" => "month",
            "day" => "day",
            "time" => "time",
            _ => "text" // Default fallback
        };
    }

    #endregion
}

internal class ProductStructureProcessingContext
{
    public Dictionary<string, DataType> AllDataTypes { get; set; } = new();
    public Dictionary<string, Metadata> AllMetadata { get; set; } = new();
    public List<Product> CreatedProducts { get; set; } = new();
    public List<Domain.Entities.Object> CreatedObjects { get; set; } = new();
    public List<Metadata> CreatedMetadata { get; set; } = new();
    public List<ObjectMetadata> CreatedObjectMetadata { get; set; } = new();
    public HashSet<string> CreatedObjectMetadataKeys { get; set; } = new();
    public Dictionary<string, Guid> ExternalIdMappings { get; set; } = new();
    public int DatabaseQueriesCount { get; set; }
}

/// <summary>
/// Handler for validating product structure
/// </summary>
public class ValidateProductStructureQueryHandler : IRequestHandler<ValidateProductStructureQuery, Result<ProductStructureCreationResult>>
{
    private readonly ILogger<ValidateProductStructureQueryHandler> _logger;

    public ValidateProductStructureQueryHandler(
        ILogger<ValidateProductStructureQueryHandler> logger)
    {
        _logger = logger;
    }

    public async Task<Result<ProductStructureCreationResult>> Handle(
        ValidateProductStructureQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing product structure validation for {ProductCount} products",
                request.Products?.Count ?? 0);

            var result = new ProductStructureCreationResult();
            var errors = new List<string>();

            // Validate request structure
            if (request.Products == null || !request.Products.Any())
            {
                errors.Add("At least one product must be provided");
            }
            else
            {
                // Validate each product
                foreach (var product in request.Products)
                {
                    ValidateProductStructure(product, errors);
                }

                // Validate unique product names
                var productNames = request.Products.Select(p => p.Name.ToLowerInvariant()).ToList();
                var duplicateNames = productNames.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key);
                foreach (var duplicateName in duplicateNames)
                {
                    errors.Add($"Duplicate product name found: '{duplicateName}'");
                }
            }

            result.Success = !errors.Any();
            result.Errors = errors;
            result.Message = result.Success ? "Validation passed" : "Validation failed";

            if (result.Success)
            {
                _logger.LogInformation("Product structure validation completed successfully");
                return Result<ProductStructureCreationResult>.Success(result, "Validation completed successfully");
            }
            else
            {
                _logger.LogWarning("Product structure validation failed. Errors: {Errors}",
                    string.Join(", ", result.Errors));

                return Result<ProductStructureCreationResult>.Success(result, "Validation completed with errors");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during product structure validation");

            return Result<ProductStructureCreationResult>.Failure("An unexpected error occurred during validation");
        }
    }

    private void ValidateProductStructure(ProductStructureDto product, List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(product.Name))
        {
            errors.Add("Product name is required");
        }

        // Validate objects if present
        if (product.Objects?.Any() == true)
        {
            ValidateObjectsStructure(product.Objects, $"Product '{product.Name}'", 0, errors);
        }
    }

    private void ValidateObjectsStructure(List<ObjectStructureDto> objects, string parentPath, int depth, List<string> errors)
    {
        if (depth > 10) // Prevent excessive nesting
        {
            errors.Add($"Maximum hierarchy depth (10) exceeded at path '{parentPath}'");
            return;
        }

        var names = new HashSet<string>();
        foreach (var obj in objects)
        {
            if (string.IsNullOrWhiteSpace(obj.Name))
            {
                errors.Add($"Object name is required at path '{parentPath}'");
                continue;
            }

            if (!names.Add(obj.Name.ToLowerInvariant()))
            {
                errors.Add($"Duplicate object name '{obj.Name}' found at path '{parentPath}'");
            }

            // Validate child objects recursively
            if (obj.Objects?.Any() == true)
            {
                var currentPath = $"{parentPath}/{obj.Name}";
                ValidateObjectsStructure(obj.Objects, currentPath, depth + 1, errors);
            }
        }
    }
}
