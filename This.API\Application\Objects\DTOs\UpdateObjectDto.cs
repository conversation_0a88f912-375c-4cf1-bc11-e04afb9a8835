using System.ComponentModel.DataAnnotations;

namespace Application.Objects.DTOs;

/// <summary>
/// Update Object DTO
/// </summary>
public class UpdateObjectDto
{
    /// <summary>
    /// Product ID this object belongs to
    /// </summary>
    [Required]
    public Guid ProductId { get; set; }

    /// <summary>
    /// Parent object ID for hierarchical structure
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Object icon
    /// </summary>
    [MaxLength(255)]
    public string? Icon { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
