import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widget_enums.dart';

/// A customizable time input widget following the 'this_componentName_relatedTo' naming convention
/// This widget handles time input with validation and various time formats
class ThisTimeInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final TimeOfDay? value;
  final ValueChanged<TimeOfDay?> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final TimeFormat timeFormat;
  final bool use24HourFormat;
  final TimeOfDay? minTime;
  final TimeOfDay? maxTime;
  final bool showTimePicker;
  final int? minuteInterval;
  final String? Function(TimeOfDay?)? customValidation;

  const ThisTimeInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.timeFormat = TimeFormat.hhMm,
    this.use24HourFormat = true,
    this.minTime,
    this.maxTime,
    this.showTimePicker = true,
    this.minuteInterval,
    this.customValidation,
  });

  @override
  State<ThisTimeInput> createState() => _ThisTimeInputState();
}

class _ThisTimeInputState extends State<ThisTimeInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: _formatTimeForDisplay(widget.value));
    _focusNode = FocusNode();
    _validateValue(widget.value);
  }

  @override
  void didUpdateWidget(ThisTimeInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = _formatTimeForDisplay(widget.value);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  String _formatTimeForDisplay(TimeOfDay? time) {
    if (time == null) return '';

    switch (widget.timeFormat) {
      case TimeFormat.hhMm:
        if (widget.use24HourFormat) {
          return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
        } else {
          final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
          final period = time.period == DayPeriod.am ? 'AM' : 'PM';
          return '${hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')} $period';
        }
      case TimeFormat.hhMmSs:
        if (widget.use24HourFormat) {
          return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:00';
        } else {
          final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
          final period = time.period == DayPeriod.am ? 'AM' : 'PM';
          return '${hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:00 $period';
        }
    }
  }

  TimeOfDay? _parseTimeFromString(String text) {
    if (text.isEmpty) return null;

    try {
      // Remove any whitespace
      text = text.trim();

      // Handle AM/PM format
      bool isPM = false;
      if (text.toUpperCase().contains('PM')) {
        isPM = true;
        text = text.replaceAll(RegExp(r'\s*PM\s*', caseSensitive: false), '');
      } else if (text.toUpperCase().contains('AM')) {
        text = text.replaceAll(RegExp(r'\s*AM\s*', caseSensitive: false), '');
      }

      // Split by colon
      final parts = text.split(':');
      if (parts.length < 2) return null;

      int hour = int.parse(parts[0]);
      int minute = int.parse(parts[1]);

      // Convert 12-hour to 24-hour format
      if (!widget.use24HourFormat) {
        if (isPM && hour != 12) {
          hour += 12;
        } else if (!isPM && hour == 12) {
          hour = 0;
        }
      }

      // Validate ranges
      if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
        return null;
      }

      return TimeOfDay(hour: hour, minute: minute);
    } catch (e) {
      return null;
    }
  }

  List<String> _validateValue(TimeOfDay? value) {
    final errors = <String>[];

    // Required validation
    if (widget.required && value == null) {
      errors.add('${widget.label} is required');
      return errors;
    }

    if (value != null) {
      // Min time validation
      if (widget.minTime != null && _isTimeBefore(value, widget.minTime!)) {
        errors.add('Time must be after ${_formatTimeForDisplay(widget.minTime!)}');
      }

      // Max time validation
      if (widget.maxTime != null && _isTimeAfter(value, widget.maxTime!)) {
        errors.add('Time must be before ${_formatTimeForDisplay(widget.maxTime!)}');
      }

      // Minute interval validation
      if (widget.minuteInterval != null && value.minute % widget.minuteInterval! != 0) {
        errors.add('Minutes must be in ${widget.minuteInterval}-minute intervals');
      }

      // Custom validation
      if (widget.customValidation != null) {
        final customError = widget.customValidation!(value);
        if (customError != null) {
          errors.add(customError);
        }
      }
    }

    return errors;
  }

  bool _isTimeBefore(TimeOfDay time1, TimeOfDay time2) {
    return time1.hour < time2.hour || (time1.hour == time2.hour && time1.minute < time2.minute);
  }

  bool _isTimeAfter(TimeOfDay time1, TimeOfDay time2) {
    return time1.hour > time2.hour || (time1.hour == time2.hour && time1.minute > time2.minute);
  }

  void _handleChange(String text) {
    final newValue = _parseTimeFromString(text);
    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  Future<void> _showTimePickerDialog() async {
    if (widget.disabled || widget.readOnly) return;

    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: widget.value ?? TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              backgroundColor: ColorPalette.darkToneInk,
              hourMinuteTextColor: ColorPalette.white,
              dayPeriodTextColor: ColorPalette.white,
              dialHandColor: ColorPalette.white,
              dialTextColor: ColorPalette.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      _controller.text = _formatTimeForDisplay(picked);
      widget.onChanged(picked);

      // Validate the picked time
      final errors = _validateValue(picked);
      setState(() {
        _errors = errors;
      });

      // Notify parent of validation state
      widget.onValidation?.call(errors);
    }
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.white,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Input Field
        TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          enabled: !widget.disabled,
          readOnly: widget.readOnly || widget.showTimePicker,
          keyboardType: widget.showTimePicker ? TextInputType.none : TextInputType.datetime,
          inputFormatters: widget.showTimePicker
              ? []
              : [
                  _TimeInputFormatter(widget.use24HourFormat),
                ],
          onChanged: widget.showTimePicker ? null : _handleChange,
          onTap: widget.showTimePicker ? _showTimePickerDialog : null,
          decoration: InputDecoration(
            hintText: widget.placeholder ?? (widget.use24HourFormat ? 'HH:MM' : 'HH:MM AM/PM'),
            hintStyle: LexendTextStyles.lexend14Regular.copyWith(
              color: ColorPalette.placeHolderTextColor,
            ),
            errorText: hasErrors ? _errors.first : null,
            errorStyle: LexendTextStyles.lexend12Regular.copyWith(
              color: const Color(0xFFC73E1D),
            ),
            suffixIcon: IconButton(
              icon: const Icon(Icons.access_time, size: 20),
              onPressed: widget.showTimePicker ? _showTimePickerDialog : null,
            ),
          ),
          style: LexendTextStyles.lexend14Regular.copyWith(
            color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.white,
          ),
        ),

        // Helper text for time format
        if (!hasErrors)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Format: ${widget.use24HourFormat ? '24-hour' : '12-hour'} (${widget.use24HourFormat ? 'HH:MM' : 'HH:MM AM/PM'})',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          ),
      ],
    );
  }
}

/// Custom input formatter for time values
class _TimeInputFormatter extends TextInputFormatter {
  final bool use24HourFormat;

  _TimeInputFormatter(this.use24HourFormat);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // Allow deletion
    if (text.length < oldValue.text.length) {
      return newValue;
    }

    // Auto-format time input
    String formatted = text.replaceAll(RegExp(r'[^\d]'), '');

    if (formatted.length >= 2) {
      formatted = '${formatted.substring(0, 2)}:${formatted.substring(2)}';
    }

    if (formatted.length > 5) {
      formatted = formatted.substring(0, 5);
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
