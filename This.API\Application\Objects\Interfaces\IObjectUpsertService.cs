using Application.Objects.DTOs;
using Shared.Common.Response;

namespace Application.Objects.Interfaces;

/// <summary>
/// Service for upserting objects with metadata and values
/// </summary>
public interface IObjectUpsertService
{
    /// <summary>
    /// Upsert an object with metadata and values
    /// </summary>
    /// <param name="objectData">Object data to create or update</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="userId">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Upsert result with object information and statistics</returns>
    Task<Result<UpsertObjectWithMetadataResponseDto>> UpsertObjectWithMetadataAsync(
        ObjectUpsertDto objectData,
        string tenantId,
        string userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate that an object exists and is accessible
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    Task<Result<bool>> ValidateObjectAccessAsync(
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get object information by ID
    /// </summary>
    /// <param name="objectId">Object ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Object information</returns>
    Task<Result<ObjectUpsertResultDto>> GetObjectInfoAsync(
        Guid objectId,
        string tenantId,
        CancellationToken cancellationToken = default);
}
