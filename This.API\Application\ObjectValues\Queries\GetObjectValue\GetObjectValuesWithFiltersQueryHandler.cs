using Abstraction.Common;
using Abstraction.Database.Repositories;
using Application.ObjectMetadataManagement.DTOs;
using Application.ObjectValues.DTOs;
using Application.ObjectValues.Specifications;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.ObjectValues.Queries.GetObjectValue;

/// <summary>
/// Handler for GetObjectValuesWithFiltersQuery
/// </summary>
public class GetObjectValuesWithFiltersQueryHandler : IRequestHandler<GetObjectValuesWithFiltersQuery, PaginatedResult<ObjectValueResponseDto>>
{
    private readonly IRepository<ObjectValue> _repository;
    private readonly ICurrentUser _currentUser;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectValuesWithFiltersQueryHandler(
        IRepository<ObjectValue> repository,
        ICurrentUser currentUser)
    {
        _repository = repository;
        _currentUser = currentUser;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PaginatedResult<ObjectValueResponseDto>> Handle(GetObjectValuesWithFiltersQuery request, CancellationToken cancellationToken)
    {
        // Calculate pagination
        var skip = (request.PageNumber - 1) * request.PageSize;

        // Get count for pagination
        var countSpec = new ObjectValueCountSpec(
            productId: request.FeatureId,
            objectId: request.ObjectId,
            metadataId: request.MetadataId,
            refId: request.RefId,
            searchTerm: request.SearchTerm,
            onlyVisibleFields: request.OnlyVisibleFields);

        var totalCount = await _repository.CountAsync(countSpec, cancellationToken);

        // Get data with pagination
        var dataSpec = new ObjectValueWithFullDataSpec(
            productId: request.FeatureId,
            objectId: request.ObjectId,
            metadataId: request.MetadataId,
            refId: request.RefId,
            searchTerm: request.SearchTerm,
            onlyVisibleFields: request.OnlyVisibleFields,
            onlyParentValues: request.OnlyParentValues,
            orderBy: request.OrderBy,
            skip: skip,
            take: request.PageSize);

        var objectValues = await _repository.ListAsync(dataSpec, cancellationToken);

        // Optimized LINQ operations - no foreach loops
        var result = await Task.WhenAll(objectValues.Select(async objectValue =>
        {
            var dto = MapToResponseDto(objectValue);

            if (request.IncludeChildValues && !request.OnlyParentValues)
            {
                dto.ChildValues = await GetChildValuesRecursive(objectValue.Id, cancellationToken);
            }

            return dto;
        }));

        return new PaginatedResult<ObjectValueResponseDto>(
            result.ToList(),
            totalCount,
            request.PageNumber,
            request.PageSize);
    }

    /// <summary>
    /// Get child values recursively using optimized LINQ operations
    /// </summary>
    private async Task<List<ObjectValueResponseDto>> GetChildValuesRecursive(Guid parentValueId, CancellationToken cancellationToken)
    {
        var childSpec = new ObjectValueChildrenSpec(parentValueId);
        var childValues = await _repository.ListAsync(childSpec, cancellationToken);

        // Optimized LINQ operations - no foreach loops
        var result = await Task.WhenAll(childValues.Select(async childValue =>
        {
            var dto = MapToResponseDto(childValue);
            dto.ChildValues = await GetChildValuesRecursive(childValue.Id, cancellationToken);
            return dto;
        }));

        return result.ToList();
    }

    /// <summary>
    /// Map ObjectValue entity to ObjectValueResponseDto
    /// </summary>
    private ObjectValueResponseDto MapToResponseDto(ObjectValue objectValue)
    {
        return new ObjectValueResponseDto
        {
            Id = objectValue.Id,
            RefId = objectValue.RefId,
            ParentObjectValueId = objectValue.ParentObjectValueId,
            Value = objectValue.Value,
            CreatedAt = objectValue.CreatedAt,
            ModifiedAt = objectValue.ModifiedAt,
            ObjectMetadata = new ObjectMetadataDto
            {
                Id = objectValue.ObjectMetadata.Id,
                ObjectId = objectValue.ObjectMetadata.ObjectId,
                ObjectName = objectValue.ObjectMetadata.Object.Name,
                MetadataId = objectValue.ObjectMetadata.MetadataId,
                MetadataKey = objectValue.ObjectMetadata.Metadata.Name,
                MetadataDisplayLabel = objectValue.ObjectMetadata.Metadata.DisplayLabel,
                IsUnique = objectValue.ObjectMetadata.IsUnique,
                IsActive = objectValue.ObjectMetadata.IsActive,
                ValuesCount = 0,
                CreatedAt = objectValue.ObjectMetadata.CreatedAt,
                CreatedBy = objectValue.ObjectMetadata.CreatedBy ?? Guid.Empty,
                ModifiedAt = objectValue.ObjectMetadata.ModifiedAt,
                ModifiedBy = objectValue.ObjectMetadata.ModifiedBy
            }
        };
    }
}
