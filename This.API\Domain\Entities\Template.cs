using Domain.Common.Contracts;
using System.Text.Json;

namespace Domain.Entities;

/// <summary>
/// Template entity - stores template versions for products
/// </summary>
public class Template : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Product ID this template belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Template version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Template stage (draft, live, beta, etc.)
    /// </summary>
    public string Stage { get; set; } = string.Empty;

    /// <summary>
    /// Template JSON content
    /// </summary>
    public JsonElement TemplateJson { get; set; }

    /// <summary>
    /// When the template was published
    /// </summary>
    public DateTime? PublishedAt { get; set; }
   
}
