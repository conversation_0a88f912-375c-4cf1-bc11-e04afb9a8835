using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for TenantLookup entity
/// </summary>
public class TenantLookupConfig : IEntityTypeConfiguration<TenantLookup>
{
    public void Configure(EntityTypeBuilder<TenantLookup> builder)
    {
        builder.ToTable("TenantLookups", "Genp");

        // Multi-tenant configuration
        builder.IsMultiTenant();

        // Properties
        builder.Property(e => e.Value)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.IsDefault)
            .HasDefaultValue(false);

        builder.Property(e => e.Value1)
            .HasMaxLength(255);

        builder.Property(e => e.Value2)
            .HasMaxLength(255);

        builder.Property(e => e.ShowSequence)
            .HasDefaultValue(0);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(e => e.TenantContextId)
            .HasDatabaseName("IX_TenantLookups_TenantContextId");

        builder.HasIndex(e => e.Value)
            .HasDatabaseName("IX_TenantLookups_Value");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_TenantLookups_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Relationships
        builder.HasOne(e => e.TenantContext)
            .WithMany(e => e.TenantLookups)
            .HasForeignKey(e => e.TenantContextId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
