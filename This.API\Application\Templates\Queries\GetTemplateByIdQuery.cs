using Application.Templates.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Templates.Queries;

/// <summary>
/// Query to get a template by ID
/// </summary>
public class GetTemplateByIdQuery : IRequest<Result<TemplateDto>>
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetTemplateByIdQuery(Guid id)
    {
        Id = id;
    }
}
