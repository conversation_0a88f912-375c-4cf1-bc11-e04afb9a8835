using Ardalis.Specification;
using Domain.Entities;

namespace Application.ObjectValues.Specifications;

/// <summary>
/// Optimized specification for RefId list queries with high performance
/// </summary>
public class OptimizedRefIdListSpec : Specification<ObjectValue>
{
    /// <summary>
    /// Constructor for optimized RefId list retrieval
    /// </summary>
    public OptimizedRefIdListSpec(
        Guid objectId,
        string tenantId,
        Guid? refId = null,
        string? searchTerm = null,
        bool onlyActive = true)
    {
        // Base filters with optimized indexing
        Query.Where(ov => ov.ObjectMetadata.ObjectId == objectId && 
                         !ov.IsDeleted &&
                         ov.RefId != null);

        // Optimized includes for single query execution
        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Object);

        Query.Include(ov => ov.ObjectMetadata)
             .ThenInclude(om => om.Metadata);

        // Active filters with proper indexing
        Query.Where(ov => !ov.ObjectMetadata.Object.IsDeleted &&
                         ov.ObjectMetadata.IsActive &&
                         !ov.ObjectMetadata.IsDeleted);

        // RefId filter if specified
        if (refId.HasValue)
        {
            Query.Where(ov => ov.RefId == refId.Value);
        }

        // Active object filter
        if (onlyActive)
        {
            Query.Where(ov => ov.ObjectMetadata.Object.IsActive);
        }

        // Search filter with optimized pattern
        if (!string.IsNullOrEmpty(searchTerm))
        {
            var searchLower = searchTerm.ToLower();
            Query.Where(ov => (ov.Value != null && ov.Value.ToLower().Contains(searchLower)) ||
                             ov.ObjectMetadata.Metadata.Name.ToLower().Contains(searchLower) || // Updated: Name is now Name property
                             (ov.ObjectMetadata.Metadata.DisplayLabel != null &&
                              ov.ObjectMetadata.Metadata.DisplayLabel.ToLower().Contains(searchLower)));
        }

        // Optimized ordering for grouping operations
        Query.OrderBy(ov => ov.RefId)
             .ThenBy(ov => ov.ObjectMetadata.Metadata.FieldOrder ?? int.MaxValue)
             .ThenBy(ov => ov.ObjectMetadata.Metadata.Name); // Updated: Name is now Name property

        // Use AsNoTracking for read-only operations
        Query.AsNoTracking();
    }
}
