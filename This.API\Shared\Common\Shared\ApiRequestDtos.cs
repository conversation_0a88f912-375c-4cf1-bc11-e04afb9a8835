namespace Shared;

/// <summary>
/// Shared request DTOs for API controllers
/// </summary>
public class SingleValueUpsertRequest
{
    public Guid Id { get; set; }
    public string PropertyName { get; set; } = string.Empty;
    public object? PropertyValue { get; set; }
}

/// <summary>
/// Request DTO for updating a single value field
/// </summary>
public class SingleValueUpdateRequest
{
    public Guid Id { get; set; }
    public string Value { get; set; } = string.Empty;
}

/// <summary>
/// Request DTO for updating a single value field by RefId and Metadata<PERSON>ey
/// </summary>
public class SingleValueUpdateByRefIdRequest
{
    /// <summary>
    /// RefId to identify the instance
    /// </summary>
    public Guid RefId { get; set; }

    /// <summary>
    /// MetadataKey to identify the metadata field
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// New value to update
    /// </summary>
    public string Value { get; set; } = string.Empty;
}

/// <summary>
/// Generic bulk upsert request
/// </summary>
public class UpsertBulkRequest
{
    public List<Dictionary<string, object?>> DataList { get; set; } = new();
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Tenant-specific bulk upsert request
/// </summary>
public class TenantUpsertBulkRequest
{
    public List<Dictionary<string, object?>> TenantDataList { get; set; } = new();
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Subscription-specific bulk upsert request
/// </summary>
public class SubscriptionUpsertBulkRequest
{
    public List<Dictionary<string, object?>> SubscriptionDataList { get; set; } = new();
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Metadata-specific bulk upsert request
/// </summary>
public class MetadataUpsertBulkRequest
{
    public List<Dictionary<string, object?>> MetadataDataList { get; set; } = new();
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// DataType-specific bulk upsert request
/// </summary>
public class DataTypeUpsertBulkRequest
{
    public List<Dictionary<string, object?>> DataTypeDataList { get; set; } = new();
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Product-specific request DTOs
/// </summary>
public class ProductUpsertRequest
{
    public Dictionary<string, object?> ProductData { get; set; } = new();
    public bool IncludeFeatures { get; set; } = false;
    public bool IncludeObjects { get; set; } = false;
    public bool IncludeMetadata { get; set; } = true;
}

public class ProductUpsertBulkRequest
{
    public List<Dictionary<string, object?>> ProductDataList { get; set; } = new();
    public bool IncludeFeatures { get; set; } = false;
    public bool IncludeObjects { get; set; } = false;
    public bool IncludeMetadata { get; set; } = true;
    public int BatchSize { get; set; } = 1000;
}

public class ProductValueUpsertRequest
{
    public List<Dictionary<string, object?>> ValueData { get; set; } = new();
    public Guid? RefId { get; set; }
    public bool ValidateMetadata { get; set; } = true;
}

/// <summary>
/// Feature-specific request DTOs
/// </summary>
public class FeatureUpsertRequest
{
    public Dictionary<string, object?> FeatureData { get; set; } = new();
    public bool IncludeObjects { get; set; } = false;
    public bool IncludeMetadata { get; set; } = true;
}

public class FeatureUpsertBulkRequest
{
    public List<Dictionary<string, object?>> FeatureDataList { get; set; } = new();
    public bool IncludeObjects { get; set; } = false;
    public bool IncludeMetadata { get; set; } = true;
    public int BatchSize { get; set; } = 1000;
}

public class FeatureValueUpsertRequest
{
    public List<Dictionary<string, object?>> ValueData { get; set; } = new();
    public Guid? RefId { get; set; }
    public bool ValidateMetadata { get; set; } = true;
}

/// <summary>
/// Object-specific request DTOs
/// </summary>
public class ObjectUpsertRequest
{
    public Dictionary<string, object?> ObjectData { get; set; } = new();
    public bool IncludeMetadata { get; set; } = true;
    public bool IncludeValues { get; set; } = true;
}

public class ObjectUpsertBulkRequest
{
    public List<Dictionary<string, object?>> ObjectDataList { get; set; } = new();
    public bool IncludeMetadata { get; set; } = true;
    public bool IncludeValues { get; set; } = true;
    public int BatchSize { get; set; } = 1000;
}

public class ObjectValueUpsertRequest
{
    public List<Dictionary<string, object?>> ValueData { get; set; } = new();
    public Guid? RefId { get; set; }
    public bool ValidateMetadata { get; set; } = true;
}

/// <summary>
/// Request DTO for upserting objects with metadata in flat structure
/// All properties including ObjectId, RefId, ParentObjectValueId and metadata are in MetadataProperties
/// </summary>
public class ObjectUpsertWithMetadataRequest
{
    /// <summary>
    /// All properties including ObjectId, RefId, ParentObjectValueId and metadata properties
    /// ObjectId: Required - identifies the target object
    /// RefId: Required - groups all metadata values for this object instance
    /// ParentObjectValueId: Optional - for hierarchical relationships
    /// Other properties: Metadata where key is DisplayLabel and value is the metadata value
    /// </summary>
    public Dictionary<string, object?> MetadataProperties { get; set; } = new();
}

/// <summary>
/// Request DTO for bulk upserting objects with metadata in flat structure
/// </summary>
public class ObjectUpsertWithMetadataBulkRequest
{
    /// <summary>
    /// List of object instances to upsert
    /// </summary>
    public List<ObjectUpsertWithMetadataRequest> Objects { get; set; } = new();

    /// <summary>
    /// Batch size for processing
    /// </summary>
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Role-specific request DTOs
/// </summary>
public class RoleUpsertRequest
{
    public Dictionary<string, object?> RoleData { get; set; } = new();
    public bool IncludeMetadata { get; set; } = true;
    public bool IncludeValues { get; set; } = true;
}

public class RoleUpsertBulkRequest
{
    public List<Dictionary<string, object?>> RoleDataList { get; set; } = new();
    public bool IncludeMetadata { get; set; } = true;
    public bool IncludeValues { get; set; } = true;
    public int BatchSize { get; set; } = 1000;
}

public class RoleValueUpsertRequest
{
    public List<Dictionary<string, object?>> ValueData { get; set; } = new();
    public Guid? RefId { get; set; }
    public bool ValidateMetadata { get; set; } = true;
}

/// <summary>
/// User-specific request DTOs
/// </summary>
public class UserUpsertRequest
{
    public Dictionary<string, object?> UserData { get; set; } = new();
    public bool IncludeMetadata { get; set; } = true;
    public bool IncludeValues { get; set; } = true;
}

public class UserUpsertBulkRequest
{
    public List<Dictionary<string, object?>> UserDataList { get; set; } = new();
    public bool IncludeMetadata { get; set; } = true;
    public bool IncludeValues { get; set; } = true;
    public int BatchSize { get; set; } = 1000;
}

public class UserValueUpsertRequest
{
    public List<Dictionary<string, object?>> ValueData { get; set; } = new();
    public Guid? RefId { get; set; }
    public bool ValidateMetadata { get; set; } = true;
}

/// <summary>
/// Values-specific bulk upsert request DTOs
/// </summary>
public class ProductValuesUpsertBulkRequest
{
    public List<Dictionary<string, object?>> ValueDataList { get; set; } = new();
    public Guid? RefId { get; set; }
    public bool ValidateMetadata { get; set; } = true;
    public int BatchSize { get; set; } = 1000;
}

public class FeatureValuesUpsertBulkRequest
{
    public List<Dictionary<string, object?>> ValueDataList { get; set; } = new();
    public Guid? RefId { get; set; }
    public bool ValidateMetadata { get; set; } = true;
    public int BatchSize { get; set; } = 1000;
}

public class ObjectValuesUpsertBulkRequest
{
    public List<Dictionary<string, object?>> ValueDataList { get; set; } = new();
    public Guid? RefId { get; set; }
    public bool ValidateMetadata { get; set; } = true;
    public int BatchSize { get; set; } = 1000;
}
