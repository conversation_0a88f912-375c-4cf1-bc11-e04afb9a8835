using Domain.Common.Contracts;

namespace Domain.Entities;

/// <summary>
/// Global data type definitions with comprehensive validation and UI properties
/// </summary>
public class DataType : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Name of the data type (e.g., 'text', 'number', 'date')
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Display name for UI
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Category of the data type ('primitive', 'formatted', 'choice', 'media', 'temporal', 'complex', 'interactive')
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// UI component to use for rendering
    /// </summary>
    public string UiComponent { get; set; } = string.Empty;

    // Validation Rules
    /// <summary>
    /// Validation pattern (regex)
    /// </summary>
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// Minimum length for text fields
    /// </summary>
    public int? MinLength { get; set; }

    /// <summary>
    /// Maximum length for text fields
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Minimum value for numeric fields
    /// </summary>
    public decimal? MinValue { get; set; }

    /// <summary>
    /// Maximum value for numeric fields
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// Number of decimal places for numeric fields
    /// </summary>
    public int? DecimalPlaces { get; set; }

    /// <summary>
    /// Step value for numeric inputs
    /// </summary>
    public decimal? StepValue { get; set; }

    /// <summary>
    /// Whether the field is required by default
    /// </summary>
    public bool IsRequired { get; set; } = false;

    // UI Properties
    /// <summary>
    /// HTML input type
    /// </summary>
    public string? InputType { get; set; }

    // UI Properties
    /// <summary>
    /// HTML input mask
    /// </summary>
    public string? InputMask { get; set; }

    /// <summary>
    /// Placeholder text
    /// </summary>
    public string? Placeholder { get; set; }

    /// <summary>
    /// Additional HTML attributes as JSON
    /// </summary>
    public string? HtmlAttributes { get; set; }

    // Choice Options
    /// <summary>
    /// Default options for choice fields (JSON or comma-separated)
    /// </summary>
    public string? DefaultOptions { get; set; }

    /// <summary>
    /// Whether multiple selections are allowed
    /// </summary>
    public bool AllowsMultiple { get; set; } = false;

    /// <summary>
    /// Whether custom options can be added
    /// </summary>
    public bool AllowsCustomOptions { get; set; } = false;

    /// <summary>
    /// Maximum number of selections for multi-select fields
    /// </summary>
    public int? MaxSelections { get; set; }

    // File/Media Properties
    /// <summary>
    /// Allowed file types (comma-separated)
    /// </summary>
    public string? AllowedFileTypes { get; set; }

    /// <summary>
    /// Maximum file size in bytes
    /// </summary>
    public long? MaxFileSizeBytes { get; set; }

    // Error Messages
    /// <summary>
    /// Error message for required field validation
    /// </summary>
    public string? RequiredErrorMessage { get; set; }

    /// <summary>
    /// Error message for pattern validation
    /// </summary>
    public string? PatternErrorMessage { get; set; }

    /// <summary>
    /// Error message for minimum length validation
    /// </summary>
    public string? MinLengthErrorMessage { get; set; }

    /// <summary>
    /// Error message for maximum length validation
    /// </summary>
    public string? MaxLengthErrorMessage { get; set; }

    /// <summary>
    /// Error message for minimum value validation
    /// </summary>
    public string? MinValueErrorMessage { get; set; }

    /// <summary>
    /// Error message for maximum value validation
    /// </summary>
    public string? MaxValueErrorMessage { get; set; }

    /// <summary>
    /// Error message for file type validation
    /// </summary>
    public string? FileTypeErrorMessage { get; set; }

    /// <summary>
    /// Error message for file size validation
    /// </summary>
    public string? FileSizeErrorMessage { get; set; }

    /// <summary>
    /// General error message
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Label to be displayed for the field
    /// </summary>
    public string? DisplayLabel {  get; set; }

    /// <summary>
    /// Additional help or guidance text for the field
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// The order in which the field should be displayed
    /// </summary>
    public string? FieldOrder { get; set; }

    /// <summary>
    /// Indicates whether the field is visible
    /// </summary>
    public bool? IsVisible { get; set; }

    /// <summary>
    /// Indicates whether the field is read-only
    /// </summary>
    public bool? IsReadonly { get; set; }

    // Lookup Configuration Properties
    /// <summary>
    /// Type of lookup (e.g., "master", "tenant", "object")
    /// </summary>
    public string? LookupType { get; set; }

    public string? OverrideLookupType { get; set; }
    public Guid? OverrideMasterContextId { get; set; }
    public Guid? OverrideObjectLookupId { get; set; }
    public Guid? OverrideTenantContextId { get; set; }

    /// <summary>
    /// Foreign key to master Context for global lookups
    /// </summary>
    public Guid? MasterContextId { get; set; }

    /// <summary>
    /// Foreign key to TenantContext for tenant-specific lookups
    /// </summary>
    public Guid? TenantContextId { get; set; }

    /// <summary>
    /// Foreign key to ObjectLookup for object-based lookups
    /// </summary>
    public Guid? ObjectLookupId { get; set; }

    // Navigation Properties
    /// <summary>
    /// Metadata definitions using this data type
    /// </summary>
    public virtual ICollection<Metadata> Metadata { get; set; } = new List<Metadata>();

    /// <summary>
    /// Navigation property to master Context (for global lookups)
    /// </summary>
    public virtual Context? MasterContext { get; set; }

    /// <summary>
    /// Navigation property to TenantContext (for tenant-specific lookups)
    /// </summary>
    public virtual TenantContext? TenantContext { get; set; }

    /// <summary>
    /// Navigation property to ObjectLookup (for object-based lookups)
    /// </summary>
    public virtual ObjectLookup? ObjectLookup { get; set; }

    public virtual Context? OverrideMasterContext { get; set; }

    /// <summary>
    /// Navigation property to TenantContext (for tenant-specific lookups)
    /// </summary>
    public virtual TenantContext? OverrideTenantContext { get; set; }

    /// <summary>
    /// Navigation property to ObjectLookup (for object-based lookups)
    /// </summary>
    public virtual ObjectLookup? OverrideObjectLookup { get; set; }
}
