using Abstraction.Database.Repositories;
using Application.Templates.DTOs;
using Application.Templates.Specifications;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Templates.Commands;

/// <summary>
/// Handler for CreateTemplateCommand
/// </summary>
public class CreateTemplateCommandHandler : IRequestHandler<CreateTemplateCommand, Result<TemplateDto>>
{
    private readonly IRepository<Template> _templateRepository;
    private readonly ILogger<CreateTemplateCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateTemplateCommandHandler(
        IRepository<Template> templateRepository,
        ILogger<CreateTemplateCommandHandler> logger)
    {
        _templateRepository = templateRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<TemplateDto>> Handle(CreateTemplateCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating template for product {ProductId}, version {Version}, stage {Stage}",
                request.ProductId, request.Version, request.Stage);

            // Check for duplicate template using specification
            var duplicateSpec = new TemplateDuplicateSpec(request.ProductId, request.Version, request.Stage);
            var existingTemplate = await _templateRepository.GetBySpecAsync(duplicateSpec, cancellationToken);

            if (existingTemplate != null)
            {
                _logger.LogWarning("Template already exists for product {ProductId}, version {Version}, stage {Stage}",
                    request.ProductId, request.Version, request.Stage);
                return Result<TemplateDto>.Failure("Template with this version and stage already exists for this product");
            }

            // Create new template
            var template = new Template
            {
                Id = Guid.NewGuid(),
                ProductId = request.ProductId,
                Version = request.Version,
                Stage = request.Stage,
                TemplateJson = request.TemplateJson,
                IsActive = request.IsActive,
                CreatedAt = DateTime.UtcNow,
                ModifiedAt = DateTime.UtcNow
            };

            await _templateRepository.AddAsync(template, cancellationToken);

            // Create response DTO
            var createdTemplate = new TemplateDto
            {
                Id = template.Id,
                ProductId = template.ProductId,
                ProductName = "Unknown", // Since there's no relationship with Product table
                Version = template.Version,
                Stage = template.Stage,
                TemplateJson = template.TemplateJson,
                CreatedAt = template.CreatedAt,
                CreatedBy = template.CreatedBy,
                PublishedAt = template.PublishedAt,
                IsActive = template.IsActive,
                IsDeleted = template.IsDeleted
            };

            _logger.LogInformation("Successfully created template {TemplateId}", template.Id);
            return Result<TemplateDto>.Success(createdTemplate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating template");
            return Result<TemplateDto>.Failure("Failed to create template");
        }
    }
}
